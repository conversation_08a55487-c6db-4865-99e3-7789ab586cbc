Title: Studies - Pandas TA

URL Source: https://www.pandas-ta.dev/api/studies

Markdown Content:
A _Study_ is a [Python DataClass](https://docs.python.org/3/library/dataclasses.html) and is used to group indicators by _name_, _description_, _cores_, and _ta_. Studies simplify both custom and bulk (multi)processing.

* * *

### The **Study** DataClass[#](http://www.pandas-ta.dev/api/studies/#the-study-dataclass "Permanent link")

Study DataClass Class to name and group indicators for processing.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `name` | `str` | Name. | _required_ |
| `ta` | `list of dicts` | i.e [{"kind": "ema", "length", 50}] | `list()` |
| `cores` | `int` | The number cores to use for multiprocessing. Default: `cpu_count()` | `cpu_count()` |
| `description` | `str` | Description of what the Study. Default: `""` | `''` |
| `created` | `str` | DateTime String at creation. Default: Automatically generated. | `get_time(to_string=True)` |

Returns:

| Type | Description |
| --- | --- |
| `DataClass` | The Study to be processed by `df.ta.study()` |

All or Common Study
Run

```
# Run "All"
df.ta.study(ta.AllStudy, **kwargs)

# Run "Common"
df.ta.study(ta.CommonStudy, **kwargs)
```

Custom Study
Create

```
DemoStudy = ta.Study(
    name="Demo Study",
    description="Example Study Group",
    cores=0,  # Usually faster than multiprocessing
    ta = [
        {"kind": "sma", "length": 200},
        {"kind": "sma", "close": "volume", "length": 50},
        {"kind": "bbands", "length": 20},
        {"kind": "rsi"},
        {"kind": "macd", "fast": 8, "slow": 21},
        {"kind": "sma", "close": "volume", "length": 20, "prefix": "VOLUME"}
    ]
```

Run

```
df.ta.study(DemoStudy, **kwargs)
```

Note
*   See [also](http://www.pandas-ta.dev/getting-started/usage/) the Pandas TA "Study" Examples
*   Case-insensitive "All" is reserved.

Multiprocessing
**Not recommended** for:

*   Small sets of indicators
*   Indicator chains

IMPORTANT

**Thanks** to all those that have sponsored and dontated to the library in the past! Your support has been greatly appreciated! ![Image 1: 🙏](http://www.pandas-ta.dev/assets/external/cdn.jsdelivr.net/gh/jdecked/twemoji@15.1.0/assets/svg/1f64f.svg)

Only **Installation Bugs/Issues** will addressed for releases between versions _0.4.25b_ and _0.4.66b_. Releases beyond version _0.4.66b_ will **only** be released after **significant** donations, sponsorships or yearly subscriptions have been received via [Buy Me a Coffee](https://www.buymeacoffee.com/twopirllc).

Support Tiers coming soon!

* * *

### Builtin Studies[#](http://www.pandas-ta.dev/api/studies/#builtin-studies "Permanent link")

Pandas TA has two builtin studies: `"All"` and `"Common"`.

#### "ALL"[#](http://www.pandas-ta.dev/api/studies/#all "Permanent link")

```
import pandas_ta as ta

print(ta.AllStudy)
```

* * *

#### "Common"[#](http://www.pandas-ta.dev/api/studies/#common "Permanent link")

```
import pandas_ta as ta

print(ta.CommonStudy)
```

[![Image 2: "Buy Me A Coffee"](http://www.pandas-ta.dev/assets/external/www.buymeacoffee.com/assets/img/custom_images/orange_img.png)](https://www.buymeacoffee.com/twopirllc)

[![Image 3: ko-fi](http://www.pandas-ta.dev/assets/external/ko-fi.com/img/githubbutton_sm.svg)](https://ko-fi.com/K3K4ZRH9D)