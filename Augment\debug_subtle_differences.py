"""
Debug Subtle Differences

This script traces every single calculation step for both standalone and smart versions
to identify the exact differences causing signal mismatches.
"""

import sys
import os
import logging
from datetime import datetime
import warnings
import numpy as np
import math
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_minute_calculations(minute):
    """Debug all calculations for a specific minute"""
    print(f"\n" + "="*120)
    print(f"🔍 DEBUGGING MINUTE {minute} - STEP BY STEP CALCULATIONS")
    print("="*120)
    
    from shared_api_manager import get_api
    from enhanced_nadarya_watson_signal import get_start_end_timestamps, live_data
    from smart_vectorized_backtester import SmartVectorizedBacktester
    
    # Test parameters
    ticker = 'BATAINDIA'
    date = '24-06-2025'
    start_time = '09:15'
    exchange = 'NSE'
    tokenid = '371'
    
    api = get_api()
    
    print(f"\n1️⃣ STANDALONE VERSION CALCULATIONS")
    print("-" * 80)
    
    # Get standalone data
    start_timestamp, end_timestamp = get_start_end_timestamps(date, start_time, minute)
    standalone_data = api.get_time_price_series(
        exchange='NSE', 
        token=tokenid, 
        starttime=start_timestamp, 
        endtime=end_timestamp, 
        interval=1
    )
    standalone_df = live_data(standalone_data)
    standalone_df = standalone_df.sort_values(by='time')
    standalone_close = standalone_df['Close'].values
    
    print(f"📊 Standalone data: {len(standalone_close)} candles")
    print(f"📊 Data range: {standalone_df.index[0]} to {standalone_df.index[-1]}")
    print(f"📊 Last 5 closes: {standalone_close[-5:]}")
    
    # Standalone Nadarya Watson calculation
    print(f"\n🧮 STANDALONE NADARYA WATSON CALCULATION:")
    h = 8
    k = 1.75
    src = standalone_close
    y_standalone = []
    
    sum_e = 0
    for i in range(len(standalone_close)):
        sum_val = 0
        sumw = 0
        for j in range(len(standalone_close)):
            w = math.exp(-(math.pow(i-j, 2)/(h*h*2)))
            sum_val += src[j] * w
            sumw += w
        y2 = sum_val / sumw
        sum_e += abs(src[i] - y2)
        y_standalone.append(y2)
    
    mae_standalone = sum_e / len(standalone_close) * k
    
    # Calculate bands and signals
    upper_band_standalone = []
    lower_band_standalone = []
    upper_signal_standalone = []
    lower_signal_standalone = []
    
    for i in range(len(standalone_close)):
        upper_band_standalone.append(y_standalone[i] + mae_standalone * k)
        lower_band_standalone.append(y_standalone[i] - mae_standalone * k)
        
        if standalone_close[i] > upper_band_standalone[i]:
            upper_signal_standalone.append(standalone_close[i])
        else:
            upper_signal_standalone.append(np.nan)
            
        if standalone_close[i] < lower_band_standalone[i]:
            lower_signal_standalone.append(standalone_close[i])
        else:
            lower_signal_standalone.append(np.nan)
    
    print(f"   MAE: {mae_standalone:.6f}")
    print(f"   Last y: {y_standalone[-1]:.6f}")
    print(f"   Last upper band: {upper_band_standalone[-1]:.6f}")
    print(f"   Last lower band: {lower_band_standalone[-1]:.6f}")
    print(f"   Last close: {standalone_close[-1]:.6f}")
    
    # Check current minute signals
    current_upper_standalone = not np.isnan(upper_signal_standalone[-1])
    current_lower_standalone = not np.isnan(lower_signal_standalone[-1])
    print(f"   Current upper signal: {current_upper_standalone}")
    print(f"   Current lower signal: {current_lower_standalone}")
    
    # Check last 2 minutes
    if len(upper_signal_standalone) >= 2:
        upper_recent_standalone = any(not np.isnan(x) for x in upper_signal_standalone[-2:])
        lower_recent_standalone = any(not np.isnan(x) for x in lower_signal_standalone[-2:])
        print(f"   Last 2 min upper: {upper_recent_standalone}")
        print(f"   Last 2 min lower: {lower_recent_standalone}")
        
        # Final band presence
        upper_present_standalone = current_upper_standalone or upper_recent_standalone
        lower_present_standalone = current_lower_standalone or lower_recent_standalone
        print(f"   Final upper present: {upper_present_standalone}")
        print(f"   Final lower present: {lower_present_standalone}")
    
    # Momentum validation for standalone
    print(f"\n🚀 STANDALONE MOMENTUM VALIDATION:")
    if len(standalone_close) >= 3:
        recent_closes = standalone_close[-3:]  # Last 3 for momentum check
        print(f"   Momentum closes: {recent_closes}")
        
        # Check upper momentum
        if current_upper_standalone or (len(upper_signal_standalone) >= 2 and any(not np.isnan(x) for x in upper_signal_standalone[-2:])):
            strong_moves = 0
            for i in range(1, len(recent_closes)):
                if recent_closes[i] > recent_closes[i-1]:
                    strong_moves += 1
                    print(f"   Upper move {i}: {recent_closes[i]:.2f} > {recent_closes[i-1]:.2f} (Strong UP)")
                else:
                    print(f"   Upper move {i}: {recent_closes[i]:.2f} <= {recent_closes[i-1]:.2f} (Weak)")
            upper_momentum_standalone = strong_moves >= 2
            print(f"   Upper momentum: {strong_moves}/2 = {upper_momentum_standalone}")
        
        # Check lower momentum  
        if current_lower_standalone or (len(lower_signal_standalone) >= 2 and any(not np.isnan(x) for x in lower_signal_standalone[-2:])):
            strong_moves = 0
            for i in range(1, len(recent_closes)):
                if recent_closes[i] < recent_closes[i-1]:
                    strong_moves += 1
                    print(f"   Lower move {i}: {recent_closes[i]:.2f} < {recent_closes[i-1]:.2f} (Strong DOWN)")
                else:
                    print(f"   Lower move {i}: {recent_closes[i]:.2f} >= {recent_closes[i-1]:.2f} (Weak)")
            lower_momentum_standalone = strong_moves >= 2
            print(f"   Lower momentum: {strong_moves}/2 = {lower_momentum_standalone}")
    
    print(f"\n2️⃣ SMART VECTORIZED VERSION CALCULATIONS")
    print("-" * 80)
    
    # Create smart backtester
    smart_backtester = SmartVectorizedBacktester(
        ticker=ticker,
        exchange=exchange,
        start=start_time,
        end='13:00',
        date=date,
        tokenid=tokenid,
        enable_momentum_validation=True,
        enable_realtime_detection=True
    )
    
    # Get smart data window
    minute_time = datetime.strptime(f"{date} {minute}", '%d-%m-%Y %H:%M')
    smart_window_data = smart_backtester._extract_window_data(minute_time)
    smart_close = smart_window_data['Close'].values
    
    print(f"📊 Smart data: {len(smart_close)} candles")
    print(f"📊 Data range: {smart_window_data.index[0]} to {smart_window_data.index[-1]}")
    print(f"📊 Last 5 closes: {smart_close[-5:]}")
    
    # Compare data
    data_match = np.array_equal(standalone_close, smart_close)
    print(f"📊 Data match: {data_match}")
    if not data_match:
        print(f"   Standalone length: {len(standalone_close)}")
        print(f"   Smart length: {len(smart_close)}")
        if len(standalone_close) != len(smart_close):
            print(f"   ❌ LENGTH MISMATCH!")
        else:
            diff_indices = np.where(standalone_close != smart_close)[0]
            print(f"   ❌ VALUE DIFFERENCES at indices: {diff_indices}")
    
    # Smart Nadarya Watson calculation
    nadarya_result = smart_backtester._calculate_nadarya_watson_for_window(smart_window_data)
    
    print(f"\n🧮 SMART NADARYA WATSON CALCULATION:")
    print(f"   MAE: {nadarya_result['mae']:.6f}")
    print(f"   Last y: {nadarya_result['y'][-1]:.6f}")
    print(f"   Last upper band: {nadarya_result['upper_band'][-1]:.6f}")
    print(f"   Last lower band: {nadarya_result['lower_band'][-1]:.6f}")
    print(f"   Last close: {nadarya_result['close_prices'][-1]:.6f}")
    
    # Compare Nadarya Watson results
    mae_match = abs(mae_standalone - nadarya_result['mae']) < 1e-6
    y_match = abs(y_standalone[-1] - nadarya_result['y'][-1]) < 1e-6
    print(f"   MAE match: {mae_match}")
    print(f"   Y match: {y_match}")
    
    # Check smart signals
    upper_signal_smart = nadarya_result['upper_band_signal']
    lower_signal_smart = nadarya_result['lower_band_signal']
    
    current_upper_smart = not np.isnan(upper_signal_smart[-1])
    current_lower_smart = not np.isnan(lower_signal_smart[-1])
    print(f"   Current upper signal: {current_upper_smart}")
    print(f"   Current lower signal: {current_lower_smart}")
    
    # Check last 2 minutes for smart
    if len(upper_signal_smart) >= 2:
        upper_recent_smart = any(not np.isnan(x) for x in upper_signal_smart[-2:])
        lower_recent_smart = any(not np.isnan(x) for x in lower_signal_smart[-2:])
        print(f"   Last 2 min upper: {upper_recent_smart}")
        print(f"   Last 2 min lower: {lower_recent_smart}")
        
        upper_present_smart = current_upper_smart or upper_recent_smart
        lower_present_smart = current_lower_smart or lower_recent_smart
        print(f"   Final upper present: {upper_present_smart}")
        print(f"   Final lower present: {lower_present_smart}")
    
    # Smart momentum validation
    print(f"\n🚀 SMART MOMENTUM VALIDATION:")
    if len(smart_close) >= 3:
        recent_closes_smart = smart_close[-3:]
        print(f"   Momentum closes: {recent_closes_smart}")
        
        # Compare momentum data
        momentum_data_match = np.array_equal(recent_closes, recent_closes_smart)
        print(f"   Momentum data match: {momentum_data_match}")
        
        # Check upper momentum for smart
        if current_upper_smart or (len(upper_signal_smart) >= 2 and any(not np.isnan(x) for x in upper_signal_smart[-2:])):
            strong_moves = 0
            for i in range(1, len(recent_closes_smart)):
                if recent_closes_smart[i] > recent_closes_smart[i-1]:
                    strong_moves += 1
                    print(f"   Upper move {i}: {recent_closes_smart[i]:.2f} > {recent_closes_smart[i-1]:.2f} (Strong UP)")
                else:
                    print(f"   Upper move {i}: {recent_closes_smart[i]:.2f} <= {recent_closes_smart[i-1]:.2f} (Weak)")
            upper_momentum_smart = strong_moves >= 2
            print(f"   Upper momentum: {strong_moves}/2 = {upper_momentum_smart}")
        
        # Check lower momentum for smart
        if current_lower_smart or (len(lower_signal_smart) >= 2 and any(not np.isnan(x) for x in lower_signal_smart[-2:])):
            strong_moves = 0
            for i in range(1, len(recent_closes_smart)):
                if recent_closes_smart[i] < recent_closes_smart[i-1]:
                    strong_moves += 1
                    print(f"   Lower move {i}: {recent_closes_smart[i]:.2f} < {recent_closes_smart[i-1]:.2f} (Strong DOWN)")
                else:
                    print(f"   Lower move {i}: {recent_closes_smart[i]:.2f} >= {recent_closes_smart[i-1]:.2f} (Weak)")
            lower_momentum_smart = strong_moves >= 2
            print(f"   Lower momentum: {strong_moves}/2 = {lower_momentum_smart}")
    
    print(f"\n3️⃣ FINAL COMPARISON")
    print("-" * 80)
    print(f"Data match: {data_match}")
    print(f"MAE match: {mae_match}")
    print(f"Y match: {y_match}")
    print(f"Current signals match: Upper={current_upper_standalone == current_upper_smart}, Lower={current_lower_standalone == current_lower_smart}")
    
    if 'upper_momentum_standalone' in locals() and 'upper_momentum_smart' in locals():
        print(f"Upper momentum match: {upper_momentum_standalone == upper_momentum_smart}")
    if 'lower_momentum_standalone' in locals() and 'lower_momentum_smart' in locals():
        print(f"Lower momentum match: {lower_momentum_standalone == lower_momentum_smart}")

def main():
    """Debug specific problematic minutes"""
    logger.info("🚀 Starting subtle differences debugging...")
    
    # Debug the problematic minutes
    problematic_minutes = ['12:29', '12:31', '12:55']
    
    for minute in problematic_minutes:
        debug_minute_calculations(minute)
    
    logger.info("🎉 Debugging completed!")

if __name__ == "__main__":
    main()
