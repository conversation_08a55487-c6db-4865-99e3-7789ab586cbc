"""
Test only the searchscrip method to understand the issue
"""

import sys
import os
import logging

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Add parent directory to path for api_helper
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_searchscrip_only():
    """Test only searchscrip method"""
    try:
        logger.info("🔍 Testing searchscrip method only...")
        
        # Import shared API manager
        from shared_api_manager import get_api
        
        # Get API instance
        api = get_api()
        logger.info("✅ API instance obtained")
        
        # Test searchscrip with different patterns
        test_cases = [
            ('NSE', 'REL'),           # From official example
            ('NSE', 'RELIANCE'),      # Full name
            ('NSE', 'RELIANCE-EQ'),   # Full name with -EQ
            ('NSE', 'NIFTY'),         # Index search
            ('NSE', 'BATAINDIA'),     # User's example
            ('NSE', 'BATAINDIA-EQ'),  # User's example with -EQ
        ]
        
        for exchange, searchtext in test_cases:
            logger.info(f"🔍 Testing: exchange='{exchange}', searchtext='{searchtext}'")
            
            try:
                # Call searchscrip directly
                result = api.searchscrip(exchange=exchange, searchtext=searchtext)
                
                logger.info(f"📊 Result type: {type(result)}")
                logger.info(f"📊 Result: {result}")
                
                if result is None:
                    logger.warning(f"⚠️ searchscrip returned None for {searchtext}")
                elif isinstance(result, dict):
                    if 'stat' in result:
                        logger.info(f"📊 Status: {result.get('stat')}")
                        if result.get('stat') == 'Ok' and 'values' in result:
                            logger.info(f"📊 Found {len(result['values'])} results")
                            for i, item in enumerate(result['values'][:3]):  # Show first 3
                                logger.info(f"  {i+1}. {item.get('tsym')} - Token: {item.get('token')}")
                        elif result.get('stat') == 'Not_Ok':
                            logger.error(f"❌ API Error: {result.get('emsg', 'Unknown error')}")
                    else:
                        logger.info(f"📊 Unexpected response format: {result}")
                else:
                    logger.info(f"📊 Unexpected result type: {type(result)} - {result}")
                    
            except Exception as e:
                logger.error(f"❌ Exception for {searchtext}: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
            
            logger.info("---")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main test execution"""
    logger.info("🚀 Starting searchscrip only test...")
    
    success = test_searchscrip_only()
    
    if success:
        logger.info("🎉 Test completed!")
    else:
        logger.error("❌ Test failed.")
    
    logger.info("🏁 Test finished.")

if __name__ == "__main__":
    main()
