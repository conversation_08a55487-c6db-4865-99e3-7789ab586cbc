Title: Custom Directory - Pandas TA

URL Source: https://www.pandas-ta.dev/api/custom/

Markdown Content:
The Library also includes functions to create a _custom_ (or private) directory for storing and executing _custom_ indicators.

Functions[#](https://www.pandas-ta.dev/api/custom/#functions "Permanent link")
------------------------------------------------------------------------------

The functions [**create_dir**](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.create_dir) and [**import_dir**](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.import_dir) are used to create and setup a _custom_ (or private) directory.

* * *

bind[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.bind "Permanent link")
-----------------------------------------------------------------------------------------

```
bind(
    name: str,
    fn: types.FunctionType,
    method: types.MethodType = None,
)
```

Bind

Helper function to bind the function and class method defined in a custom indicator module to the active pandas_ta instance.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `name` | `str` | The name of the indicator within pandas_ta | _required_ |
| `fn` | `types.FunctionType` | The indicator function | _required_ |
| `method` | `types.MethodType` | The class method corresponding to the passed function | `None` |

create_dir[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.create_dir "Permanent link")
-----------------------------------------------------------------------------------------------------

```
create_dir(
    path: str, categories: bool = True, verbose: bool = True
)
```

Create Dir

Sets up a suitable folder structure for working with custom indicators. Use it **once** to setup and initialize the custom folder.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `path` | `str` | Indicator directory full path | _required_ |
| `categories` | `bool` | Create category sub-folders | `True` |
| `verbose` | `bool` | Verbose output | `True` |

get_module_functions[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.get_module_functions "Permanent link")
-------------------------------------------------------------------------------------------------------------------------

```
get_module_functions(module: types.ModuleType) -> DictLike
```

Get Module Functions

Returns a dictionary with the mapping: "name" to a _function_.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `module` | `types.ModuleType` | python module | _required_ |

Returns:

| Type | Description |
| --- | --- |
| `DictLike` | Returns a dictionary with the mapping: "name" to a _function_ |

Example
Example return

```
{
    "func1_name": func1,
    "func2_name": func2, # ...
}
```

import_dir[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.import_dir "Permanent link")
-----------------------------------------------------------------------------------------------------

```
import_dir(path: str, verbose: bool = True)
```

Import Dir

Import a directory of custom (proprietary) indicators into Pandas TA.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `path` | `str` | Full path to indicator directory. | _required_ |
| `verbose` | `bool` | Output process to STDOUT. | `True` |

load_indicator_module[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.load_indicator_module "Permanent link")
---------------------------------------------------------------------------------------------------------------------------

```
load_indicator_module(name: str) -> dict
```

Helper function to (re)load an indicator module.

Returns:

| Name | Type | Description |
| --- | --- | --- |
| `dict` | `dict` | module functions mapping |
|  | `dict` | ```{ "func1_name": func1, "func2_name": func2, # ... |
|  | `dict` | }``` |

IMPORTANT

**Thanks** to all those that have sponsored and dontated to the library in the past! Your support has been greatly appreciated! ![Image 1: 🙏](https://www.pandas-ta.dev/assets/external/cdn.jsdelivr.net/gh/jdecked/twemoji@15.1.0/assets/svg/1f64f.svg)

Only **Installation Bugs/Issues** will addressed for releases between versions _0.4.25b_ and _0.4.66b_. Releases beyond version _0.4.66b_ will **only** be released after **significant** donations, sponsorships or yearly subscriptions have been received via [Buy Me a Coffee](https://www.buymeacoffee.com/twopirllc).

Support Tiers coming soon!

[![Image 2: "Buy Me A Coffee"](https://www.pandas-ta.dev/assets/external/www.buymeacoffee.com/assets/img/custom_images/orange_img.png)](https://www.buymeacoffee.com/twopirllc)

[![Image 3: ko-fi](https://www.pandas-ta.dev/assets/external/ko-fi.com/img/githubbutton_sm.svg)](https://ko-fi.com/K3K4ZRH9D)