Title: Candle - Pandas TA

URL Source: https://www.pandas-ta.dev/api/candle/

Markdown Content:
* * *

cdl_doji[#](https://www.pandas-ta.dev/api/candle/#src.pandas_ta.candle.cdl_doji.cdl_doji "Permanent link")
----------------------------------------------------------------------------------------------------------

```
cdl_doji(
    open_: Series,
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    factor: IntFloat = None,
    scalar: IntFloat = None,
    asint: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Doji

Attempts to identify a "Doji" candle which is shorter than 10% of the average of the 10 previous bars High-Low range.

Sources
*   [TA Lib](https://github.com/TA-Lib/ta-lib/blob/main/src/ta_func/ta_CDLDOJI.c)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `open_` | `Series` | `open` Series | _required_ |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `factor` | `float` | Doji value. Default: `100` | `None` |
| `scalar` | `float` | Scalar. Default: `100` | `None` |
| `asint` | `bool` | Returns as `Int`. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `naive` | `bool` | Prefills potential Doji; bodies that are less than a percentage, `factor`, of it's High-Low range. Default: `False` |
| `fillna` | `value` | Replaces `na`'s with `value`. |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Warning
TA-Lib Correlation: `np.float64(0.9434563530497265)`

Tip
Corrective contributions welcome!

* * *

cdl_inside[#](https://www.pandas-ta.dev/api/candle/#src.pandas_ta.candle.cdl_inside.cdl_inside "Permanent link")
----------------------------------------------------------------------------------------------------------------

```
cdl_inside(
    open_: Series,
    high: Series,
    low: Series,
    close: Series,
    asbool: bool = None,
    scalar: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Inside Bar

Attempts to identify an "Inside" candle which is smaller than it's previous candle.

Sources
*   [TA Lib](https://github.com/TA-Lib/ta-lib/blob/main/src/ta_func/ta_CDL3INSIDE.c)
*   [tradingview](https://www.tradingview.com/script/IyIGN1WO-Inside-Bar/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `open_` | `Series` | `open` Series | _required_ |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `asbool` | `bool` | Return booleans. Default: `False` | `None` |
| `scalar` | `float` | Scalar. Default: `100` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | Replaces `na`'s with `value`. |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

cdl_pattern[#](https://www.pandas-ta.dev/api/candle/#src.pandas_ta.candle.cdl_pattern.cdl_pattern "Permanent link")
-------------------------------------------------------------------------------------------------------------------

```
cdl_pattern(
    open_: Series,
    high: Series,
    low: Series,
    close: Series,
    name: str | List[str] = "all",
    scalar: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Candle Pattern

This function wraps TA Lib candle patterns.

Sources
*   [TA Lib](https://ta-lib.org/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `open_` | `Series` | `open` Series | _required_ |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `name` | `str | List[str]` | Pattern name or a list of pattern names. Default: `"all"` | `'all'` |
| `scalar` | `float` | Scalar. Default: `100` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | Replaces `na`'s with `value`. |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | Pattern Column(s) |

TA Lib
TA Lib must be installed

cdl_z[#](https://www.pandas-ta.dev/api/candle/#src.pandas_ta.candle.cdl_z.cdl_z "Permanent link")
-------------------------------------------------------------------------------------------------

```
cdl_z(
    open_: Series,
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    full: bool = None,
    ddof: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Z Candles

Creates candlesticks using a rolling Z Score.

Sources
*   Kevin Johnson

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `open_` | `Series` | `open` Series | _required_ |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `full` | `bool` | Apply `length` to whole DataFrame. Default: `False` | `None` |
| `ddof` | `int` | By default, uses Pandas `ddof=1`. For Numpy calculation, use `0`. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `naive` | `bool` | If `True`, prefills potential Doji less than the length if it less than a percentage of it's High-Low range. Default: `False` |
| `fillna` | `value` | Replaces `na`'s with `value`. |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 4 columns |

Note
*   Numpy `std()`[ddof](https://numpy.org/doc/stable/reference/generated/numpy.std.html) explanation.

* * *

ha[#](https://www.pandas-ta.dev/api/candle/#src.pandas_ta.candle.ha.ha "Permanent link")
----------------------------------------------------------------------------------------

```
ha(
    open_: Series,
    high: Series,
    low: Series,
    close: Series,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Heikin Ashi Candles

Creates Japanese _ohlc_ candlesticks that attempts to filter out market noise. Developed by Munehisa Homma in the 1700s, Heikin Ashi Candles share some characteristics with standard candlestick charts but creates a smoother candlestick appearance.

Sources
*   [Investopedia](https://www.investopedia.com/terms/h/heikinashi.asp)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `open_` | `Series` | `open` Series | _required_ |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | Replaces `na`'s with `value`. |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 4 columns |
