Title: Custom Directory - Pandas TA

URL Source: https://www.pandas-ta.dev/api/custom/

Published Time: Tue, 10 Jun 2025 22:38:27 GMT

Markdown Content:
The Library also includes functions to create a _custom_ (or private) directory for storing and executing _custom_ indicators.

Functions[#](https://www.pandas-ta.dev/api/custom/#functions "Permanent link")
------------------------------------------------------------------------------

The functions [**create_dir**](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.create_dir) and [**import_dir**](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.import_dir) are used to create and setup a _custom_ (or private) directory.

* * *

bind[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.bind "Permanent link")
-----------------------------------------------------------------------------------------

```
bind(
    name: str,
    fn: types.FunctionType,
    method: types.MethodType = None,
)
```

Bind

Helper function to bind the function and class method defined in a custom indicator module to the active pandas_ta instance.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `name` | `str` | The name of the indicator within pandas_ta | _required_ |
| `fn` | `types.FunctionType` | The indicator function | _required_ |
| `method` | `types.MethodType` | The class method corresponding to the passed function | `None` |

create_dir[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.create_dir "Permanent link")
-----------------------------------------------------------------------------------------------------

```
create_dir(
    path: str, categories: bool = True, verbose: bool = True
)
```

Create Dir

Sets up a suitable folder structure for working with custom indicators. Use it **once** to setup and initialize the custom folder.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `path` | `str` | Indicator directory full path | _required_ |
| `categories` | `bool` | Create category sub-folders | `True` |
| `verbose` | `bool` | Verbose output | `True` |

get_module_functions[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.get_module_functions "Permanent link")
-------------------------------------------------------------------------------------------------------------------------

```
get_module_functions(module: types.ModuleType) -> DictLike
```

Get Module Functions

Returns a dictionary with the mapping: "name" to a _function_.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `module` | `types.ModuleType` | python module | _required_ |

Returns:

| Type | Description |
| --- | --- |
| `DictLike` | Returns a dictionary with the mapping: "name" to a _function_ |

Example
Example return

```
{
    "func1_name": func1,
    "func2_name": func2, # ...
}
```

import_dir[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.import_dir "Permanent link")
-----------------------------------------------------------------------------------------------------

```
import_dir(path: str, verbose: bool = True)
```

Import Dir

Import a directory of custom (proprietary) indicators into Pandas TA.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `path` | `str` | Full path to indicator directory. | _required_ |
| `verbose` | `bool` | Output process to STDOUT. | `True` |

load_indicator_module[#](https://www.pandas-ta.dev/api/custom/#src.pandas_ta.custom.load_indicator_module "Permanent link")
---------------------------------------------------------------------------------------------------------------------------

```
load_indicator_module(name: str) -> dict
```

Helper function to (re)load an indicator module.

Returns:

| Name | Type | Description |
| --- | --- | --- |
| `dict` | `dict` | module functions mapping |
|  | `dict` | ```{ "func1_name": func1, "func2_name": func2, # ... |
|  | `dict` | }``` |

