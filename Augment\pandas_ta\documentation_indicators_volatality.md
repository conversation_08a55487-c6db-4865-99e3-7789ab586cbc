Title: Volatility - Pandas TA

URL Source: https://www.pandas-ta.dev/api/volatility/

Markdown Content:
* * *

aberration[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.aberration.aberration "Permanent link")
------------------------------------------------------------------------------------------------------------------------

```
aberration(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    atr_length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Aberration

Similar to Keltner Channels.

Sources
*   [Request #46](https://github.com/twopirllc/pandas-ta/issues/46)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `5` | `None` |
| `atr_length` | `int` | ATR period. Default: `15` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 4 columns |

* * *

accbands[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.accbands.accbands "Permanent link")
------------------------------------------------------------------------------------------------------------------

```
accbands(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    c: IntFloat = None,
    drift: Int = None,
    mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Acceleration Bands

This indicator, by Price Headley, creates lower and upper bands centered around a moving average based on a ratio of it's High-Low range.

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/acceleration-bands-abands/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `c` | `int` | Multiplier. Default: `4` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"sma"` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

* * *

atr[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.atr.atr "Permanent link")
---------------------------------------------------------------------------------------------------

```
atr(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    mamode: str = None,
    talib: bool = None,
    prenan: bool = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Average True Range

This indicator attempts to quantify volatility with a focus on gaps or limit moves.

Sources
*   [tradingview](https://www.tradingview.com/wiki/Average_True_Range_(ATR))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"rma"` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `prenan` | `bool` | Sets initial values to `np.nan` based on `drift`. Default: `False` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `percent` | `bool` | Return as percent. Default: `False` |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

atrts[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.atrts.atrts "Permanent link")
---------------------------------------------------------------------------------------------------------

```
atrts(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    ma_length: Int = None,
    k: IntFloat = None,
    mamode: str = None,
    talib: bool = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

ATR Trailing Stop

This indicator attempts to identify exits for long and short positions. To determine trend, it uses a moving average with a scalable ATR.

Sources
*   [motivewave](https://www.motivewave.com/studies/atr_trailing_stops.htm)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `ma_length` | `int` | MA Length. Default: `20` | `None` |
| `k` | `int` | ATR multiplier. Default: `3` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `percent` | `bool` | Return as percent. Default: `False` |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

bbands[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.bbands.bbands "Permanent link")
------------------------------------------------------------------------------------------------------------

```
bbands(
    close: Series,
    length: Int = None,
    lower_std: IntFloat = None,
    upper_std: IntFloat = None,
    ddof: Int = None,
    mamode: str = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Bollinger Bands

This indicator, by John Bollinger, attempts to quantify volatility by creating lower and upper bands centered around a moving average.

Sources
*   [tradingview](https://www.tradingview.com/wiki/Bollinger_Bands_(BB))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `5` | `None` |
| `lower_std` | `IntFloat` | Lower standard deviation. Default: `2.0` | `None` |
| `upper_std` | `IntFloat` | Upper standard deviation. Default: `2.0` | `None` |
| `ddof` | `int` | Degrees of Freedom to use. Default: `0` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"sma"` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `ddof` | `int` | By default, uses Pandas `ddof=1`. For Numpy calculation, use `0`. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 5 columns |

Note
*   TA Lib does not have a `ddof` parameter.
*   The divisor used in calculations is: `N - ddof`, where `N` is the number of elements. To use `ddof`, set `talib=False`.

* * *

chandelier_exit[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.chandelier_exit.chandelier_exit "Permanent link")
---------------------------------------------------------------------------------------------------------------------------------------

```
chandelier_exit(
    high: Series,
    low: Series,
    close: Series,
    high_length: Int = None,
    low_length: Int = None,
    atr_length: Int = None,
    multiplier: IntFloat = None,
    mamode: str = None,
    talib: bool = None,
    use_close: bool = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
)
```

Chandelier Exit

This indicator attempts to identify trailing stop-losses based on ATR.

Sources
*   [stockcharts](https://school.stockcharts.com/doku.php?id=technical_indicators:chandelier_exit)
*   [tradingview](https://in.tradingview.com/scripts/chandelier/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `high_length` | `int` | Highest high period. Default: `22` | `None` |
| `low_length` | `int` | Lowest low period. Default: `22` | `None` |
| `atr_length` | `int)` | ATR length. Default: `14` | `None` |
| `multiplier` | `float` | Lower & Upper Bands scalar. Default: `2.0` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"rma"` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `use_close` | `bool` | Use `max(high_length, low_length)` for the `close`. Default: `False` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

* * *

donchian[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.donchian.donchian "Permanent link")
------------------------------------------------------------------------------------------------------------------

```
donchian(
    high: Series,
    low: Series,
    lower_length: Int = None,
    upper_length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Donchian Channels

This indicator attempt to quantify volatility similarily to Bollinger Bands and Keltner Channels.

Sources
*   [tradingview](https://www.tradingview.com/wiki/Donchian_Channels_(DC))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `lower_length` | `int` | Lower period. Default: `20` | `None` |
| `upper_length` | `int` | Upper period. Default: `20` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

* * *

hwc[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.hwc.hwc "Permanent link")
---------------------------------------------------------------------------------------------------

```
hwc(
    close: Series,
    scalar: IntFloat = None,
    channels: bool = None,
    na: IntFloat = None,
    nb: IntFloat = None,
    nc: IntFloat = None,
    nd: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Holt-Winter Channel

This indicator creates a three-parameter moving average using the "Holt-Winters" method.

Sources
*   [rengel8](https://github.com/rengel8) (2021-08-11) based on the implementation from "MetaTrader 5"
*   [mql5](https://www.mql5.com/en/code/20857)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `scalar` | `float` | Channel scalar. Default: `1` | `None` |
| `channels` | `bool` | Return width and percentage columns. Default: `True` | `None` |
| `na` | `float` | Smoothed series in range `[0, 1]`. Default: `0.2` | `None` |
| `nb` | `float` | Trend value in range `[0, 1]`. Default: `0.1` | `None` |
| `nc` | `float` | Seasonality value in range `[0, 1]`. Default: `0.1` | `None` |
| `nd` | `float` | Channel value in range `[0, 1]`. Default: `0.1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

* * *

kc[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.kc.kc "Permanent link")
------------------------------------------------------------------------------------------------

```
kc(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    scalar: IntFloat = None,
    tr: bool = None,
    mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Keltner Channels

This indicator attempts to identify volatility similarily to Bollinger Bands and Donchian Channels.

Sources
*   [tradingview](https://www.tradingview.com/wiki/Keltner_Channels_(KC))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `scalar` | `float` | Band scalar. Default: `2` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `tr` | `bool` | Use True Range calculation. Otherwise use `high - low` for range computation. Default: `True` |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

* * *

massi[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.massi.massi "Permanent link")
---------------------------------------------------------------------------------------------------------

```
massi(
    high: Series,
    low: Series,
    fast: Int = None,
    slow: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Mass Index

This indicator attempts to use a High-Low Range to identify trend reversals based on range expansions.

Sources
*   [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:mass_index)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `fast` | `int` | Fast period. Default: `9` | `None` |
| `slow` | `int` | Slow period. Default: `25` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

natr[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.natr.natr "Permanent link")
------------------------------------------------------------------------------------------------------

```
natr(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    scalar: IntFloat = None,
    mamode: str = None,
    talib: bool = None,
    prenan: bool = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Normalized Average True Range

This indicator applies a normalizer to Average True Range.

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/normalized-average-true-range-natr/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `scalar` | `float` | Scalar. Default: `100` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `prenan` | `bool` | Sets initial values to `np.nan` based on `drift`. Default: `False` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Warning
TA-Lib Correlation: `np.float64(0.9506743353852364)`

Tip
Corrective contributions welcome!

* * *

pdist[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.pdist.pdist "Permanent link")
---------------------------------------------------------------------------------------------------------

```
pdist(
    open_: Series,
    high: Series,
    low: Series,
    close: Series,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Price Distance

This indicator attempts to quantify the magnitude covered by price movements.

Sources
*   [prorealcode](https://www.prorealcode.com/prorealtime-indicators/pricedistance/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `open_` | `Series` | `open` Series | _required_ |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

rvi[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.rvi.rvi "Permanent link")
---------------------------------------------------------------------------------------------------

```
rvi(
    close: Series,
    high: Series = None,
    low: Series = None,
    length: Int = None,
    scalar: IntFloat = None,
    refined: bool = None,
    thirds: bool = None,
    mamode: str = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Relative Volatility Index

This indicator attempts to quantify volatility using standard deviation.

Sources
*   [motivewave](https://www.motivewave.com/studies/relative_volatility_index.htm)
*   [tradingview A](https://www.tradingview.com/script/mLZJqxKn-Relative-Volatility-Index/)
*   [tradingview B](https://www.tradingview.com/support/solutions/43000594684-relative-volatility-index/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | `None` |
| `low` | `Series` | `low` Series | `None` |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `scalar` | `float` | Bands scalar. Default: `100` | `None` |
| `refined` | `bool` | Use 'refined' calculation which is the average of RVI(high) and RVI(low) instead of RVI(close). Default: `False` | `None` |
| `thirds` | `bool` | Average of `high`, `low` and `close`. Default: `False` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

* * *

thermo[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.thermo.thermo "Permanent link")
------------------------------------------------------------------------------------------------------------

```
thermo(
    high: Series,
    low: Series,
    length: Int = None,
    long: Int = None,
    short: Int = None,
    mamode: str = None,
    asint: bool = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Elders Thermometer

This indicator, by Dr Alexander Elder, attempts to quantify volatility.

Sources
*   [motivewave](https://www.motivewave.com/studies/elders_thermometer.htm)
*   [tradingview](https://www.tradingview.com/script/HqvTuEMW-Elder-s-Market-Thermometer-LazyBear/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `long` | `int` | Buy factor. Default: `2` | `None` |
| `short` | `float` | Sell factor. Default: `0.5` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `asint` | `int` | Returns as int. Default: `True` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 4 columns |

* * *

true_range[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.true_range.true_range "Permanent link")
------------------------------------------------------------------------------------------------------------------------

```
true_range(
    high: Series,
    low: Series,
    close: Series,
    talib: bool = None,
    prenan: bool = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

True Range

This indicator attempts to quantify a High-Low range including potential gap scenarios.

Sources
*   [macroption](https://www.macroption.com/true-range/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `prenan` | `bool` | Sets initial values to `nan` based on `drift`. Default: `False` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Warning
TA-Lib Correlation: `np.float64(0.9999999999999999)`

Tip
Corrective contributions welcome!

* * *

ui[#](https://www.pandas-ta.dev/api/volatility/#src.pandas_ta.volatility.ui.ui "Permanent link")
------------------------------------------------------------------------------------------------

```
ui(
    close: Series,
    length: Int = None,
    scalar: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Ulcer Index

This indicator, by Peter Martin, attempts to quantify downside volatility with a Quadratic Mean.

Sources
*   [tangotools](http://www.tangotools.com/ui/ui.htm)
*   [tradingtechnologies](https://library.tradingtechnologies.com/trade/chrt-ti-ulcer-index.html)
*   [wikipedia](https://en.wikipedia.org/wiki/Ulcer_index)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `scalar` | `float` | Bands scalar. Default: `100` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `everget` | `value` | Use Evergets' TradingView SMA. Default: `False` |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |
