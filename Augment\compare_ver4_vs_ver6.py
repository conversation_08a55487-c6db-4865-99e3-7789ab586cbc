"""
Compare Ver4 vs Ver6 Implementation

This script runs both Ver4 logic and Ver6 optimized versions and compares:
1. Signal detection accuracy
2. Timing differences
3. Entry/exit points
4. Performance metrics
5. Logic preservation validation
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Ver4VsVer6Comparator:
    """Compare Ver4 and Ver6 implementations"""
    
    def __init__(self, ticker, date, start_time, end_time, exchange='NSE'):
        self.ticker = ticker
        self.date = date
        self.start_time = start_time
        self.end_time = end_time
        self.exchange = exchange
        
        # Results storage
        self.ver4_results = {}
        self.ver6_results = {}
        self.comparison_data = {}
        
        # Import components
        from shared_api_manager import get_api
        self.api = get_api()
        
        # Resolve token
        self.tokenid = self._resolve_token()
    
    def _resolve_token(self):
        """Resolve ticker to token ID"""
        try:
            if self.exchange == 'NSE':
                search_result = self.api.searchscrip(exchange='NSE', searchtext=self.ticker + '-EQ')
                if search_result and 'values' in search_result and search_result['values']:
                    tokenid = search_result['values'][0]['token']
                    logger.info(f"📊 Resolved {self.ticker} to token: {tokenid}")
                    return tokenid
                else:
                    raise Exception(f"Symbol {self.ticker} not found")
            else:
                raise Exception(f"Exchange {self.exchange} not supported yet")
        except Exception as e:
            logger.error(f"❌ Error resolving token: {str(e)}")
            raise
    
    def run_ver4_logic(self):
        """Run Ver4 exact logic implementation"""
        logger.info("🔄 Running Ver4 logic...")
        
        start_time = time.time()
        
        try:
            from optimized_backtester_v4_logic import OptimizedBacktesterV4Logic
            
            # Create Ver4 backtester
            backtester_v4 = OptimizedBacktesterV4Logic(
                ticker=self.ticker,
                exchange=self.exchange,
                tokenid=self.tokenid,
                date=self.date,
                start_time=self.start_time,
                end_time=self.end_time
            )
            
            # Run backtesting
            results = backtester_v4.run_backtest()
            
            execution_time = time.time() - start_time
            
            self.ver4_results = {
                'execution_time': execution_time,
                'results': results,
                'backtester': backtester_v4,
                'performance_stats': backtester_v4.get_performance_stats(),
                'signal_data': backtester_v4.get_signal_history(),
                'cache_stats': backtester_v4.get_cache_stats()
            }
            
            logger.info(f"✅ Ver4 completed in {execution_time:.2f} seconds")
            return self.ver4_results
            
        except Exception as e:
            logger.error(f"❌ Ver4 execution failed: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def run_ver6_logic(self):
        """Run Ver6 optimized implementation"""
        logger.info("🔄 Running Ver6 logic...")
        
        start_time = time.time()
        
        try:
            # Import Ver6 backtester
            sys.path.append(os.path.dirname(current_dir))
            from backtester_ver6 import StarterSystemV6
            
            # Create Ver6 backtester
            backtester_v6 = StarterSystemV6(
                ticker=self.ticker,
                exchange=self.exchange,
                start=self.start_time,
                end=self.end_time,
                date=self.date,
                tokenid=self.tokenid
            )
            
            # Run backtesting
            backtester_v6.run(signal_reversal=0)
            
            execution_time = time.time() - start_time
            
            self.ver6_results = {
                'execution_time': execution_time,
                'backtester': backtester_v6,
                'data': backtester_v6.data,
                'performance_stats': getattr(backtester_v6, 'performance_stats', {}),
                'signal_data': getattr(backtester_v6, 'signal_history', [])
            }
            
            logger.info(f"✅ Ver6 completed in {execution_time:.2f} seconds")
            return self.ver6_results
            
        except Exception as e:
            logger.error(f"❌ Ver6 execution failed: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    def compare_results(self):
        """Compare Ver4 and Ver6 results"""
        logger.info("🔍 Comparing Ver4 vs Ver6 results...")
        
        if not self.ver4_results or not self.ver6_results:
            logger.error("❌ Cannot compare - one or both versions failed to run")
            return None
        
        comparison = {
            'performance': self._compare_performance(),
            'signals': self._compare_signals(),
            'accuracy': self._compare_accuracy(),
            'timing': self._compare_timing()
        }
        
        self.comparison_data = comparison
        return comparison
    
    def _compare_performance(self):
        """Compare performance metrics"""
        v4_time = self.ver4_results['execution_time']
        v6_time = self.ver6_results['execution_time']
        
        speedup = v4_time / v6_time if v6_time > 0 else float('inf')
        
        v4_cache = self.ver4_results.get('cache_stats', {})
        v4_perf = self.ver4_results.get('performance_stats', {})
        v6_perf = self.ver6_results.get('performance_stats', {})
        
        return {
            'ver4_execution_time': v4_time,
            'ver6_execution_time': v6_time,
            'speedup_factor': speedup,
            'ver4_cache_hits': v4_cache.get('cache_hits', 0),
            'ver4_cache_misses': v4_cache.get('cache_misses', 0),
            'ver4_api_calls': v4_perf.get('api_calls', 0),
            'ver6_api_calls': v6_perf.get('api_calls', 0)
        }
    
    def _compare_signals(self):
        """Compare signal generation"""
        v4_signals = self.ver4_results.get('signal_data', [])
        v6_signals = self.ver6_results.get('signal_data', [])
        
        # Extract signal information
        v4_signal_times = []
        v6_signal_times = []
        
        # Process Ver4 signals
        if isinstance(v4_signals, list):
            for signal in v4_signals:
                if isinstance(signal, dict) and signal.get('signal', 0) != 0:
                    v4_signal_times.append(signal.get('time', ''))
        
        # Process Ver6 signals
        if hasattr(self.ver6_results['backtester'], 'data'):
            v6_data = self.ver6_results['backtester'].data
            if 'SidewaysNadarya' in v6_data.columns:
                v6_signal_mask = v6_data['SidewaysNadarya'] != 0
                v6_signal_times = v6_data[v6_signal_mask].index.tolist()
        
        return {
            'ver4_signal_count': len(v4_signal_times),
            'ver6_signal_count': len(v6_signal_times),
            'ver4_signal_times': v4_signal_times,
            'ver6_signal_times': v6_signal_times,
            'signals_match': len(v4_signal_times) == len(v6_signal_times)
        }
    
    def _compare_accuracy(self):
        """Compare accuracy of implementations"""
        # This would compare the actual signal values, entry/exit points, etc.
        # For now, we'll do a basic comparison
        
        v4_data = self.ver4_results.get('results', {})
        v6_data = self.ver6_results.get('data', pd.DataFrame())
        
        accuracy_score = 1.0  # Start with perfect score
        
        # Compare signal counts
        v4_signals = len(self.ver4_results.get('signal_data', []))
        v6_signals = len(self.ver6_results.get('signal_data', []))
        
        if v4_signals != v6_signals:
            accuracy_score -= 0.2
        
        return {
            'accuracy_score': accuracy_score,
            'logic_preserved': accuracy_score >= 0.9,
            'ver4_data_points': len(v4_data) if isinstance(v4_data, dict) else 0,
            'ver6_data_points': len(v6_data) if isinstance(v6_data, pd.DataFrame) else 0
        }
    
    def _compare_timing(self):
        """Compare timing and execution patterns"""
        return {
            'ver4_faster': self.ver4_results['execution_time'] < self.ver6_results['execution_time'],
            'time_difference': abs(self.ver4_results['execution_time'] - self.ver6_results['execution_time']),
            'performance_improvement': (
                (self.ver4_results['execution_time'] - self.ver6_results['execution_time']) / 
                self.ver4_results['execution_time'] * 100
            ) if self.ver4_results['execution_time'] > 0 else 0
        }
    
    def create_comparison_plots(self):
        """Create comprehensive comparison plots"""
        if not self.comparison_data:
            logger.error("❌ No comparison data available. Run compare_results() first.")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'Ver4 vs Ver6 Comparison for {self.ticker} on {self.date}', fontsize=16, fontweight='bold')
        
        # Plot 1: Performance Comparison
        ax1 = axes[0, 0]
        perf_data = self.comparison_data['performance']
        versions = ['Ver4', 'Ver6']
        times = [perf_data['ver4_execution_time'], perf_data['ver6_execution_time']]
        colors = ['blue', 'green']
        
        bars = ax1.bar(versions, times, color=colors, alpha=0.7)
        ax1.set_ylabel('Execution Time (seconds)')
        ax1.set_title('Execution Time Comparison')
        ax1.grid(True, alpha=0.3)
        
        # Add speedup annotation
        speedup = perf_data['speedup_factor']
        ax1.text(0.5, max(times) * 0.8, f'Speedup: {speedup:.2f}x', 
                ha='center', fontsize=12, fontweight='bold')
        
        # Plot 2: Signal Count Comparison
        ax2 = axes[0, 1]
        signal_data = self.comparison_data['signals']
        signal_counts = [signal_data['ver4_signal_count'], signal_data['ver6_signal_count']]
        
        bars = ax2.bar(versions, signal_counts, color=colors, alpha=0.7)
        ax2.set_ylabel('Number of Signals')
        ax2.set_title('Signal Generation Comparison')
        ax2.grid(True, alpha=0.3)
        
        # Add match indicator
        match_text = "✅ Match" if signal_data['signals_match'] else "❌ Mismatch"
        ax2.text(0.5, max(signal_counts) * 0.8, match_text, 
                ha='center', fontsize=12, fontweight='bold')
        
        # Plot 3: Cache Performance (Ver4 only)
        ax3 = axes[1, 0]
        cache_hits = perf_data['ver4_cache_hits']
        cache_misses = perf_data['ver4_cache_misses']
        
        if cache_hits + cache_misses > 0:
            cache_labels = ['Cache Hits', 'Cache Misses']
            cache_values = [cache_hits, cache_misses]
            ax3.pie(cache_values, labels=cache_labels, autopct='%1.1f%%', startangle=90)
            ax3.set_title('Ver4 Cache Performance')
        else:
            ax3.text(0.5, 0.5, 'No Cache Data', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('Ver4 Cache Performance')
        
        # Plot 4: Accuracy Score
        ax4 = axes[1, 1]
        accuracy_data = self.comparison_data['accuracy']
        accuracy_score = accuracy_data['accuracy_score']
        
        # Create a gauge-like plot
        theta = np.linspace(0, np.pi, 100)
        r = np.ones_like(theta)
        
        ax4.plot(theta, r, 'k-', linewidth=2)
        ax4.fill_between(theta, 0, r, alpha=0.3, color='lightgray')
        
        # Fill accuracy portion
        accuracy_theta = theta[:int(accuracy_score * 100)]
        accuracy_r = r[:int(accuracy_score * 100)]
        color = 'green' if accuracy_score >= 0.9 else 'orange' if accuracy_score >= 0.7 else 'red'
        ax4.fill_between(accuracy_theta, 0, accuracy_r, alpha=0.7, color=color)
        
        ax4.set_xlim(0, np.pi)
        ax4.set_ylim(0, 1.2)
        ax4.set_title(f'Logic Preservation: {accuracy_score:.1%}')
        ax4.text(np.pi/2, 0.5, f'{accuracy_score:.1%}', ha='center', va='center', 
                fontsize=20, fontweight='bold')
        ax4.set_xticks([])
        ax4.set_yticks([])
        
        plt.tight_layout()
        
        # Save plot
        plot_filename = f'ver4_vs_ver6_comparison_{self.ticker}_{self.date.replace("-", "")}.png'
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        logger.info(f"📊 Comparison plot saved as {plot_filename}")
        
        plt.show()
        
        return fig
    
    def print_detailed_comparison(self):
        """Print detailed comparison report"""
        if not self.comparison_data:
            logger.error("❌ No comparison data available. Run compare_results() first.")
            return
        
        print("\n" + "="*100)
        print(f"📊 DETAILED COMPARISON REPORT: Ver4 vs Ver6 for {self.ticker} on {self.date}")
        print("="*100)
        
        # Performance comparison
        perf = self.comparison_data['performance']
        print(f"\n🚀 PERFORMANCE COMPARISON:")
        print(f"   Ver4 Execution Time: {perf['ver4_execution_time']:.3f} seconds")
        print(f"   Ver6 Execution Time: {perf['ver6_execution_time']:.3f} seconds")
        print(f"   Speedup Factor: {perf['speedup_factor']:.2f}x")
        print(f"   Performance Improvement: {self.comparison_data['timing']['performance_improvement']:.1f}%")
        
        # Signal comparison
        signals = self.comparison_data['signals']
        print(f"\n🎯 SIGNAL COMPARISON:")
        print(f"   Ver4 Signals Generated: {signals['ver4_signal_count']}")
        print(f"   Ver6 Signals Generated: {signals['ver6_signal_count']}")
        print(f"   Signals Match: {'✅ Yes' if signals['signals_match'] else '❌ No'}")
        
        if signals['ver4_signal_times']:
            print(f"   Ver4 Signal Times: {', '.join(map(str, signals['ver4_signal_times']))}")
        if signals['ver6_signal_times']:
            print(f"   Ver6 Signal Times: {', '.join(map(str, signals['ver6_signal_times']))}")
        
        # Accuracy comparison
        accuracy = self.comparison_data['accuracy']
        print(f"\n✅ ACCURACY COMPARISON:")
        print(f"   Logic Preservation Score: {accuracy['accuracy_score']:.1%}")
        print(f"   Logic Preserved: {'✅ Yes' if accuracy['logic_preserved'] else '❌ No'}")
        print(f"   Ver4 Data Points: {accuracy['ver4_data_points']}")
        print(f"   Ver6 Data Points: {accuracy['ver6_data_points']}")
        
        # Cache performance
        print(f"\n💾 CACHE PERFORMANCE (Ver4):")
        print(f"   Cache Hits: {perf['ver4_cache_hits']}")
        print(f"   Cache Misses: {perf['ver4_cache_misses']}")
        if perf['ver4_cache_hits'] + perf['ver4_cache_misses'] > 0:
            hit_rate = perf['ver4_cache_hits'] / (perf['ver4_cache_hits'] + perf['ver4_cache_misses'])
            print(f"   Cache Hit Rate: {hit_rate:.1%}")
        
        # API calls
        print(f"\n🌐 API USAGE:")
        print(f"   Ver4 API Calls: {perf['ver4_api_calls']}")
        print(f"   Ver6 API Calls: {perf['ver6_api_calls']}")
        
        # Overall assessment
        print(f"\n🏆 OVERALL ASSESSMENT:")
        if accuracy['logic_preserved'] and perf['speedup_factor'] > 1:
            print("   ✅ SUCCESS: Ver6 maintains Ver4 logic with improved performance!")
        elif accuracy['logic_preserved']:
            print("   ⚠️  PARTIAL: Logic preserved but no significant performance gain")
        else:
            print("   ❌ ISSUE: Logic not properly preserved in Ver6")
        
        print("\n" + "="*100)

def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Compare Ver4 vs Ver6 implementations')
    parser.add_argument('--ticker', default='BATAINDIA', help='Ticker symbol')
    parser.add_argument('--date', default='20-06-2025', help='Date in DD-MM-YYYY format')
    parser.add_argument('--start', default='12:00', help='Start time in HH:MM format')
    parser.add_argument('--end', default='15:30', help='End time in HH:MM format')
    parser.add_argument('--exchange', default='NSE', help='Exchange')
    
    args = parser.parse_args()
    
    logger.info(f"🚀 Starting Ver4 vs Ver6 comparison for {args.ticker}")
    
    # Create comparator
    comparator = Ver4VsVer6Comparator(
        ticker=args.ticker,
        date=args.date,
        start_time=args.start,
        end_time=args.end,
        exchange=args.exchange
    )
    
    # Run both versions
    comparator.run_ver4_logic()
    comparator.run_ver6_logic()
    
    # Compare results
    comparator.compare_results()
    
    # Create plots
    comparator.create_comparison_plots()
    
    # Print detailed report
    comparator.print_detailed_comparison()
    
    logger.info("🎉 Comparison completed!")

if __name__ == "__main__":
    main()
