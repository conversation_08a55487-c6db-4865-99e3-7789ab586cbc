{"cells": [{"cell_type": "code", "execution_count": 8, "id": "d8e714b7", "metadata": {}, "outputs": [], "source": ["import pandas_ta as ta\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "84bfb98a", "metadata": {}, "source": ["## Fetching data"]}, {"cell_type": "code", "execution_count": 15, "id": "18cb5a85", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame()\n", "df = df.ta.ticker(\"aapl\")"]}, {"cell_type": "code", "execution_count": 10, "id": "7efb0c2a", "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits  \n", "Date                                                           \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0  \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0  \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0  \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0  \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0  \n", "...                              ...        ...           ...  \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0  \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0  \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0  \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0  \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0  \n", "\n", "[10545 rows x 7 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 16, "id": "639c8062", "metadata": {}, "outputs": [], "source": ["# Valid periods: 1d,5d,1mo,3mo,6mo,1y,2y,5y,10y,ytd,max\n", "\n", "# Valid intervals: 1m,2m,5m,15m,30m,60m,90m,1h,1d,5d,1wk,1mo,3mo\n", "\n", "df = df.ta.ticker(\"aapl\", period=\"1mo\", interval=\"1h\")"]}, {"cell_type": "code", "execution_count": 17, "id": "6668f1c5", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-09-08 09:30:00-04:00</th>\n", "      <td>154.639999</td>\n", "      <td>155.839996</td>\n", "      <td>154.369995</td>\n", "      <td>154.919998</td>\n", "      <td>20423405</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 10:30:00-04:00</th>\n", "      <td>154.910004</td>\n", "      <td>156.360001</td>\n", "      <td>154.700195</td>\n", "      <td>155.990005</td>\n", "      <td>9445752</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 11:30:00-04:00</th>\n", "      <td>155.985001</td>\n", "      <td>156.190002</td>\n", "      <td>152.679993</td>\n", "      <td>153.149902</td>\n", "      <td>12280244</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 12:30:00-04:00</th>\n", "      <td>153.149994</td>\n", "      <td>154.020004</td>\n", "      <td>152.695007</td>\n", "      <td>153.733505</td>\n", "      <td>10943346</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 13:30:00-04:00</th>\n", "      <td>153.740005</td>\n", "      <td>154.139999</td>\n", "      <td>153.229996</td>\n", "      <td>153.789993</td>\n", "      <td>7342717</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 12:30:00-04:00</th>\n", "      <td>140.669998</td>\n", "      <td>140.720001</td>\n", "      <td>139.940002</td>\n", "      <td>140.279999</td>\n", "      <td>7685803</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 13:30:00-04:00</th>\n", "      <td>140.145004</td>\n", "      <td>140.600006</td>\n", "      <td>140.035004</td>\n", "      <td>140.077896</td>\n", "      <td>6175166</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 14:30:00-04:00</th>\n", "      <td>140.070007</td>\n", "      <td>140.535004</td>\n", "      <td>139.600006</td>\n", "      <td>139.721893</td>\n", "      <td>8536199</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 15:30:00-04:00</th>\n", "      <td>139.720001</td>\n", "      <td>140.490005</td>\n", "      <td>139.445007</td>\n", "      <td>140.080002</td>\n", "      <td>13306374</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 16:00:00-04:00</th>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>155 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "2022-09-08 09:30:00-04:00  154.639999  155.839996  154.369995  154.919998   \n", "2022-09-08 10:30:00-04:00  154.910004  156.360001  154.700195  155.990005   \n", "2022-09-08 11:30:00-04:00  155.985001  156.190002  152.679993  153.149902   \n", "2022-09-08 12:30:00-04:00  153.149994  154.020004  152.695007  153.733505   \n", "2022-09-08 13:30:00-04:00  153.740005  154.139999  153.229996  153.789993   \n", "...                               ...         ...         ...         ...   \n", "2022-10-07 12:30:00-04:00  140.669998  140.720001  139.940002  140.279999   \n", "2022-10-07 13:30:00-04:00  140.145004  140.600006  140.035004  140.077896   \n", "2022-10-07 14:30:00-04:00  140.070007  140.535004  139.600006  139.721893   \n", "2022-10-07 15:30:00-04:00  139.720001  140.490005  139.445007  140.080002   \n", "2022-10-07 16:00:00-04:00  140.089996  140.089996  140.089996  140.089996   \n", "\n", "                             Volume  Dividends  Stock Splits  \n", "2022-09-08 09:30:00-04:00  20423405          0             0  \n", "2022-09-08 10:30:00-04:00   9445752          0             0  \n", "2022-09-08 11:30:00-04:00  12280244          0             0  \n", "2022-09-08 12:30:00-04:00  10943346          0             0  \n", "2022-09-08 13:30:00-04:00   7342717          0             0  \n", "...                             ...        ...           ...  \n", "2022-10-07 12:30:00-04:00   7685803          0             0  \n", "2022-10-07 13:30:00-04:00   6175166          0             0  \n", "2022-10-07 14:30:00-04:00   8536199          0             0  \n", "2022-10-07 15:30:00-04:00  13306374          0             0  \n", "2022-10-07 16:00:00-04:00         0          0             0  \n", "\n", "[155 rows x 7 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "markdown", "id": "da7a37a4", "metadata": {}, "source": ["## Indicators"]}, {"cell_type": "code", "execution_count": 10, "id": "914c25f1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on AnalysisIndicators in module pandas_ta.core object:\n", "\n", "class AnalysisIndicators(BasePandasObject)\n", " |  AnalysisIndicators(pandas_obj)\n", " |  \n", " |  This Pandas Extension is named 'ta' for Technical Analysis. In other words,\n", " |  it is a Numerical Time Series Feature Generator where the Time Series data\n", " |  is biased towards Financial Market data; typical data includes columns\n", " |  named :\"open\", \"high\", \"low\", \"close\", \"volume\".\n", " |  \n", " |  This TA Library hopefully allows you to apply familiar and unique Technical\n", " |  Analysis Indicators easily with the DataFrame Extension named 'ta'. Even\n", " |  though 'ta' is a Pandas DataFrame Extension, you can still call Technical\n", " |  Analysis indicators individually if you are more comfortable with that\n", " |  approach or it allows you to easily and automatically apply the indicators\n", " |  with the strategy method. See: help(ta.strategy).\n", " |  \n", " |  By default, the 'ta' extension uses lower case column names: open, high,\n", " |  low, close, and volume. You can override the defaults by providing the it's\n", " |  replacement name when calling the indicator. For example, to call the\n", " |  indicator hl2().\n", " |  \n", " |  With 'default' columns: open, high, low, close, and volume.\n", " |  >>> df.ta.hl2()\n", " |  >>> df.ta(kind=\"hl2\")\n", " |  \n", " |  With DataFrame columns: Open, High, Low, Close, and Volume.\n", " |  >>> df.ta.hl2(high=\"High\", low=\"Low\")\n", " |  >>> df.ta(kind=\"hl2\", high=\"High\", low=\"Low\")\n", " |  \n", " |  If you do not want to use a DataFrame Extension, just call it normally.\n", " |  >>> sma10 = ta.sma(df[\"Close\"]) # Default length=10\n", " |  >>> sma50 = ta.sma(df[\"Close\"], length=50)\n", " |  >>> ichi<PERSON><PERSON>, span = ta.ichimoku(df[\"High\"], df[\"Low\"], df[\"Close\"])\n", " |  \n", " |  Args:\n", " |      kind (str, optional): Default: None. Kind is the 'name' of the indicator.\n", " |          It converts kind to lowercase before calling.\n", " |      timed (bool, optional): Default: False. Curious about the execution\n", " |          speed?\n", " |      kwargs: Extension specific modifiers.\n", " |          append (bool, optional): Default: False. When True, it appends the\n", " |          resultant column(s) to the DataFrame.\n", " |  \n", " |  Returns:\n", " |      Most Indicators will return a Pandas Series. Others like MACD, BBANDS,\n", " |      KC, et al will return a Pandas DataFrame. Ichimoku on the other hand\n", " |      will return two DataFrames, the Ichimoku DataFrame for the known period\n", " |      and a Span DataFrame for the future of the Span values.\n", " |  \n", " |  Let's get started!\n", " |  \n", " |  1. Loading the 'ta' module:\n", " |  >>> import pandas as pd\n", " |  >>> import ta as ta\n", " |  \n", " |  2. Load some data:\n", " |  >>> df = pd.read_csv(\"AAPL.csv\", index_col=\"date\", parse_dates=True)\n", " |  \n", " |  3. Help!\n", " |  3a. General Help:\n", " |  >>> help(df.ta)\n", " |  >>> df.ta()\n", " |  3b. Indicator Help:\n", " |  >>> help(ta.apo)\n", " |  3c. Indicator Extension Help:\n", " |  >>> help(df.ta.apo)\n", " |  \n", " |  4. Ways of calling an indicator.\n", " |  4a. Standard: Calling just the APO indicator without \"ta\" DataFrame extension.\n", " |  >>> ta.apo(df[\"close\"])\n", " |  4b. DataFrame Extension: Calling just the APO indicator with \"ta\" DataFrame extension.\n", " |  >>> df.ta.apo()\n", " |  4c. DataFrame Extension (kind): Calling APO using 'kind'\n", " |  >>> df.ta(kind=\"apo\")\n", " |  4d. Strategy:\n", " |  >>> df.ta.strategy(\"All\") # Default\n", " |  >>> df.ta.strategy(ta.Strategy(\"My Strat\", ta=[{\"kind\": \"apo\"}])) # Custom\n", " |  \n", " |  5. Working with kwargs\n", " |  5a. Append the result to the working df.\n", " |  >>> df.ta.apo(append=True)\n", " |  5b. Timing an indicator.\n", " |  >>> apo = df.ta(kind=\"apo\", timed=True)\n", " |  >>> print(apo.timed)\n", " |  \n", " |  Method resolution order:\n", " |      AnalysisIndicators\n", " |      BasePandasObject\n", " |      pandas.core.base.PandasObject\n", " |      pandas.core.accessor.DirNamesMixin\n", " |      builtins.object\n", " |  \n", " |  Methods defined here:\n", " |  \n", " |  __call__(self, kind: str = None, timed: bool = False, version: bool = False, **kwargs)\n", " |      Call self as a function.\n", " |  \n", " |  __init__(self, pandas_obj)\n", " |      Initialize self.  See help(type(self)) for accurate signature.\n", " |  \n", " |  aberration(self, length=None, atr_length=None, offset=None, **kwargs)\n", " |      # Volatility\n", " |  \n", " |  above(self, asint=True, offset=None, **kwargs)\n", " |      # Utility\n", " |  \n", " |  above_value(self, value=None, asint=True, offset=None, **kwargs)\n", " |  \n", " |  accbands(self, length=None, c=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  ad(self, open_=None, signed=True, offset=None, **kwargs)\n", " |      # Volume\n", " |  \n", " |  adosc(self, open_=None, fast=None, slow=None, signed=True, offset=None, **kwargs)\n", " |  \n", " |  adx(self, length=None, lensig=None, mamode=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |      # Trend\n", " |  \n", " |  alma(self, length=None, sigma=None, distribution_offset=None, offset=None, **kwargs)\n", " |      # Overlap\n", " |  \n", " |  amat(self, fast=None, slow=None, mamode=None, lookback=None, offset=None, **kwargs)\n", " |  \n", " |  ao(self, fast=None, slow=None, offset=None, **kwargs)\n", " |      # Momentum\n", " |  \n", " |  aobv(self, fast=None, slow=None, mamode=None, max_lookback=None, min_lookback=None, offset=None, **kwargs)\n", " |  \n", " |  apo(self, fast=None, slow=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  aroon(self, length=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  atr(self, length=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  bbands(self, length=None, std=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  below(self, asint=True, offset=None, **kwargs)\n", " |  \n", " |  below_value(self, value=None, asint=True, offset=None, **kwargs)\n", " |  \n", " |  bias(self, length=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  bop(self, percentage=False, offset=None, **kwargs)\n", " |  \n", " |  brar(self, length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  cci(self, length=None, c=None, offset=None, **kwargs)\n", " |  \n", " |  cdl_pattern(self, name='all', offset=None, **kwargs)\n", " |      # Public DataFrame Methods: Indicators and Utilities\n", " |      # Candles\n", " |  \n", " |  cdl_z(self, full=None, offset=None, **kwargs)\n", " |  \n", " |  cfo(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  cg(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  chop(self, length=None, atr_length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  cksp(self, p=None, x=None, q=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  cmf(self, open_=None, length=None, offset=None, **kwargs)\n", " |  \n", " |  cmo(self, length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  constants(self, append: bool, values: list)\n", " |      Constants\n", " |      \n", " |      Add or remove constants to the DataFrame easily with <PERSON><PERSON><PERSON>'s arrays or\n", " |      lists. Useful when you need easily accessible horizontal lines for\n", " |      charting.\n", " |      \n", " |      Add constant '1' to the DataFrame\n", " |      >>> df.ta.constants(True, [1])\n", " |      Remove constant '1' to the DataFrame\n", " |      >>> df.ta.constants(False, [1])\n", " |      \n", " |      Adding constants for charting\n", " |      >>> import numpy as np\n", " |      >>> chart_lines = np.append(np.arange(-4, 5, 1), np.arange(-100, 110, 10))\n", " |      >>> df.ta.constants(True, chart_lines)\n", " |      Removing some constants from the DataFrame\n", " |      >>> df.ta.constants(False, np.array([-60, -40, 40, 60]))\n", " |      \n", " |      Args:\n", " |          append (bool): If True, appends a Numpy range of constants to the\n", " |              working DataFrame.  If False, it removes the constant range from\n", " |              the working DataFrame. Default: None.\n", " |      \n", " |      Returns:\n", " |          Returns the appended constants\n", " |          Returns nothing to the user.  Either adds or removes constant ranges\n", " |          from the working DataFrame.\n", " |  \n", " |  coppock(self, length=None, fast=None, slow=None, offset=None, **kwargs)\n", " |  \n", " |  cross(self, above=True, asint=True, offset=None, **kwargs)\n", " |  \n", " |  cross_value(self, value=None, above=True, asint=True, offset=None, **kwargs)\n", " |  \n", " |  cti(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  decay(self, length=None, mode=None, offset=None, **kwargs)\n", " |  \n", " |  decreasing(self, length=None, strict=None, asint=None, offset=None, **kwargs)\n", " |  \n", " |  dema(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  dm(self, drift=None, offset=None, mamode=None, **kwargs)\n", " |  \n", " |  donchian(self, lower_length=None, upper_length=None, offset=None, **kwargs)\n", " |  \n", " |  dpo(self, length=None, centered=True, offset=None, **kwargs)\n", " |  \n", " |  ebsw(self, close=None, length=None, bars=None, offset=None, **kwargs)\n", " |      # Cycles\n", " |  \n", " |  efi(self, length=None, mamode=None, offset=None, drift=None, **kwargs)\n", " |  \n", " |  ema(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  entropy(self, length=None, base=None, offset=None, **kwargs)\n", " |      # Statistics\n", " |  \n", " |  eom(self, length=None, divisor=None, offset=None, drift=None, **kwargs)\n", " |  \n", " |  er(self, length=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  eri(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  fisher(self, length=None, signal=None, offset=None, **kwargs)\n", " |  \n", " |  fwma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  ha(self, offset=None, **kwargs)\n", " |  \n", " |  hilo(self, high_length=None, low_length=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  hl2(self, offset=None, **kwargs)\n", " |  \n", " |  hlc3(self, offset=None, **kwargs)\n", " |  \n", " |  hma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  hwc(self, na=None, nb=None, nc=None, nd=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  hwma(self, na=None, nb=None, nc=None, offset=None, **kwargs)\n", " |  \n", " |  ichi<PERSON>ku(self, tenkan=None, kijun=None, senkou=None, include_chikou=True, offset=None, **kwargs)\n", " |  \n", " |  increasing(self, length=None, strict=None, asint=None, offset=None, **kwargs)\n", " |  \n", " |  indicators(self, **kwargs)\n", " |      List of Indicators\n", " |      \n", " |      kwargs:\n", " |          as_list (bool, optional): When True, it returns a list of the\n", " |              indicators. Default: False.\n", " |          exclude (list, optional): The passed in list will be excluded\n", " |              from the indicators list. Default: None.\n", " |      \n", " |      Returns:\n", " |          Prints the list of indicators. If as_list=True, then a list.\n", " |  \n", " |  inertia(self, length=None, rvi_length=None, scalar=None, refined=None, thirds=None, mamode=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  jma(self, length=None, phase=None, offset=None, **kwargs)\n", " |  \n", " |  kama(self, length=None, fast=None, slow=None, offset=None, **kwargs)\n", " |  \n", " |  kc(self, length=None, scalar=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  kdj(self, length=None, signal=None, offset=None, **kwargs)\n", " |  \n", " |  kst(self, roc1=None, roc2=None, roc3=None, roc4=None, sma1=None, sma2=None, sma3=None, sma4=None, signal=None, offset=None, **kwargs)\n", " |  \n", " |  kurtosis(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  kvo(self, fast=None, slow=None, length_sig=None, mamode=None, offset=None, drift=None, **kwargs)\n", " |  \n", " |  linreg(self, length=None, offset=None, adjust=None, **kwargs)\n", " |  \n", " |  log_return(self, length=None, cumulative=False, percent=False, offset=None, **kwargs)\n", " |      # Performance\n", " |  \n", " |  long_run(self, fast=None, slow=None, length=None, offset=None, **kwargs)\n", " |  \n", " |  macd(self, fast=None, slow=None, signal=None, offset=None, **kwargs)\n", " |  \n", " |  mad(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  massi(self, fast=None, slow=None, offset=None, **kwargs)\n", " |  \n", " |  mcgd(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  median(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  mfi(self, length=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  midpoint(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  midprice(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  mom(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  natr(self, length=None, mamode=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  nvi(self, length=None, initial=None, signed=True, offset=None, **kwargs)\n", " |  \n", " |  obv(self, offset=None, **kwargs)\n", " |  \n", " |  ohlc4(self, offset=None, **kwargs)\n", " |  \n", " |  pdist(self, drift=None, offset=None, **kwargs)\n", " |  \n", " |  percent_return(self, length=None, cumulative=False, percent=False, offset=None, **kwargs)\n", " |  \n", " |  pgo(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  ppo(self, fast=None, slow=None, scalar=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  psar(self, af0=None, af=None, max_af=None, offset=None, **kwargs)\n", " |  \n", " |  psl(self, open_=None, length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  pvi(self, length=None, initial=None, signed=True, offset=None, **kwargs)\n", " |  \n", " |  pvo(self, fast=None, slow=None, signal=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  pvol(self, volume=None, offset=None, **kwargs)\n", " |  \n", " |  pvr(self, **kwargs)\n", " |  \n", " |  pvt(self, offset=None, **kwargs)\n", " |  \n", " |  pwma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  qqe(self, length=None, smooth=None, factor=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  qstick(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  quantile(self, length=None, q=None, offset=None, **kwargs)\n", " |  \n", " |  rma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  roc(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  rsi(self, length=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  rsx(self, length=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  rvgi(self, length=None, swma_length=None, offset=None, **kwargs)\n", " |  \n", " |  rvi(self, length=None, scalar=None, refined=None, thirds=None, mamode=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  short_run(self, fast=None, slow=None, length=None, offset=None, **kwargs)\n", " |  \n", " |  sinwma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  skew(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  slope(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  sma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  smi(self, fast=None, slow=None, signal=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  squeeze(self, bb_length=None, bb_std=None, kc_length=None, kc_scalar=None, mom_length=None, mom_smooth=None, use_tr=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  squeeze_pro(self, bb_length=None, bb_std=None, kc_length=None, kc_scalar_wide=None, kc_scalar_normal=None, kc_scalar_narrow=None, mom_length=None, mom_smooth=None, use_tr=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  ssf(self, length=None, poles=None, offset=None, **kwargs)\n", " |  \n", " |  stc(self, ma1=None, ma2=None, osc=None, tclength=None, fast=None, slow=None, factor=None, offset=None, **kwargs)\n", " |  \n", " |  stdev(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  stoch(self, fast_k=None, slow_k=None, slow_d=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  stochrsi(self, length=None, rsi_length=None, k=None, d=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  strategy(self, *args, **kwargs)\n", " |      Strategy Method\n", " |      \n", " |      An experimental method that by default runs all applicable indicators.\n", " |      Future implementations will allow more specific indicator generation\n", " |      with possibly as json, yaml config file or an sqlite3 table.\n", " |      \n", " |      \n", " |      Kwargs:\n", " |          chunksize (bool): Adjust the chunksize for the Multiprocessing Pool.\n", " |              Default: Number of cores of the OS\n", " |          exclude (list): List of indicator names to exclude. Some are\n", " |              excluded by default for various reasons; they require additional\n", " |              sources, performance (td_seq), not a ohlcv chart (vp) etc.\n", " |          name (str): Select all indicators or indicators by\n", " |              Category such as: \"candles\", \"cycles\", \"momentum\", \"overlap\",\n", " |              \"performance\", \"statistics\", \"trend\", \"volatility\", \"volume\", or\n", " |              \"all\". De<PERSON>ult: \"all\"\n", " |          ordered (bool): Whether to run \"all\" in order. Default: True\n", " |          timed (bool): Show the process time of the strategy().\n", " |              Default: False\n", " |          verbose (bool): Provide some additional insight on the progress of\n", " |              the strategy() execution. Default: False\n", " |  \n", " |  supertrend(self, period=None, multiplier=None, mamode=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  swma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  t3(self, length=None, a=None, offset=None, **kwargs)\n", " |  \n", " |  td_seq(self, asint=None, offset=None, show_all=None, **kwargs)\n", " |  \n", " |  tema(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  thermo(self, long=None, short=None, length=None, mamode=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  ticker(self, ticker: str, **kwargs)\n", " |      ticker\n", " |      \n", " |      This method downloads Historical Data if the package yfinance is installed.\n", " |      Additionally it can run a ta.Strategy; Builtin or Custom. It returns a\n", " |      DataFrame if there the DataFrame is not empty, otherwise it exits. For\n", " |      additional yfinance arguments, use help(ta.yf).\n", " |      \n", " |      Historical Data\n", " |      >>> df = df.ta.ticker(\"aapl\")\n", " |      More specifically\n", " |      >>> df = df.ta.ticker(\"aapl\", period=\"max\", interval=\"1d\", kind=None)\n", " |      \n", " |      Changing the period of Historical Data\n", " |      Period is used instead of start/end\n", " |      >>> df = df.ta.ticker(\"aapl\", period=\"1y\")\n", " |      \n", " |      Changing the period and interval of Historical Data\n", " |      Retrieves the past year in weeks\n", " |      >>> df = df.ta.ticker(\"aapl\", period=\"1y\", interval=\"1wk\")\n", " |      Retrieves the past month in hours\n", " |      >>> df = df.ta.ticker(\"aapl\", period=\"1mo\", interval=\"1h\")\n", " |      \n", " |      Show everything\n", " |      >>> df = df.ta.ticker(\"aapl\", kind=\"all\")\n", " |      \n", " |      Args:\n", " |          ticker (str): Any string for a ticker you would use with yfinance.\n", " |              Default: \"SPY\"\n", " |      Kwargs:\n", " |          kind (str): Options see above. Default: \"history\"\n", " |          ds (str): Data Source to use. Default: \"yahoo\"\n", " |          strategy (str | ta.Strategy): Which strategy to apply after\n", " |              downloading chart history. Default: None\n", " |      \n", " |          See help(ta.yf) for additional kwargs\n", " |      \n", " |      Returns:\n", " |          Exits if the DataFrame is empty or None\n", " |          Otherwise it returns a DataFrame\n", " |  \n", " |  tos_stdevall(self, length=None, stds=None, offset=None, **kwargs)\n", " |  \n", " |  trima(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  trix(self, length=None, signal=None, scalar=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  true_range(self, drift=None, offset=None, **kwargs)\n", " |  \n", " |  tsi(self, fast=None, slow=None, drift=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  tsignals(self, trend=None, asbool=None, trend_reset=None, trend_offset=None, offset=None, **kwargs)\n", " |  \n", " |  ttm_trend(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  ui(self, length=None, scalar=None, offset=None, **kwargs)\n", " |  \n", " |  uo(self, fast=None, medium=None, slow=None, fast_w=None, medium_w=None, slow_w=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  variance(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  vhf(self, length=None, drift=None, offset=None, **kwargs)\n", " |  \n", " |  vidya(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  vortex(self, drift=None, offset=None, **kwargs)\n", " |  \n", " |  vp(self, width=None, percent=None, **kwargs)\n", " |  \n", " |  vwap(self, anchor=None, offset=None, **kwargs)\n", " |  \n", " |  vwma(self, volume=None, length=None, offset=None, **kwargs)\n", " |  \n", " |  wcp(self, offset=None, **kwargs)\n", " |  \n", " |  willr(self, length=None, percentage=True, offset=None, **kwargs)\n", " |  \n", " |  wma(self, length=None, offset=None, **kwargs)\n", " |  \n", " |  xsignals(self, signal=None, xa=None, xb=None, above=None, long=None, asbool=None, trend_reset=None, trend_offset=None, offset=None, **kwargs)\n", " |  \n", " |  zlma(self, length=None, mamode=None, offset=None, **kwargs)\n", " |  \n", " |  zscore(self, length=None, std=None, offset=None, **kwargs)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Readonly properties defined here:\n", " |  \n", " |  categories\n", " |      Returns the categories.\n", " |  \n", " |  datetime_ordered\n", " |      Returns True if the index is a datetime and ordered.\n", " |  \n", " |  last_run\n", " |      Returns the time when the DataFrame was last run.\n", " |  \n", " |  reverse\n", " |      Reverses the DataFrame. Simply: df.iloc[::-1]\n", " |  \n", " |  to_utc\n", " |      Sets the DataFrame index to UTC format\n", " |  \n", " |  version\n", " |      Returns the version.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data descriptors defined here:\n", " |  \n", " |  adjusted\n", " |      property: df.ta.adjusted\n", " |  \n", " |  cores\n", " |      Returns the categories.\n", " |  \n", " |  exchange\n", " |      Returns the current Exchange. Default: \"NYSE\".\n", " |  \n", " |  time_range\n", " |      Returns the time ranges of the DataFrame as a float. Default is in \"years\". help(ta.toal_time)\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from pandas.core.base.PandasObject:\n", " |  \n", " |  __repr__(self) -> 'str'\n", " |      Return a string representation for a particular object.\n", " |  \n", " |  __sizeof__(self) -> 'int'\n", " |      Generates the total memory usage for an object that returns\n", " |      either a value or Series of values\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data and other attributes inherited from pandas.core.base.PandasObject:\n", " |  \n", " |  __annotations__ = {'_cache': 'dict[str, Any]'}\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Methods inherited from pandas.core.accessor.DirNamesMixin:\n", " |  \n", " |  __dir__(self) -> 'list[str]'\n", " |      Provide method name lookup and completion.\n", " |      \n", " |      Notes\n", " |      -----\n", " |      Only provide 'public' methods.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data descriptors inherited from pandas.core.accessor.DirNamesMixin:\n", " |  \n", " |  __dict__\n", " |      dictionary for instance variables (if defined)\n", " |  \n", " |  __weakref__\n", " |      list of weak references to the object (if defined)\n", "\n"]}], "source": ["help(df.ta)"]}, {"cell_type": "code", "execution_count": 11, "id": "ba60a3a2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pandas TA - Technical Analysis Indicators - v0.3.14b0\n", "Total Indicators & Utilities: 205\n", "Abbreviations:\n", "    aberration, above, above_value, accbands, ad, adosc, adx, alma, amat, ao, aobv, apo, aroon, atr, bbands, below, below_value, bias, bop, brar, cci, cdl_pattern, cdl_z, cfo, cg, chop, cksp, cmf, cmo, coppock, cross, cross_value, cti, decay, decreasing, dema, dm, donchian, dpo, ebsw, efi, ema, entropy, eom, er, eri, fisher, fwma, ha, hilo, hl2, hlc3, hma, hwc, hwma, ichimoku, increasing, inertia, jma, kama, kc, kdj, kst, kurtosis, kvo, linreg, log_return, long_run, macd, mad, massi, mcgd, median, mfi, midpoint, midprice, mom, natr, nvi, obv, ohlc4, pdist, percent_return, pgo, ppo, psar, psl, pvi, pvo, pvol, pvr, pvt, pwma, qqe, qstick, quantile, rma, roc, rsi, rsx, rvgi, rvi, short_run, sinwma, skew, slope, sma, smi, squeeze, squeeze_pro, ssf, stc, stdev, stoch, stochrsi, supertrend, swma, t3, td_seq, tema, thermo, tos_stdevall, trima, trix, true_range, tsi, tsignals, ttm_trend, ui, uo, variance, vhf, vidya, vortex, vp, vwap, vwma, wcp, willr, wma, xsignals, zlma, zscore\n", "\n", "Candle Patterns:\n", "    2crows, 3blackcrows, 3inside, 3linestrike, 3outside, 3starsinsouth, 3whitesoldiers, abandonedbaby, advanceblock, belthold, breakaway, closingmar<PERSON>ozu, concealbabyswall, counterattack, darkcloudcover, doji, dojistar, dragonflydoji, engulfing, eveningdojistar, eveningstar, gapsidesidewhite, gravestonedoji, hammer, hangingman, harami, haramicross, highwave, hikkake, hikkakemod, homingpigeon, identical3crows, inneck, inside, invertedhammer, kicking, kickingbylength, ladderbottom, longleggeddoji, longline, marubozu, matchinglow, mathold, morningdojistar, morningstar, onneck, piercing, rickshawman, risefall3methods, separatinglines, shootingstar, shortline, spinningtop, stalledpattern, sticksandwich, takuri, tasukigap, thrusting, tristar, unique3river, upsidegap2crows, xsidegap3methods\n", "Help on NoneType object:\n", "\n", "class NoneType(object)\n", " |  Methods defined here:\n", " |  \n", " |  __bool__(self, /)\n", " |      self != 0\n", " |  \n", " |  __repr__(self, /)\n", " |      Return repr(self).\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Static methods defined here:\n", " |  \n", " |  __new__(*args, **kwargs) from builtins.type\n", " |      Create and return a new object.  See help(type) for accurate signature.\n", "\n"]}], "source": ["help(df.ta.indicators())"]}, {"cell_type": "code", "execution_count": 18, "id": "00a1a2ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function cci in module pandas_ta.momentum.cci:\n", "\n", "cci(high, low, close, length=None, c=None, talib=None, offset=None, **kwargs)\n", "    Commodity Channel Index (CCI)\n", "    \n", "    Commodity Channel Index is a momentum oscillator used to primarily identify\n", "    overbought and oversold levels relative to a mean.\n", "    \n", "    Sources:\n", "        https://www.tradingview.com/wiki/Commodity_Channel_Index_(CCI)\n", "    \n", "    Calculation:\n", "        Default Inputs:\n", "            length=14, c=0.015\n", "        SMA = Simple Moving Average\n", "        MAD = Mean Absolute Deviation\n", "        tp = typical_price = hlc3 = (high + low + close) / 3\n", "        mean_tp = SMA(tp, length)\n", "        mad_tp = MAD(tp, length)\n", "        CCI = (tp - mean_tp) / (c * mad_tp)\n", "    \n", "    Args:\n", "        high (pd.Series): Series of 'high's\n", "        low (pd.Series): Series of 'low's\n", "        close (pd.Series): Series of 'close's\n", "        length (int): It's period. Default: 14\n", "        c (float): <PERSON><PERSON>stant. Default: 0.015\n", "        talib (bool): If TA Lib is installed and talib is True, Returns the TA Lib\n", "            version. Default: True\n", "        offset (int): How many periods to offset the result. Default: 0\n", "    \n", "    Kwargs:\n", "        fillna (value, optional): pd.DataFrame.fillna(value)\n", "        fill_method (value, optional): Type of fill method\n", "    \n", "    Returns:\n", "        pd.Series: New feature generated.\n", "\n"]}], "source": ["help(ta.cci)"]}, {"cell_type": "markdown", "id": "027e7fa6", "metadata": {}, "source": ["# Three Layers of Abstraction"]}, {"cell_type": "markdown", "id": "eacac5c7", "metadata": {}, "source": ["## 1. Directly calling function"]}, {"cell_type": "code", "execution_count": 14, "id": "1fe51fd9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function rsi in module pandas_ta.momentum.rsi:\n", "\n", "rsi(close, length=None, scalar=None, talib=None, drift=None, offset=None, **kwargs)\n", "    Relative Strength Index (RSI)\n", "    \n", "    The Relative Strength Index is popular momentum oscillator used to measure the\n", "    velocity as well as the magnitude of directional price movements.\n", "    \n", "    Sources:\n", "        https://www.tradingview.com/wiki/Relative_Strength_Index_(RSI)\n", "    \n", "    Calculation:\n", "        Default Inputs:\n", "            length=14, scalar=100, drift=1\n", "        ABS = Absolute Value\n", "        RMA = Rolling Moving Average\n", "    \n", "        diff = close.diff(drift)\n", "        positive = diff if diff > 0 else 0\n", "        negative = diff if diff < 0 else 0\n", "    \n", "        pos_avg = RMA(positive, length)\n", "        neg_avg = ABS(RMA(negative, length))\n", "    \n", "        RSI = scalar * pos_avg / (pos_avg + neg_avg)\n", "    \n", "    Args:\n", "        close (pd.Series): Series of 'close's\n", "        length (int): It's period. Default: 14\n", "        scalar (float): How much to magnify. Default: 100\n", "        talib (bool): If TA Lib is installed and talib is True, Returns the TA Lib\n", "            version. Default: True\n", "        drift (int): The difference period. Default: 1\n", "        offset (int): How many periods to offset the result. Default: 0\n", "    \n", "    Kwargs:\n", "        fillna (value, optional): pd.DataFrame.fillna(value)\n", "        fill_method (value, optional): Type of fill method\n", "    \n", "    Returns:\n", "        pd.Series: New feature generated.\n", "\n"]}], "source": ["help(ta.rsi)"]}, {"cell_type": "code", "execution_count": 20, "id": "bc9c0c78", "metadata": {}, "outputs": [{"data": {"text/plain": ["2022-09-08 09:30:00-04:00          NaN\n", "2022-09-08 10:30:00-04:00          NaN\n", "2022-09-08 11:30:00-04:00          NaN\n", "2022-09-08 12:30:00-04:00          NaN\n", "2022-09-08 13:30:00-04:00          NaN\n", "                               ...    \n", "2022-10-07 12:30:00-04:00    48.768181\n", "2022-10-07 13:30:00-04:00    48.405843\n", "2022-10-07 14:30:00-04:00    49.013101\n", "2022-10-07 15:30:00-04:00    50.227033\n", "2022-10-07 16:00:00-04:00    47.023899\n", "Name: RSI_40, Length: 155, dtype: float64"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["rsi = ta.rsi(close = df[\"Volume\"], length = 40)\n", "rsi"]}, {"cell_type": "code", "execution_count": 21, "id": "611b94b2", "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>rsi</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-09-08 09:30:00-04:00</th>\n", "      <td>154.639999</td>\n", "      <td>155.839996</td>\n", "      <td>154.369995</td>\n", "      <td>154.919998</td>\n", "      <td>20423405</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 10:30:00-04:00</th>\n", "      <td>154.910004</td>\n", "      <td>156.360001</td>\n", "      <td>154.700195</td>\n", "      <td>155.990005</td>\n", "      <td>9445752</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 11:30:00-04:00</th>\n", "      <td>155.985001</td>\n", "      <td>156.190002</td>\n", "      <td>152.679993</td>\n", "      <td>153.149902</td>\n", "      <td>12280244</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 12:30:00-04:00</th>\n", "      <td>153.149994</td>\n", "      <td>154.020004</td>\n", "      <td>152.695007</td>\n", "      <td>153.733505</td>\n", "      <td>10943346</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 13:30:00-04:00</th>\n", "      <td>153.740005</td>\n", "      <td>154.139999</td>\n", "      <td>153.229996</td>\n", "      <td>153.789993</td>\n", "      <td>7342717</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 12:30:00-04:00</th>\n", "      <td>140.669998</td>\n", "      <td>140.720001</td>\n", "      <td>139.940002</td>\n", "      <td>140.279999</td>\n", "      <td>7685803</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>41.152446</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 13:30:00-04:00</th>\n", "      <td>140.145004</td>\n", "      <td>140.600006</td>\n", "      <td>140.035004</td>\n", "      <td>140.077896</td>\n", "      <td>6175166</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.910621</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 14:30:00-04:00</th>\n", "      <td>140.070007</td>\n", "      <td>140.535004</td>\n", "      <td>139.600006</td>\n", "      <td>139.721893</td>\n", "      <td>8536199</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.480855</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 15:30:00-04:00</th>\n", "      <td>139.720001</td>\n", "      <td>140.490005</td>\n", "      <td>139.445007</td>\n", "      <td>140.080002</td>\n", "      <td>13306374</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>41.119013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 16:00:00-04:00</th>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>41.137078</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>155 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "2022-09-08 09:30:00-04:00  154.639999  155.839996  154.369995  154.919998   \n", "2022-09-08 10:30:00-04:00  154.910004  156.360001  154.700195  155.990005   \n", "2022-09-08 11:30:00-04:00  155.985001  156.190002  152.679993  153.149902   \n", "2022-09-08 12:30:00-04:00  153.149994  154.020004  152.695007  153.733505   \n", "2022-09-08 13:30:00-04:00  153.740005  154.139999  153.229996  153.789993   \n", "...                               ...         ...         ...         ...   \n", "2022-10-07 12:30:00-04:00  140.669998  140.720001  139.940002  140.279999   \n", "2022-10-07 13:30:00-04:00  140.145004  140.600006  140.035004  140.077896   \n", "2022-10-07 14:30:00-04:00  140.070007  140.535004  139.600006  139.721893   \n", "2022-10-07 15:30:00-04:00  139.720001  140.490005  139.445007  140.080002   \n", "2022-10-07 16:00:00-04:00  140.089996  140.089996  140.089996  140.089996   \n", "\n", "                             Volume  Dividends  Stock Splits        rsi  \n", "2022-09-08 09:30:00-04:00  20423405          0             0        NaN  \n", "2022-09-08 10:30:00-04:00   9445752          0             0        NaN  \n", "2022-09-08 11:30:00-04:00  12280244          0             0        NaN  \n", "2022-09-08 12:30:00-04:00  10943346          0             0        NaN  \n", "2022-09-08 13:30:00-04:00   7342717          0             0        NaN  \n", "...                             ...        ...           ...        ...  \n", "2022-10-07 12:30:00-04:00   7685803          0             0  41.152446  \n", "2022-10-07 13:30:00-04:00   6175166          0             0  40.910621  \n", "2022-10-07 14:30:00-04:00   8536199          0             0  40.480855  \n", "2022-10-07 15:30:00-04:00  13306374          0             0  41.119013  \n", "2022-10-07 16:00:00-04:00         0          0             0  41.137078  \n", "\n", "[155 rows x 8 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"rsi\"] = ta.rsi(close = df[\"Close\"], length = 40)\n", "df"]}, {"cell_type": "markdown", "id": "469d22d5", "metadata": {}, "source": ["## 2. Using dataframe accessor"]}, {"cell_type": "code", "execution_count": 22, "id": "630506f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function bbands in module pandas_ta.volatility.bbands:\n", "\n", "bbands(close, length=None, std=None, ddof=0, mamode=None, talib=None, offset=None, **kwargs)\n", "    Bollinger Bands (BBANDS)\n", "    \n", "    A popular volatility indicator by <PERSON>.\n", "    \n", "    Sources:\n", "        https://www.tradingview.com/wiki/Bollinger_Bands_(BB)\n", "    \n", "    Calculation:\n", "        Default Inputs:\n", "            length=5, std=2, mamode=\"sma\", ddof=0\n", "        EMA = Exponential Moving Average\n", "        SMA = Simple Moving Average\n", "        STDEV = Standard Deviation\n", "        stdev = STDEV(close, length, ddof)\n", "        if \"ema\":\n", "            MID = EMA(close, length)\n", "        else:\n", "            MID = SMA(close, length)\n", "    \n", "        LOWER = MID - std * stdev\n", "        UPPER = MID + std * stdev\n", "    \n", "        BANDWIDTH = 100 * (UPPER - LOWER) / MID\n", "        PERCENT = (close - LOWER) / (UPPER - LOWER)\n", "    \n", "    Args:\n", "        close (pd.Series): Series of 'close's\n", "        length (int): The short period. Default: 5\n", "        std (int): The long period. Default: 2\n", "        ddof (int): Degrees of Freedom to use. Default: 0\n", "        mamode (str): See ```help(ta.ma)```. De<PERSON><PERSON>: 'sma'\n", "        talib (bool): If TA Lib is installed and talib is True, Returns the TA Lib\n", "            version. Default: True\n", "        offset (int): How many periods to offset the result. Default: 0\n", "    \n", "    Kwargs:\n", "        fillna (value, optional): pd.DataFrame.fillna(value)\n", "        fill_method (value, optional): Type of fill method\n", "    \n", "    Returns:\n", "        pd.DataFrame: lower, mid, upper, bandwidth, and percent columns.\n", "\n"]}], "source": ["help(ta.bbands)"]}, {"cell_type": "code", "execution_count": 23, "id": "5645cef2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BBL_5_2.0</th>\n", "      <th>BBM_5_2.0</th>\n", "      <th>BBU_5_2.0</th>\n", "      <th>BBB_5_2.0</th>\n", "      <th>BBP_5_2.0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-09-08 09:30:00-04:00</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 10:30:00-04:00</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 11:30:00-04:00</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 12:30:00-04:00</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 13:30:00-04:00</th>\n", "      <td>152.288308</td>\n", "      <td>154.316681</td>\n", "      <td>156.345054</td>\n", "      <td>2.628845</td>\n", "      <td>0.370170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 12:30:00-04:00</th>\n", "      <td>137.967317</td>\n", "      <td>141.759000</td>\n", "      <td>145.550682</td>\n", "      <td>5.349477</td>\n", "      <td>0.304968</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 13:30:00-04:00</th>\n", "      <td>139.773876</td>\n", "      <td>140.676578</td>\n", "      <td>141.579279</td>\n", "      <td>1.283371</td>\n", "      <td>0.168394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 14:30:00-04:00</th>\n", "      <td>139.360636</td>\n", "      <td>140.396957</td>\n", "      <td>141.433279</td>\n", "      <td>1.476274</td>\n", "      <td>0.174298</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 15:30:00-04:00</th>\n", "      <td>139.544406</td>\n", "      <td>140.166959</td>\n", "      <td>140.789511</td>\n", "      <td>0.888301</td>\n", "      <td>0.430161</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 16:00:00-04:00</th>\n", "      <td>139.687928</td>\n", "      <td>140.049957</td>\n", "      <td>140.411986</td>\n", "      <td>0.517000</td>\n", "      <td>0.555298</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>155 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                            BBL_5_2.0   BBM_5_2.0   BBU_5_2.0  BBB_5_2.0  \\\n", "2022-09-08 09:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 10:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 11:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 12:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 13:30:00-04:00  152.288308  154.316681  156.345054   2.628845   \n", "...                               ...         ...         ...        ...   \n", "2022-10-07 12:30:00-04:00  137.967317  141.759000  145.550682   5.349477   \n", "2022-10-07 13:30:00-04:00  139.773876  140.676578  141.579279   1.283371   \n", "2022-10-07 14:30:00-04:00  139.360636  140.396957  141.433279   1.476274   \n", "2022-10-07 15:30:00-04:00  139.544406  140.166959  140.789511   0.888301   \n", "2022-10-07 16:00:00-04:00  139.687928  140.049957  140.411986   0.517000   \n", "\n", "                           BBP_5_2.0  \n", "2022-09-08 09:30:00-04:00        NaN  \n", "2022-09-08 10:30:00-04:00        NaN  \n", "2022-09-08 11:30:00-04:00        NaN  \n", "2022-09-08 12:30:00-04:00        NaN  \n", "2022-09-08 13:30:00-04:00   0.370170  \n", "...                              ...  \n", "2022-10-07 12:30:00-04:00   0.304968  \n", "2022-10-07 13:30:00-04:00   0.168394  \n", "2022-10-07 14:30:00-04:00   0.174298  \n", "2022-10-07 15:30:00-04:00   0.430161  \n", "2022-10-07 16:00:00-04:00   0.555298  \n", "\n", "[155 rows x 5 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df.ta.bbands()"]}, {"cell_type": "code", "execution_count": 26, "id": "b419e87a", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>rsi</th>\n", "      <th>BBL_5_2.0</th>\n", "      <th>BBM_5_2.0</th>\n", "      <th>BBU_5_2.0</th>\n", "      <th>BBB_5_2.0</th>\n", "      <th>BBP_5_2.0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-09-08 09:30:00-04:00</th>\n", "      <td>154.639999</td>\n", "      <td>155.839996</td>\n", "      <td>154.369995</td>\n", "      <td>154.919998</td>\n", "      <td>20423405</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 10:30:00-04:00</th>\n", "      <td>154.910004</td>\n", "      <td>156.360001</td>\n", "      <td>154.700195</td>\n", "      <td>155.990005</td>\n", "      <td>9445752</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 11:30:00-04:00</th>\n", "      <td>155.985001</td>\n", "      <td>156.190002</td>\n", "      <td>152.679993</td>\n", "      <td>153.149902</td>\n", "      <td>12280244</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 12:30:00-04:00</th>\n", "      <td>153.149994</td>\n", "      <td>154.020004</td>\n", "      <td>152.695007</td>\n", "      <td>153.733505</td>\n", "      <td>10943346</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 13:30:00-04:00</th>\n", "      <td>153.740005</td>\n", "      <td>154.139999</td>\n", "      <td>153.229996</td>\n", "      <td>153.789993</td>\n", "      <td>7342717</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>152.288308</td>\n", "      <td>154.316681</td>\n", "      <td>156.345054</td>\n", "      <td>2.628845</td>\n", "      <td>0.370170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 12:30:00-04:00</th>\n", "      <td>140.669998</td>\n", "      <td>140.720001</td>\n", "      <td>139.940002</td>\n", "      <td>140.279999</td>\n", "      <td>7685803</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>41.152446</td>\n", "      <td>137.967317</td>\n", "      <td>141.759000</td>\n", "      <td>145.550682</td>\n", "      <td>5.349477</td>\n", "      <td>0.304968</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 13:30:00-04:00</th>\n", "      <td>140.145004</td>\n", "      <td>140.600006</td>\n", "      <td>140.035004</td>\n", "      <td>140.077896</td>\n", "      <td>6175166</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.910621</td>\n", "      <td>139.773876</td>\n", "      <td>140.676578</td>\n", "      <td>141.579279</td>\n", "      <td>1.283371</td>\n", "      <td>0.168394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 14:30:00-04:00</th>\n", "      <td>140.070007</td>\n", "      <td>140.535004</td>\n", "      <td>139.600006</td>\n", "      <td>139.721893</td>\n", "      <td>8536199</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.480855</td>\n", "      <td>139.360636</td>\n", "      <td>140.396957</td>\n", "      <td>141.433279</td>\n", "      <td>1.476274</td>\n", "      <td>0.174298</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 15:30:00-04:00</th>\n", "      <td>139.720001</td>\n", "      <td>140.490005</td>\n", "      <td>139.445007</td>\n", "      <td>140.080002</td>\n", "      <td>13306374</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>41.119013</td>\n", "      <td>139.544406</td>\n", "      <td>140.166959</td>\n", "      <td>140.789511</td>\n", "      <td>0.888301</td>\n", "      <td>0.430161</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 16:00:00-04:00</th>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>41.137078</td>\n", "      <td>139.687928</td>\n", "      <td>140.049957</td>\n", "      <td>140.411986</td>\n", "      <td>0.517000</td>\n", "      <td>0.555298</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>155 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "2022-09-08 09:30:00-04:00  154.639999  155.839996  154.369995  154.919998   \n", "2022-09-08 10:30:00-04:00  154.910004  156.360001  154.700195  155.990005   \n", "2022-09-08 11:30:00-04:00  155.985001  156.190002  152.679993  153.149902   \n", "2022-09-08 12:30:00-04:00  153.149994  154.020004  152.695007  153.733505   \n", "2022-09-08 13:30:00-04:00  153.740005  154.139999  153.229996  153.789993   \n", "...                               ...         ...         ...         ...   \n", "2022-10-07 12:30:00-04:00  140.669998  140.720001  139.940002  140.279999   \n", "2022-10-07 13:30:00-04:00  140.145004  140.600006  140.035004  140.077896   \n", "2022-10-07 14:30:00-04:00  140.070007  140.535004  139.600006  139.721893   \n", "2022-10-07 15:30:00-04:00  139.720001  140.490005  139.445007  140.080002   \n", "2022-10-07 16:00:00-04:00  140.089996  140.089996  140.089996  140.089996   \n", "\n", "                             Volume  Dividends  Stock Splits        rsi  \\\n", "2022-09-08 09:30:00-04:00  20423405          0             0        NaN   \n", "2022-09-08 10:30:00-04:00   9445752          0             0        NaN   \n", "2022-09-08 11:30:00-04:00  12280244          0             0        NaN   \n", "2022-09-08 12:30:00-04:00  10943346          0             0        NaN   \n", "2022-09-08 13:30:00-04:00   7342717          0             0        NaN   \n", "...                             ...        ...           ...        ...   \n", "2022-10-07 12:30:00-04:00   7685803          0             0  41.152446   \n", "2022-10-07 13:30:00-04:00   6175166          0             0  40.910621   \n", "2022-10-07 14:30:00-04:00   8536199          0             0  40.480855   \n", "2022-10-07 15:30:00-04:00  13306374          0             0  41.119013   \n", "2022-10-07 16:00:00-04:00         0          0             0  41.137078   \n", "\n", "                            BBL_5_2.0   BBM_5_2.0   BBU_5_2.0  BBB_5_2.0  \\\n", "2022-09-08 09:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 10:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 11:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 12:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 13:30:00-04:00  152.288308  154.316681  156.345054   2.628845   \n", "...                               ...         ...         ...        ...   \n", "2022-10-07 12:30:00-04:00  137.967317  141.759000  145.550682   5.349477   \n", "2022-10-07 13:30:00-04:00  139.773876  140.676578  141.579279   1.283371   \n", "2022-10-07 14:30:00-04:00  139.360636  140.396957  141.433279   1.476274   \n", "2022-10-07 15:30:00-04:00  139.544406  140.166959  140.789511   0.888301   \n", "2022-10-07 16:00:00-04:00  139.687928  140.049957  140.411986   0.517000   \n", "\n", "                           BBP_5_2.0  \n", "2022-09-08 09:30:00-04:00        NaN  \n", "2022-09-08 10:30:00-04:00        NaN  \n", "2022-09-08 11:30:00-04:00        NaN  \n", "2022-09-08 12:30:00-04:00        NaN  \n", "2022-09-08 13:30:00-04:00   0.370170  \n", "...                              ...  \n", "2022-10-07 12:30:00-04:00   0.304968  \n", "2022-10-07 13:30:00-04:00   0.168394  \n", "2022-10-07 14:30:00-04:00   0.174298  \n", "2022-10-07 15:30:00-04:00   0.430161  \n", "2022-10-07 16:00:00-04:00   0.555298  \n", "\n", "[155 rows x 13 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 25, "id": "ec088c0d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BBL_5_2.0</th>\n", "      <th>BBM_5_2.0</th>\n", "      <th>BBU_5_2.0</th>\n", "      <th>BBB_5_2.0</th>\n", "      <th>BBP_5_2.0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-09-08 09:30:00-04:00</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 10:30:00-04:00</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 11:30:00-04:00</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 12:30:00-04:00</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 13:30:00-04:00</th>\n", "      <td>152.288308</td>\n", "      <td>154.316681</td>\n", "      <td>156.345054</td>\n", "      <td>2.628845</td>\n", "      <td>0.370170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 12:30:00-04:00</th>\n", "      <td>137.967317</td>\n", "      <td>141.759000</td>\n", "      <td>145.550682</td>\n", "      <td>5.349477</td>\n", "      <td>0.304968</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 13:30:00-04:00</th>\n", "      <td>139.773876</td>\n", "      <td>140.676578</td>\n", "      <td>141.579279</td>\n", "      <td>1.283371</td>\n", "      <td>0.168394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 14:30:00-04:00</th>\n", "      <td>139.360636</td>\n", "      <td>140.396957</td>\n", "      <td>141.433279</td>\n", "      <td>1.476274</td>\n", "      <td>0.174298</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 15:30:00-04:00</th>\n", "      <td>139.544406</td>\n", "      <td>140.166959</td>\n", "      <td>140.789511</td>\n", "      <td>0.888301</td>\n", "      <td>0.430161</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 16:00:00-04:00</th>\n", "      <td>139.687928</td>\n", "      <td>140.049957</td>\n", "      <td>140.411986</td>\n", "      <td>0.517000</td>\n", "      <td>0.555298</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>155 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                            BBL_5_2.0   BBM_5_2.0   BBU_5_2.0  BBB_5_2.0  \\\n", "2022-09-08 09:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 10:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 11:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 12:30:00-04:00         NaN         NaN         NaN        NaN   \n", "2022-09-08 13:30:00-04:00  152.288308  154.316681  156.345054   2.628845   \n", "...                               ...         ...         ...        ...   \n", "2022-10-07 12:30:00-04:00  137.967317  141.759000  145.550682   5.349477   \n", "2022-10-07 13:30:00-04:00  139.773876  140.676578  141.579279   1.283371   \n", "2022-10-07 14:30:00-04:00  139.360636  140.396957  141.433279   1.476274   \n", "2022-10-07 15:30:00-04:00  139.544406  140.166959  140.789511   0.888301   \n", "2022-10-07 16:00:00-04:00  139.687928  140.049957  140.411986   0.517000   \n", "\n", "                           BBP_5_2.0  \n", "2022-09-08 09:30:00-04:00        NaN  \n", "2022-09-08 10:30:00-04:00        NaN  \n", "2022-09-08 11:30:00-04:00        NaN  \n", "2022-09-08 12:30:00-04:00        NaN  \n", "2022-09-08 13:30:00-04:00   0.370170  \n", "...                              ...  \n", "2022-10-07 12:30:00-04:00   0.304968  \n", "2022-10-07 13:30:00-04:00   0.168394  \n", "2022-10-07 14:30:00-04:00   0.174298  \n", "2022-10-07 15:30:00-04:00   0.430161  \n", "2022-10-07 16:00:00-04:00   0.555298  \n", "\n", "[155 rows x 5 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df.ta.bbands(append=True)"]}, {"cell_type": "code", "execution_count": 24, "id": "091188ab", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>rsi</th>\n", "      <th>RSI_14</th>\n", "      <th>BBL_5_2.0</th>\n", "      <th>BBM_5_2.0</th>\n", "      <th>BBU_5_2.0</th>\n", "      <th>BBB_5_2.0</th>\n", "      <th>BBP_5_2.0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-09-08 09:30:00-04:00</th>\n", "      <td>154.639999</td>\n", "      <td>155.839996</td>\n", "      <td>154.369995</td>\n", "      <td>154.919998</td>\n", "      <td>20423405</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 10:30:00-04:00</th>\n", "      <td>154.910004</td>\n", "      <td>156.360001</td>\n", "      <td>154.700195</td>\n", "      <td>155.990005</td>\n", "      <td>9445752</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 11:30:00-04:00</th>\n", "      <td>155.985001</td>\n", "      <td>156.190002</td>\n", "      <td>152.679993</td>\n", "      <td>153.149902</td>\n", "      <td>12280244</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 12:30:00-04:00</th>\n", "      <td>153.149994</td>\n", "      <td>154.020004</td>\n", "      <td>152.695007</td>\n", "      <td>153.733505</td>\n", "      <td>10943346</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-09-08 13:30:00-04:00</th>\n", "      <td>153.740005</td>\n", "      <td>154.139999</td>\n", "      <td>153.229996</td>\n", "      <td>153.789993</td>\n", "      <td>7342717</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>152.288308</td>\n", "      <td>154.316681</td>\n", "      <td>156.345054</td>\n", "      <td>2.628845</td>\n", "      <td>0.370170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 12:30:00-04:00</th>\n", "      <td>140.669998</td>\n", "      <td>140.720001</td>\n", "      <td>139.940002</td>\n", "      <td>140.279999</td>\n", "      <td>7685803</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.853757</td>\n", "      <td>32.022693</td>\n", "      <td>137.967317</td>\n", "      <td>141.759000</td>\n", "      <td>145.550682</td>\n", "      <td>5.349477</td>\n", "      <td>0.304968</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 13:30:00-04:00</th>\n", "      <td>140.145004</td>\n", "      <td>140.600006</td>\n", "      <td>140.035004</td>\n", "      <td>140.077896</td>\n", "      <td>6175166</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.607664</td>\n", "      <td>31.428617</td>\n", "      <td>139.773876</td>\n", "      <td>140.676578</td>\n", "      <td>141.579279</td>\n", "      <td>1.283371</td>\n", "      <td>0.168394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 14:30:00-04:00</th>\n", "      <td>140.070007</td>\n", "      <td>140.535004</td>\n", "      <td>139.600006</td>\n", "      <td>139.721893</td>\n", "      <td>8536199</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.170491</td>\n", "      <td>30.360166</td>\n", "      <td>139.360636</td>\n", "      <td>140.396957</td>\n", "      <td>141.433279</td>\n", "      <td>1.476274</td>\n", "      <td>0.174298</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 15:30:00-04:00</th>\n", "      <td>139.720001</td>\n", "      <td>140.490005</td>\n", "      <td>139.445007</td>\n", "      <td>140.080002</td>\n", "      <td>13306374</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.827724</td>\n", "      <td>32.833747</td>\n", "      <td>139.544406</td>\n", "      <td>140.166959</td>\n", "      <td>140.789511</td>\n", "      <td>0.888301</td>\n", "      <td>0.430161</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 16:00:00-04:00</th>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>140.089996</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.846325</td>\n", "      <td>32.905376</td>\n", "      <td>139.687928</td>\n", "      <td>140.049957</td>\n", "      <td>140.411986</td>\n", "      <td>0.517000</td>\n", "      <td>0.555298</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>155 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "2022-09-08 09:30:00-04:00  154.639999  155.839996  154.369995  154.919998   \n", "2022-09-08 10:30:00-04:00  154.910004  156.360001  154.700195  155.990005   \n", "2022-09-08 11:30:00-04:00  155.985001  156.190002  152.679993  153.149902   \n", "2022-09-08 12:30:00-04:00  153.149994  154.020004  152.695007  153.733505   \n", "2022-09-08 13:30:00-04:00  153.740005  154.139999  153.229996  153.789993   \n", "...                               ...         ...         ...         ...   \n", "2022-10-07 12:30:00-04:00  140.669998  140.720001  139.940002  140.279999   \n", "2022-10-07 13:30:00-04:00  140.145004  140.600006  140.035004  140.077896   \n", "2022-10-07 14:30:00-04:00  140.070007  140.535004  139.600006  139.721893   \n", "2022-10-07 15:30:00-04:00  139.720001  140.490005  139.445007  140.080002   \n", "2022-10-07 16:00:00-04:00  140.089996  140.089996  140.089996  140.089996   \n", "\n", "                             Volume  Dividends  Stock Splits        rsi  \\\n", "2022-09-08 09:30:00-04:00  20423405          0             0        NaN   \n", "2022-09-08 10:30:00-04:00   9445752          0             0        NaN   \n", "2022-09-08 11:30:00-04:00  12280244          0             0        NaN   \n", "2022-09-08 12:30:00-04:00  10943346          0             0        NaN   \n", "2022-09-08 13:30:00-04:00   7342717          0             0        NaN   \n", "...                             ...        ...           ...        ...   \n", "2022-10-07 12:30:00-04:00   7685803          0             0  40.853757   \n", "2022-10-07 13:30:00-04:00   6175166          0             0  40.607664   \n", "2022-10-07 14:30:00-04:00   8536199          0             0  40.170491   \n", "2022-10-07 15:30:00-04:00  13306374          0             0  40.827724   \n", "2022-10-07 16:00:00-04:00         0          0             0  40.846325   \n", "\n", "                              RSI_14   BBL_5_2.0   BBM_5_2.0   BBU_5_2.0  \\\n", "2022-09-08 09:30:00-04:00        NaN         NaN         NaN         NaN   \n", "2022-09-08 10:30:00-04:00        NaN         NaN         NaN         NaN   \n", "2022-09-08 11:30:00-04:00        NaN         NaN         NaN         NaN   \n", "2022-09-08 12:30:00-04:00        NaN         NaN         NaN         NaN   \n", "2022-09-08 13:30:00-04:00        NaN  152.288308  154.316681  156.345054   \n", "...                              ...         ...         ...         ...   \n", "2022-10-07 12:30:00-04:00  32.022693  137.967317  141.759000  145.550682   \n", "2022-10-07 13:30:00-04:00  31.428617  139.773876  140.676578  141.579279   \n", "2022-10-07 14:30:00-04:00  30.360166  139.360636  140.396957  141.433279   \n", "2022-10-07 15:30:00-04:00  32.833747  139.544406  140.166959  140.789511   \n", "2022-10-07 16:00:00-04:00  32.905376  139.687928  140.049957  140.411986   \n", "\n", "                           BBB_5_2.0  BBP_5_2.0  \n", "2022-09-08 09:30:00-04:00        NaN        NaN  \n", "2022-09-08 10:30:00-04:00        NaN        NaN  \n", "2022-09-08 11:30:00-04:00        NaN        NaN  \n", "2022-09-08 12:30:00-04:00        NaN        NaN  \n", "2022-09-08 13:30:00-04:00   2.628845   0.370170  \n", "...                              ...        ...  \n", "2022-10-07 12:30:00-04:00   5.349477   0.304968  \n", "2022-10-07 13:30:00-04:00   1.283371   0.168394  \n", "2022-10-07 14:30:00-04:00   1.476274   0.174298  \n", "2022-10-07 15:30:00-04:00   0.888301   0.430161  \n", "2022-10-07 16:00:00-04:00   0.517000   0.555298  \n", "\n", "[155 rows x 14 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "markdown", "id": "246dcd88", "metadata": {}, "source": ["## Column name formatting\n", "\n", "Pandas-ta has some flexibility with column names, see this dictionary from `core.py`\n", "\n", "```\n", "common_names = {\n", "    \"Date\": \"date\",\n", "    \"Time\": \"time\",\n", "    \"Timestamp\": \"timestamp\",\n", "    \"Datetime\": \"datetime\",\n", "    \"Open\": \"open\",\n", "    \"High\": \"high\",\n", "    \"Low\": \"low\",\n", "    \"Close\": \"close\",\n", "    \"Adj Close\": \"adj_close\",\n", "    \"Volume\": \"volume\",\n", "    \"Dividends\": \"dividends\",\n", "    \"Stock Splits\": \"split\",\n", "}\n", "```\n"]}, {"cell_type": "markdown", "id": "37435335", "metadata": {}, "source": ["### Chaining Indicators"]}, {"cell_type": "code", "execution_count": 29, "id": "62e2a482", "metadata": {}, "outputs": [{"data": {"text/plain": ["2022-09-08 09:30:00-04:00           NaN\n", "2022-09-08 10:30:00-04:00           NaN\n", "2022-09-08 11:30:00-04:00           NaN\n", "2022-09-08 12:30:00-04:00           NaN\n", "2022-09-08 13:30:00-04:00           NaN\n", "                                ...    \n", "2022-10-07 12:30:00-04:00    143.181928\n", "2022-10-07 13:30:00-04:00    142.642391\n", "2022-10-07 14:30:00-04:00    142.158634\n", "2022-10-07 15:30:00-04:00    141.754111\n", "2022-10-07 16:00:00-04:00    141.451544\n", "Name: test_EMA_10_OHLC4, Length: 155, dtype: float64"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# ohlc4 = (open + high + low + close)/4\n", "df.ta.ema(close=df.ta.ohlc4(), length=10, suffix=\"OHLC4\", prefix=\"test\", append = True)"]}, {"cell_type": "code", "execution_count": 28, "id": "0852fd16", "metadata": {}, "outputs": [{"data": {"text/plain": ["2022-09-08 09:30:00-04:00    154.942497\n", "2022-09-08 10:30:00-04:00    155.490051\n", "2022-09-08 11:30:00-04:00    154.501225\n", "2022-09-08 12:30:00-04:00    153.399628\n", "2022-09-08 13:30:00-04:00    153.724998\n", "                                ...    \n", "2022-10-07 12:30:00-04:00    140.402500\n", "2022-10-07 13:30:00-04:00    140.214478\n", "2022-10-07 14:30:00-04:00    139.981728\n", "2022-10-07 15:30:00-04:00    139.933754\n", "2022-10-07 16:00:00-04:00    140.089996\n", "Name: OHLC4, Length: 155, dtype: float64"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df.ta.ohlc4()"]}, {"cell_type": "markdown", "id": "73bae70a", "metadata": {}, "source": ["## 3. Strategies"]}, {"cell_type": "code", "execution_count": 27, "id": "28c72213", "metadata": {}, "outputs": [], "source": ["df = df.ta.ticker(\"aapl\")"]}, {"cell_type": "code", "execution_count": 28, "id": "c50c2534", "metadata": {}, "outputs": [], "source": ["MyStrategy = ta.Strategy(\n", "    name=\"DCSMA10\",\n", "    ta=[\n", "        {\"kind\": \"ohlc4\"},\n", "        {\"kind\": \"sma\", \"length\": 10},\n", "        {\"kind\": \"donchian\", \"lower_length\": 10, \"upper_length\": 15},\n", "        {\"kind\": \"ema\", \"close\": \"SMA_10\", \"length\": 10, \"suffix\": \"OHLC4\"},\n", "    ]\n", ")\n", "\n", "# (2) Run the Strategy\n", "df.ta.strategy(MyStrategy)"]}, {"cell_type": "code", "execution_count": 29, "id": "7cad4929", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>OHLC4</th>\n", "      <th>SMA_10</th>\n", "      <th>DCL_10_15</th>\n", "      <th>DCM_10_15</th>\n", "      <th>DCU_10_15</th>\n", "      <th>EMA_10_OHLC4</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.100148</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.095038</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.088078</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.090144</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.092754</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>140.355003</td>\n", "      <td>148.928998</td>\n", "      <td>137.690002</td>\n", "      <td>149.114998</td>\n", "      <td>160.539993</td>\n", "      <td>152.575156</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.402500</td>\n", "      <td>147.848999</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>151.715855</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.215000</td>\n", "      <td>147.116998</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>150.879699</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.999996</td>\n", "      <td>146.385997</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>150.062662</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>141.294998</td>\n", "      <td>145.351997</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>149.206178</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits       OHLC4  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0    0.100148   \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0    0.095038   \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0    0.088078   \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0    0.090144   \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0    0.092754   \n", "...                              ...        ...           ...         ...   \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0  140.355003   \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0  145.402500   \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0  145.215000   \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0  145.999996   \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0  141.294998   \n", "\n", "                               SMA_10   DCL_10_15   DCM_10_15   DCU_10_15  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-15 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-16 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-17 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-18 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  148.928998  137.690002  149.114998  160.539993   \n", "2022-10-04 00:00:00-04:00  147.848999  137.690002  148.215004  158.740005   \n", "2022-10-05 00:00:00-04:00  147.116998  137.690002  148.215004  158.740005   \n", "2022-10-06 00:00:00-04:00  146.385997  137.690002  148.215004  158.740005   \n", "2022-10-07 00:00:00-04:00  145.351997  137.690002  148.215004  158.740005   \n", "\n", "                           EMA_10_OHLC4  \n", "Date                                     \n", "1980-12-12 00:00:00-05:00           NaN  \n", "1980-12-15 00:00:00-05:00           NaN  \n", "1980-12-16 00:00:00-05:00           NaN  \n", "1980-12-17 00:00:00-05:00           NaN  \n", "1980-12-18 00:00:00-05:00           NaN  \n", "...                                 ...  \n", "2022-10-03 00:00:00-04:00    152.575156  \n", "2022-10-04 00:00:00-04:00    151.715855  \n", "2022-10-05 00:00:00-04:00    150.879699  \n", "2022-10-06 00:00:00-04:00    150.062662  \n", "2022-10-07 00:00:00-04:00    149.206178  \n", "\n", "[10545 rows x 13 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "markdown", "id": "fc0f1ec6", "metadata": {}, "source": ["## All the indicators"]}, {"cell_type": "code", "execution_count": 32, "id": "c049cd26", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_19648/2946661692.py:3: FutureWarning: The series.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df.ta.strategy(\"All\", timed = True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[i] Runtime: 13913.2577 ms (13.9133 s)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_19648/2946661692.py:3: UserWarning: Converting to PeriodArray/Index representation will drop timezone information.\n", "  df.ta.strategy(\"All\", timed = True)\n", "/tmp/ipykernel_19648/2946661692.py:3: UserWarning: Converting to PeriodArray/Index representation will drop timezone information.\n", "  df.ta.strategy(\"All\", timed = True)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>ABER_ZG_5_15</th>\n", "      <th>ABER_SG_5_15</th>\n", "      <th>ABER_XG_5_15</th>\n", "      <th>...</th>\n", "      <th>VIDYA_14</th>\n", "      <th>VTXP_14</th>\n", "      <th>VTXM_14</th>\n", "      <th>VWAP_D</th>\n", "      <th>VWMA_10</th>\n", "      <th>WCP</th>\n", "      <th>WILLR_14</th>\n", "      <th>WMA_10</th>\n", "      <th>ZL_EMA_10</th>\n", "      <th>ZS_30</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.100184</td>\n", "      <td>NaN</td>\n", "      <td>0.100148</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.094965</td>\n", "      <td>NaN</td>\n", "      <td>0.094929</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.088006</td>\n", "      <td>NaN</td>\n", "      <td>0.087969</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.090180</td>\n", "      <td>NaN</td>\n", "      <td>0.090144</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.093225</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.092791</td>\n", "      <td>NaN</td>\n", "      <td>0.092754</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>144.942666</td>\n", "      <td>149.814706</td>\n", "      <td>140.070625</td>\n", "      <td>...</td>\n", "      <td>155.686293</td>\n", "      <td>0.683566</td>\n", "      <td>1.154799</td>\n", "      <td>141.070002</td>\n", "      <td>148.430145</td>\n", "      <td>141.415001</td>\n", "      <td>-77.387203</td>\n", "      <td>146.285634</td>\n", "      <td>141.553455</td>\n", "      <td>-1.798987</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>143.619333</td>\n", "      <td>148.417904</td>\n", "      <td>138.820762</td>\n", "      <td>...</td>\n", "      <td>155.392509</td>\n", "      <td>0.748936</td>\n", "      <td>1.068794</td>\n", "      <td>145.526667</td>\n", "      <td>147.380298</td>\n", "      <td>145.670002</td>\n", "      <td>-60.047494</td>\n", "      <td>145.771272</td>\n", "      <td>141.700103</td>\n", "      <td>-1.229065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>143.050666</td>\n", "      <td>147.820667</td>\n", "      <td>138.280666</td>\n", "      <td>...</td>\n", "      <td>155.201270</td>\n", "      <td>0.765295</td>\n", "      <td>1.026783</td>\n", "      <td>145.596664</td>\n", "      <td>146.686473</td>\n", "      <td>145.797497</td>\n", "      <td>-58.622374</td>\n", "      <td>145.507816</td>\n", "      <td>143.267355</td>\n", "      <td>-1.125067</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>143.604666</td>\n", "      <td>148.211333</td>\n", "      <td>138.998000</td>\n", "      <td>...</td>\n", "      <td>155.014340</td>\n", "      <td>0.848831</td>\n", "      <td>0.983685</td>\n", "      <td>146.063329</td>\n", "      <td>146.089857</td>\n", "      <td>145.904995</td>\n", "      <td>-63.230456</td>\n", "      <td>145.201088</td>\n", "      <td>144.975107</td>\n", "      <td>-1.207937</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>143.827333</td>\n", "      <td>148.525554</td>\n", "      <td>139.129111</td>\n", "      <td>...</td>\n", "      <td>154.266499</td>\n", "      <td>0.784179</td>\n", "      <td>1.060046</td>\n", "      <td>140.880000</td>\n", "      <td>145.170454</td>\n", "      <td>140.682499</td>\n", "      <td>-88.598605</td>\n", "      <td>144.056360</td>\n", "      <td>143.657814</td>\n", "      <td>-1.951891</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 285 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits  ABER_ZG_5_15  \\\n", "Date                                                                          \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0           NaN   \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0           NaN   \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0           NaN   \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0           NaN   \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0      0.093225   \n", "...                              ...        ...           ...           ...   \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0    144.942666   \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0    143.619333   \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0    143.050666   \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0    143.604666   \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0    143.827333   \n", "\n", "                           ABER_SG_5_15  ABER_XG_5_15  ...    VIDYA_14  \\\n", "Date                                                   ...               \n", "1980-12-12 00:00:00-05:00           NaN           NaN  ...         NaN   \n", "1980-12-15 00:00:00-05:00           NaN           NaN  ...         NaN   \n", "1980-12-16 00:00:00-05:00           NaN           NaN  ...         NaN   \n", "1980-12-17 00:00:00-05:00           NaN           NaN  ...         NaN   \n", "1980-12-18 00:00:00-05:00           NaN           NaN  ...         NaN   \n", "...                                 ...           ...  ...         ...   \n", "2022-10-03 00:00:00-04:00    149.814706    140.070625  ...  155.686293   \n", "2022-10-04 00:00:00-04:00    148.417904    138.820762  ...  155.392509   \n", "2022-10-05 00:00:00-04:00    147.820667    138.280666  ...  155.201270   \n", "2022-10-06 00:00:00-04:00    148.211333    138.998000  ...  155.014340   \n", "2022-10-07 00:00:00-04:00    148.525554    139.129111  ...  154.266499   \n", "\n", "                            VTXP_14   VTXM_14      VWAP_D     VWMA_10  \\\n", "Date                                                                    \n", "1980-12-12 00:00:00-05:00       NaN       NaN    0.100184         NaN   \n", "1980-12-15 00:00:00-05:00       NaN       NaN    0.094965         NaN   \n", "1980-12-16 00:00:00-05:00       NaN       NaN    0.088006         NaN   \n", "1980-12-17 00:00:00-05:00       NaN       NaN    0.090180         NaN   \n", "1980-12-18 00:00:00-05:00       NaN       NaN    0.092791         NaN   \n", "...                             ...       ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  0.683566  1.154799  141.070002  148.430145   \n", "2022-10-04 00:00:00-04:00  0.748936  1.068794  145.526667  147.380298   \n", "2022-10-05 00:00:00-04:00  0.765295  1.026783  145.596664  146.686473   \n", "2022-10-06 00:00:00-04:00  0.848831  0.983685  146.063329  146.089857   \n", "2022-10-07 00:00:00-04:00  0.784179  1.060046  140.880000  145.170454   \n", "\n", "                                  WCP   WILLR_14      WMA_10   ZL_EMA_10  \\\n", "Date                                                                       \n", "1980-12-12 00:00:00-05:00    0.100148        NaN         NaN         NaN   \n", "1980-12-15 00:00:00-05:00    0.094929        NaN         NaN         NaN   \n", "1980-12-16 00:00:00-05:00    0.087969        NaN         NaN         NaN   \n", "1980-12-17 00:00:00-05:00    0.090144        NaN         NaN         NaN   \n", "1980-12-18 00:00:00-05:00    0.092754        NaN         NaN         NaN   \n", "...                               ...        ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  141.415001 -77.387203  146.285634  141.553455   \n", "2022-10-04 00:00:00-04:00  145.670002 -60.047494  145.771272  141.700103   \n", "2022-10-05 00:00:00-04:00  145.797497 -58.622374  145.507816  143.267355   \n", "2022-10-06 00:00:00-04:00  145.904995 -63.230456  145.201088  144.975107   \n", "2022-10-07 00:00:00-04:00  140.682499 -88.598605  144.056360  143.657814   \n", "\n", "                              ZS_30  \n", "Date                                 \n", "1980-12-12 00:00:00-05:00       NaN  \n", "1980-12-15 00:00:00-05:00       NaN  \n", "1980-12-16 00:00:00-05:00       NaN  \n", "1980-12-17 00:00:00-05:00       NaN  \n", "1980-12-18 00:00:00-05:00       NaN  \n", "...                             ...  \n", "2022-10-03 00:00:00-04:00 -1.798987  \n", "2022-10-04 00:00:00-04:00 -1.229065  \n", "2022-10-05 00:00:00-04:00 -1.125067  \n", "2022-10-06 00:00:00-04:00 -1.207937  \n", "2022-10-07 00:00:00-04:00 -1.951891  \n", "\n", "[10545 rows x 285 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.ta.ticker(\"aapl\")\n", "df.ta.cores = 0\n", "df.ta.strategy(\"All\", timed = True)\n", "df"]}, {"cell_type": "code", "execution_count": 31, "id": "178c3beb", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits',\n", "       'ABER_ZG_5_15', 'ABER_SG_5_15', 'ABER_XG_5_15',\n", "       ...\n", "       'VIDYA_14', 'VTXP_14', 'VTXM_14', 'VWAP_D', 'VWMA_10', 'WCP',\n", "       'WILLR_14', 'WMA_10', 'ZL_EMA_10', 'ZS_30'],\n", "      dtype='object', length=285)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "markdown", "id": "3964fbdd", "metadata": {}, "source": ["## Custom Column names\n", "(No multi-processing)"]}, {"cell_type": "code", "execution_count": 5, "id": "37464d65", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>CDL_2CROWS</th>\n", "      <th>CDL_3BLACKCROWS</th>\n", "      <th>CDL_3INSIDE</th>\n", "      <th>...</th>\n", "      <th>CDL_UPSIDEGAP2CROWS</th>\n", "      <th>CDL_XSIDEGAP3METHODS</th>\n", "      <th>open_Z_30_1</th>\n", "      <th>high_Z_30_1</th>\n", "      <th>low_Z_30_1</th>\n", "      <th>close_Z_30_1</th>\n", "      <th>HA_open</th>\n", "      <th>HA_high</th>\n", "      <th>HA_low</th>\n", "      <th>HA_close</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100148</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.100094</td>\n", "      <td>0.100094</td>\n", "      <td>0.094820</td>\n", "      <td>0.095038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.097566</td>\n", "      <td>0.097566</td>\n", "      <td>0.087861</td>\n", "      <td>0.088078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.092822</td>\n", "      <td>0.092822</td>\n", "      <td>0.090035</td>\n", "      <td>0.090144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.091483</td>\n", "      <td>0.093081</td>\n", "      <td>0.091483</td>\n", "      <td>0.092754</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-2.308389</td>\n", "      <td>-2.064321</td>\n", "      <td>-2.081569</td>\n", "      <td>-1.798987</td>\n", "      <td>143.589255</td>\n", "      <td>143.589255</td>\n", "      <td>137.690002</td>\n", "      <td>140.355003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.354988</td>\n", "      <td>-1.529249</td>\n", "      <td>-1.167332</td>\n", "      <td>-1.229065</td>\n", "      <td>141.972129</td>\n", "      <td>146.220001</td>\n", "      <td>141.972129</td>\n", "      <td>145.402500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.392314</td>\n", "      <td>-1.290141</td>\n", "      <td>-1.269122</td>\n", "      <td>-1.125067</td>\n", "      <td>143.687315</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>145.215000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.099441</td>\n", "      <td>-1.198773</td>\n", "      <td>-0.916395</td>\n", "      <td>-1.207937</td>\n", "      <td>144.451157</td>\n", "      <td>147.539993</td>\n", "      <td>144.451157</td>\n", "      <td>145.999996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-100.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.483835</td>\n", "      <td>-1.759172</td>\n", "      <td>-1.719395</td>\n", "      <td>-1.951891</td>\n", "      <td>145.225577</td>\n", "      <td>145.225577</td>\n", "      <td>139.449997</td>\n", "      <td>141.294998</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 77 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits  CDL_2CROWS  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0         0.0   \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0         0.0   \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0         0.0   \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0         0.0   \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0         0.0   \n", "...                              ...        ...           ...         ...   \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0         0.0   \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0         0.0   \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0         0.0   \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0         0.0   \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0         0.0   \n", "\n", "                           CDL_3BLACKCROWS  CDL_3INSIDE  ...  \\\n", "Date                                                     ...   \n", "1980-12-12 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-15 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-16 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-17 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-18 00:00:00-05:00              0.0          0.0  ...   \n", "...                                    ...          ...  ...   \n", "2022-10-03 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-04 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-05 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-06 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-07 00:00:00-04:00              0.0       -100.0  ...   \n", "\n", "                           CDL_UPSIDEGAP2CROWS  CDL_XSIDEGAP3METHODS  \\\n", "Date                                                                   \n", "1980-12-12 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-15 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-16 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-17 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-18 00:00:00-05:00                  0.0                   0.0   \n", "...                                        ...                   ...   \n", "2022-10-03 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-04 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-05 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-06 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-07 00:00:00-04:00                  0.0                   0.0   \n", "\n", "                           open_Z_30_1  high_Z_30_1  low_Z_30_1  close_Z_30_1  \\\n", "Date                                                                            \n", "1980-12-12 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-15 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-16 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-17 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-18 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "...                                ...          ...         ...           ...   \n", "2022-10-03 00:00:00-04:00    -2.308389    -2.064321   -2.081569     -1.798987   \n", "2022-10-04 00:00:00-04:00    -1.354988    -1.529249   -1.167332     -1.229065   \n", "2022-10-05 00:00:00-04:00    -1.392314    -1.290141   -1.269122     -1.125067   \n", "2022-10-06 00:00:00-04:00    -1.099441    -1.198773   -0.916395     -1.207937   \n", "2022-10-07 00:00:00-04:00    -1.483835    -1.759172   -1.719395     -1.951891   \n", "\n", "                              HA_open     HA_high      HA_low    HA_close  \n", "Date                                                                       \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100148  \n", "1980-12-15 00:00:00-05:00    0.100094    0.100094    0.094820    0.095038  \n", "1980-12-16 00:00:00-05:00    0.097566    0.097566    0.087861    0.088078  \n", "1980-12-17 00:00:00-05:00    0.092822    0.092822    0.090035    0.090144  \n", "1980-12-18 00:00:00-05:00    0.091483    0.093081    0.091483    0.092754  \n", "...                               ...         ...         ...         ...  \n", "2022-10-03 00:00:00-04:00  143.589255  143.589255  137.690002  140.355003  \n", "2022-10-04 00:00:00-04:00  141.972129  146.220001  141.972129  145.402500  \n", "2022-10-05 00:00:00-04:00  143.687315  147.380005  143.009995  145.215000  \n", "2022-10-06 00:00:00-04:00  144.451157  147.539993  144.451157  145.999996  \n", "2022-10-07 00:00:00-04:00  145.225577  145.225577  139.449997  141.294998  \n", "\n", "[10545 rows x 77 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.ta.ticker(\"aapl\")\n", "df.ta.strategy(\"candles\")\n", "df"]}, {"cell_type": "code", "execution_count": 35, "id": "02f7ebb0", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>OHLC4</th>\n", "      <th>SMA</th>\n", "      <th>DLC</th>\n", "      <th>DCM</th>\n", "      <th>DCU</th>\n", "      <th>EMA2</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.100148</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.095038</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.088078</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.090144</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.092754</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>140.355003</td>\n", "      <td>152.086998</td>\n", "      <td>137.690002</td>\n", "      <td>149.114998</td>\n", "      <td>160.539993</td>\n", "      <td>151.337411</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.402500</td>\n", "      <td>151.665498</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>150.772181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.215000</td>\n", "      <td>151.187498</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>150.242926</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>145.999996</td>\n", "      <td>150.735997</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>149.838837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>141.294998</td>\n", "      <td>149.871997</td>\n", "      <td>137.690002</td>\n", "      <td>148.215004</td>\n", "      <td>158.740005</td>\n", "      <td>149.025138</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits       OHLC4  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0    0.100148   \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0    0.095038   \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0    0.088078   \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0    0.090144   \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0    0.092754   \n", "...                              ...        ...           ...         ...   \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0  140.355003   \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0  145.402500   \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0  145.215000   \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0  145.999996   \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0  141.294998   \n", "\n", "                                  SMA         DLC         DCM         DCU  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-15 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-16 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-17 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "1980-12-18 00:00:00-05:00         NaN         NaN         NaN         NaN   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  152.086998  137.690002  149.114998  160.539993   \n", "2022-10-04 00:00:00-04:00  151.665498  137.690002  148.215004  158.740005   \n", "2022-10-05 00:00:00-04:00  151.187498  137.690002  148.215004  158.740005   \n", "2022-10-06 00:00:00-04:00  150.735997  137.690002  148.215004  158.740005   \n", "2022-10-07 00:00:00-04:00  149.871997  137.690002  148.215004  158.740005   \n", "\n", "                                 EMA2  \n", "Date                                   \n", "1980-12-12 00:00:00-05:00         NaN  \n", "1980-12-15 00:00:00-05:00         NaN  \n", "1980-12-16 00:00:00-05:00         NaN  \n", "1980-12-17 00:00:00-05:00         NaN  \n", "1980-12-18 00:00:00-05:00         NaN  \n", "...                               ...  \n", "2022-10-03 00:00:00-04:00  151.337411  \n", "2022-10-04 00:00:00-04:00  150.772181  \n", "2022-10-05 00:00:00-04:00  150.242926  \n", "2022-10-06 00:00:00-04:00  149.838837  \n", "2022-10-07 00:00:00-04:00  149.025138  \n", "\n", "[10545 rows x 13 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.ta.ticker(\"aapl\")\n", "MyStrategy = ta.Strategy(\n", "    name=\"DCSMA10\",\n", "    ta=[\n", "        {\"kind\": \"ohlc4\"},\n", "        {\"kind\": \"sma\", \"length\": 10, \"col_names\":(\"SMA\",)},\n", "        {\"kind\": \"donchian\", \"lower_length\": 10, \"upper_length\": 15, \"col_names\":(\"DLC\",\"DCM\",\"DCU\")},\n", "        {\"kind\": \"ema\", \"close\": \"OHLC4\", \"length\": 10, \"suffix\": \"OHLC4\", \"col_names\":(\"EMA2\",)},\n", "    ]\n", ")\n", "\n", "# (2) Run the Strategy\n", "df.ta.strategy(MyStrategy, length= 20)\n", "df"]}, {"cell_type": "code", "execution_count": 37, "id": "c8b8c134", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>CDL_2CROWS</th>\n", "      <th>CDL_3BLACKCROWS</th>\n", "      <th>CDL_3INSIDE</th>\n", "      <th>...</th>\n", "      <th>CDL_UPSIDEGAP2CROWS</th>\n", "      <th>CDL_XSIDEGAP3METHODS</th>\n", "      <th>open_Z_30_1</th>\n", "      <th>high_Z_30_1</th>\n", "      <th>low_Z_30_1</th>\n", "      <th>close_Z_30_1</th>\n", "      <th>HA_open</th>\n", "      <th>HA_high</th>\n", "      <th>HA_low</th>\n", "      <th>HA_close</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1980-12-12 00:00:00-05:00</th>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100039</td>\n", "      <td>469033600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.100039</td>\n", "      <td>0.100474</td>\n", "      <td>0.100039</td>\n", "      <td>0.100148</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-15 00:00:00-05:00</th>\n", "      <td>0.095255</td>\n", "      <td>0.095255</td>\n", "      <td>0.094820</td>\n", "      <td>0.094820</td>\n", "      <td>175884800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.100094</td>\n", "      <td>0.100094</td>\n", "      <td>0.094820</td>\n", "      <td>0.095038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-16 00:00:00-05:00</th>\n", "      <td>0.088296</td>\n", "      <td>0.088296</td>\n", "      <td>0.087861</td>\n", "      <td>0.087861</td>\n", "      <td>105728000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.097566</td>\n", "      <td>0.097566</td>\n", "      <td>0.087861</td>\n", "      <td>0.088078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-17 00:00:00-05:00</th>\n", "      <td>0.090035</td>\n", "      <td>0.090470</td>\n", "      <td>0.090035</td>\n", "      <td>0.090035</td>\n", "      <td>86441600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.092822</td>\n", "      <td>0.092822</td>\n", "      <td>0.090035</td>\n", "      <td>0.090144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1980-12-18 00:00:00-05:00</th>\n", "      <td>0.092646</td>\n", "      <td>0.093081</td>\n", "      <td>0.092646</td>\n", "      <td>0.092646</td>\n", "      <td>73449600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.091483</td>\n", "      <td>0.093081</td>\n", "      <td>0.091483</td>\n", "      <td>0.092754</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-03 00:00:00-04:00</th>\n", "      <td>138.210007</td>\n", "      <td>143.070007</td>\n", "      <td>137.690002</td>\n", "      <td>142.449997</td>\n", "      <td>114311700</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-2.308389</td>\n", "      <td>-2.064321</td>\n", "      <td>-2.081569</td>\n", "      <td>-1.798987</td>\n", "      <td>143.589255</td>\n", "      <td>143.589255</td>\n", "      <td>137.690002</td>\n", "      <td>140.355003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-04 00:00:00-04:00</th>\n", "      <td>145.029999</td>\n", "      <td>146.220001</td>\n", "      <td>144.259995</td>\n", "      <td>146.100006</td>\n", "      <td>87830100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.354988</td>\n", "      <td>-1.529249</td>\n", "      <td>-1.167332</td>\n", "      <td>-1.229065</td>\n", "      <td>141.972129</td>\n", "      <td>146.220001</td>\n", "      <td>141.972129</td>\n", "      <td>145.402500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-05 00:00:00-04:00</th>\n", "      <td>144.070007</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>146.399994</td>\n", "      <td>79471000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.392314</td>\n", "      <td>-1.290141</td>\n", "      <td>-1.269122</td>\n", "      <td>-1.125067</td>\n", "      <td>143.687315</td>\n", "      <td>147.380005</td>\n", "      <td>143.009995</td>\n", "      <td>145.215000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-06 00:00:00-04:00</th>\n", "      <td>145.809998</td>\n", "      <td>147.539993</td>\n", "      <td>145.220001</td>\n", "      <td>145.429993</td>\n", "      <td>68402200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.099441</td>\n", "      <td>-1.198773</td>\n", "      <td>-0.916395</td>\n", "      <td>-1.207937</td>\n", "      <td>144.451157</td>\n", "      <td>147.539993</td>\n", "      <td>144.451157</td>\n", "      <td>145.999996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-10-07 00:00:00-04:00</th>\n", "      <td>142.539993</td>\n", "      <td>143.100006</td>\n", "      <td>139.449997</td>\n", "      <td>140.089996</td>\n", "      <td>85859100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-100.0</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.483835</td>\n", "      <td>-1.759172</td>\n", "      <td>-1.719395</td>\n", "      <td>-1.951891</td>\n", "      <td>145.225577</td>\n", "      <td>145.225577</td>\n", "      <td>139.449997</td>\n", "      <td>141.294998</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10545 rows × 77 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100039   \n", "1980-12-15 00:00:00-05:00    0.095255    0.095255    0.094820    0.094820   \n", "1980-12-16 00:00:00-05:00    0.088296    0.088296    0.087861    0.087861   \n", "1980-12-17 00:00:00-05:00    0.090035    0.090470    0.090035    0.090035   \n", "1980-12-18 00:00:00-05:00    0.092646    0.093081    0.092646    0.092646   \n", "...                               ...         ...         ...         ...   \n", "2022-10-03 00:00:00-04:00  138.210007  143.070007  137.690002  142.449997   \n", "2022-10-04 00:00:00-04:00  145.029999  146.220001  144.259995  146.100006   \n", "2022-10-05 00:00:00-04:00  144.070007  147.380005  143.009995  146.399994   \n", "2022-10-06 00:00:00-04:00  145.809998  147.539993  145.220001  145.429993   \n", "2022-10-07 00:00:00-04:00  142.539993  143.100006  139.449997  140.089996   \n", "\n", "                              Volume  Dividends  Stock Splits  CDL_2CROWS  \\\n", "Date                                                                        \n", "1980-12-12 00:00:00-05:00  469033600        0.0           0.0         0.0   \n", "1980-12-15 00:00:00-05:00  175884800        0.0           0.0         0.0   \n", "1980-12-16 00:00:00-05:00  105728000        0.0           0.0         0.0   \n", "1980-12-17 00:00:00-05:00   86441600        0.0           0.0         0.0   \n", "1980-12-18 00:00:00-05:00   73449600        0.0           0.0         0.0   \n", "...                              ...        ...           ...         ...   \n", "2022-10-03 00:00:00-04:00  114311700        0.0           0.0         0.0   \n", "2022-10-04 00:00:00-04:00   87830100        0.0           0.0         0.0   \n", "2022-10-05 00:00:00-04:00   79471000        0.0           0.0         0.0   \n", "2022-10-06 00:00:00-04:00   68402200        0.0           0.0         0.0   \n", "2022-10-07 00:00:00-04:00   85859100        0.0           0.0         0.0   \n", "\n", "                           CDL_3BLACKCROWS  CDL_3INSIDE  ...  \\\n", "Date                                                     ...   \n", "1980-12-12 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-15 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-16 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-17 00:00:00-05:00              0.0          0.0  ...   \n", "1980-12-18 00:00:00-05:00              0.0          0.0  ...   \n", "...                                    ...          ...  ...   \n", "2022-10-03 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-04 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-05 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-06 00:00:00-04:00              0.0          0.0  ...   \n", "2022-10-07 00:00:00-04:00              0.0       -100.0  ...   \n", "\n", "                           CDL_UPSIDEGAP2CROWS  CDL_XSIDEGAP3METHODS  \\\n", "Date                                                                   \n", "1980-12-12 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-15 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-16 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-17 00:00:00-05:00                  0.0                   0.0   \n", "1980-12-18 00:00:00-05:00                  0.0                   0.0   \n", "...                                        ...                   ...   \n", "2022-10-03 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-04 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-05 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-06 00:00:00-04:00                  0.0                   0.0   \n", "2022-10-07 00:00:00-04:00                  0.0                   0.0   \n", "\n", "                           open_Z_30_1  high_Z_30_1  low_Z_30_1  close_Z_30_1  \\\n", "Date                                                                            \n", "1980-12-12 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-15 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-16 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-17 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "1980-12-18 00:00:00-05:00          NaN          NaN         NaN           NaN   \n", "...                                ...          ...         ...           ...   \n", "2022-10-03 00:00:00-04:00    -2.308389    -2.064321   -2.081569     -1.798987   \n", "2022-10-04 00:00:00-04:00    -1.354988    -1.529249   -1.167332     -1.229065   \n", "2022-10-05 00:00:00-04:00    -1.392314    -1.290141   -1.269122     -1.125067   \n", "2022-10-06 00:00:00-04:00    -1.099441    -1.198773   -0.916395     -1.207937   \n", "2022-10-07 00:00:00-04:00    -1.483835    -1.759172   -1.719395     -1.951891   \n", "\n", "                              HA_open     HA_high      HA_low    HA_close  \n", "Date                                                                       \n", "1980-12-12 00:00:00-05:00    0.100039    0.100474    0.100039    0.100148  \n", "1980-12-15 00:00:00-05:00    0.100094    0.100094    0.094820    0.095038  \n", "1980-12-16 00:00:00-05:00    0.097566    0.097566    0.087861    0.088078  \n", "1980-12-17 00:00:00-05:00    0.092822    0.092822    0.090035    0.090144  \n", "1980-12-18 00:00:00-05:00    0.091483    0.093081    0.091483    0.092754  \n", "...                               ...         ...         ...         ...  \n", "2022-10-03 00:00:00-04:00  143.589255  143.589255  137.690002  140.355003  \n", "2022-10-04 00:00:00-04:00  141.972129  146.220001  141.972129  145.402500  \n", "2022-10-05 00:00:00-04:00  143.687315  147.380005  143.009995  145.215000  \n", "2022-10-06 00:00:00-04:00  144.451157  147.539993  144.451157  145.999996  \n", "2022-10-07 00:00:00-04:00  145.225577  145.225577  139.449997  141.294998  \n", "\n", "[10545 rows x 77 columns]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df.ta.ticker(\"aapl\")\n", "df.ta.strategy(\"candles\")\n", "df"]}, {"cell_type": "code", "execution_count": 7, "id": "66fb6642", "metadata": {}, "outputs": [{"data": {"text/plain": ["['candles',\n", " 'cycles',\n", " 'momentum',\n", " 'overlap',\n", " 'performance',\n", " 'statistics',\n", " 'trend',\n", " 'volatility',\n", " 'volume']"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.ta.categories"]}, {"cell_type": "code", "execution_count": null, "id": "9bfdf587", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}