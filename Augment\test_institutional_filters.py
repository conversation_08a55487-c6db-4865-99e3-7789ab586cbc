"""
Test Institutional-Grade Filters for False Breakout Protection

This script demonstrates the enhanced signal filtering system that prevents
false signals caused by breakouts instead of reversals.
"""

import sys
import os
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_institutional_filters():
    """Test the institutional-grade filters"""
    print("🏛️ Testing Institutional-Grade Filters")
    print("="*60)
    
    try:
        # Import the enhanced backtester
        import importlib.util
        spec = importlib.util.spec_from_file_location("smart_vectorized_backtester_copy", "smart_vectorized_backtester copy.py")
        smart_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(smart_module)
        SmartVectorizedBacktester = smart_module.SmartVectorizedBacktester
        from shared_api_manager import get_api
        
        # Get API and test with NSE stock
        api = get_api()
        
        # Test parameters
        ticker = 'BATAINDIA'
        exchange = 'NSE'
        date = '25-06-2025'
        start_time = '09:15'
        end_time = '13:00'
        tokenid = '371'
        
        print(f"📊 Testing with {ticker} on {exchange}")
        print(f"📅 Date: {date}, Time: {start_time} to {end_time}")
        
        # Test 1: Standard backtester (original)
        print(f"\n1️⃣ STANDARD BACKTESTER (Original)")
        print("-" * 40)
        
        standard_backtester = SmartVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start=start_time,
            end=end_time,
            date=date,
            tokenid=tokenid,
            enable_momentum_validation=True,
            enable_realtime_detection=True,
            enable_institutional_filters=False,  # Disabled
            enable_breakout_protection=False     # Disabled
        )
        
        standard_signals = standard_backtester.run_smart_vectorized_backtest()
        
        print(f"✅ Standard signals found: {len(standard_signals)}")
        for signal in standard_signals[:3]:  # Show first 3
            print(f"   📈 {signal['time']}: {signal['signal_type']} - {signal['reason'][:80]}...")
        
        # Test 2: Institutional-grade backtester (enhanced)
        print(f"\n2️⃣ INSTITUTIONAL-GRADE BACKTESTER (Enhanced)")
        print("-" * 50)
        
        institutional_backtester = SmartVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start=start_time,
            end=end_time,
            date=date,
            tokenid=tokenid,
            enable_momentum_validation=True,
            enable_realtime_detection=True,
            enable_institutional_filters=True,   # Enabled
            enable_breakout_protection=True      # Enabled
        )
        
        institutional_signals = institutional_backtester.run_smart_vectorized_backtest()
        
        print(f"✅ Institutional signals found: {len(institutional_signals)}")
        for signal in institutional_signals[:3]:  # Show first 3
            print(f"   🏛️ {signal['time']}: {signal['signal_type']} - Risk: {signal.get('breakout_risk', 'N/A')}/100")
            print(f"      Reason: {signal['reason'][:100]}...")
            if 'institutional_filters' in signal:
                print(f"      Filters: {signal['institutional_filters'][:80]}...")
        
        # Analysis
        print(f"\n3️⃣ FILTER EFFECTIVENESS ANALYSIS")
        print("-" * 40)
        
        filtered_out = len(standard_signals) - len(institutional_signals)
        filter_rate = (filtered_out / len(standard_signals) * 100) if len(standard_signals) > 0 else 0
        
        print(f"📊 Original signals: {len(standard_signals)}")
        print(f"📊 Institutional signals: {len(institutional_signals)}")
        print(f"📊 Filtered out: {filtered_out} ({filter_rate:.1f}%)")
        
        if filtered_out > 0:
            print(f"✅ Institutional filters successfully removed {filtered_out} potentially false signals!")
        else:
            print(f"ℹ️ All signals passed institutional filters (high-quality signals)")
        
        # Show detailed analysis for institutional signals
        if institutional_signals:
            print(f"\n4️⃣ DETAILED SIGNAL ANALYSIS")
            print("-" * 30)
            
            for i, signal in enumerate(institutional_signals[:5], 1):  # Show first 5
                print(f"\n🎯 Signal {i}: {signal['time']} - {signal['signal_type']}")
                print(f"   Breakout Risk: {signal.get('breakout_risk', 'N/A')}/100")
                
                if 'breakout_analysis' in signal:
                    print(f"   Risk Analysis: {signal['breakout_analysis'][:120]}...")
                
                if 'institutional_filters' in signal:
                    print(f"   Filter Status: {signal['institutional_filters'][:120]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def explain_institutional_filters():
    """Explain the institutional filters"""
    print(f"\n🎓 INSTITUTIONAL FILTER EXPLANATION")
    print("="*60)
    
    print("🏛️ These filters are used by professional traders and institutions:")
    print()
    
    print("1️⃣ VOLATILITY EXPANSION DETECTION")
    print("   • Detects when ATR (Average True Range) is expanding >20%")
    print("   • Expanding volatility often indicates breakout potential")
    print("   • Helps avoid false reversal signals during breakouts")
    print()
    
    print("2️⃣ VOLUME SURGE DETECTION")
    print("   • Identifies when recent volume is >150% of average")
    print("   • High volume often confirms breakouts rather than reversals")
    print("   • Institutional money flow indicator")
    print()
    
    print("3️⃣ MOMENTUM DIVERGENCE DETECTION")
    print("   • Checks if momentum is accelerating in breakout direction")
    print("   • Compares recent vs earlier momentum patterns")
    print("   • Prevents signals when trend is accelerating")
    print()
    
    print("4️⃣ BOLLINGER BAND WIDTH EXPANSION")
    print("   • Monitors BB width expansion >15%")
    print("   • Expanding bands indicate increasing volatility")
    print("   • Classic breakout preparation signal")
    print()
    
    print("5️⃣ PRICE PATTERN RECOGNITION")
    print("   • Detects continuation patterns (triangles, flags)")
    print("   • Identifies ascending/descending triangle patterns")
    print("   • Avoids signals during pattern completion")
    print()
    
    print("6️⃣ MARKET STRUCTURE ANALYSIS")
    print("   • Identifies key support/resistance levels")
    print("   • Checks proximity to significant pivot points")
    print("   • Avoids signals near breakout-prone levels")
    print()
    
    print("🛡️ BREAKOUT PROTECTION SCORING (0-100):")
    print("   • Trend Strength: 30 points max")
    print("   • Consecutive Candles: 25 points max")
    print("   • Distance from MA: 20 points max")
    print("   • Volatility Spike: 15 points max")
    print("   • Time of Day Risk: 10 points max")
    print("   • Signals with >70/100 risk are rejected")

def main():
    """Run institutional filter tests"""
    print("🚀 INSTITUTIONAL-GRADE SIGNAL FILTERING TEST")
    print("="*70)
    
    # Explain the filters first
    explain_institutional_filters()
    
    # Test the filters
    success = test_institutional_filters()
    
    print(f"\n🎯 SUMMARY")
    print("="*30)
    
    if success:
        print("✅ Institutional filters are working correctly!")
        print("🏛️ Your signals now have professional-grade protection against:")
        print("   • False breakout signals")
        print("   • High-risk reversal attempts")
        print("   • Momentum continuation patterns")
        print("   • Volume-confirmed breakouts")
        print("   • Volatility expansion scenarios")
        print()
        print("💡 Use the enhanced backtester for higher quality signals!")
    else:
        print("❌ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
