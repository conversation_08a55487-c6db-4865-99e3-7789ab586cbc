"""
COMPARE ULTRA-OPTIMIZED BACKTESTER VS ORIGINAL VER4

This script compares the results of the ultra-optimized batch processing backtester
with the original Ver4 backtester to validate accuracy and performance.
"""

import os
import sys
import json
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from intelligent_vectorized_backtester import IntelligentVectorizedBacktester
from run_backtester_v4_standalone import run_backtester_v4_analysis

def compare_backtesters():
    """Compare ultra-optimized vs Ver4 backtester"""
    print("🔬 ULTRA-OPTIMIZED VS VER4 BACKTESTER COMPARISON")
    print("=" * 60)
    
    # Test parameters
    ticker = "BATAINDIA"
    exchange = "NSE"
    start = "12:00"
    end = "12:30"  # Shorter test for comparison
    date = "20-06-2025"
    tokenid = "11630"
    
    print(f"📊 Testing: {ticker} on {date}")
    print(f"⏰ Time: {start} to {end}")
    print(f"🔢 Token: {tokenid}")
    print()
    
    results = {}
    
    try:
        # Test 1: Ultra-Optimized Backtester
        print("🚀 TESTING ULTRA-OPTIMIZED BACKTESTER")
        print("-" * 40)
        
        start_time = datetime.now()
        
        ultra_backtester = IntelligentVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start=start,
            end=end,
            date=date,
            tokenid=tokenid
        )
        
        ultra_results = ultra_backtester.single_vectorized_signal_generation()
        ultra_time = (datetime.now() - start_time).total_seconds()
        
        if ultra_results['success']:
            print(f"✅ Ultra-Optimized completed in {ultra_time:.2f}s")
            print(f"📡 API Calls: {ultra_results['api_calls_used']}")
            print(f"🎯 Signals: {ultra_results['signals_generated']}")
            print(f"📥 Positions: {ultra_results['positions_opened']}")
            
            results['ultra_optimized'] = {
                'success': True,
                'execution_time': ultra_time,
                'api_calls': ultra_results['api_calls_used'],
                'signals': ultra_results['signals_generated'],
                'positions': ultra_results['positions_opened'],
                'signal_details': ultra_results['signals'],
                'position_details': ultra_results['positions']
            }
        else:
            print(f"❌ Ultra-Optimized failed: {ultra_results.get('error')}")
            results['ultra_optimized'] = {'success': False, 'error': ultra_results.get('error')}
            
    except Exception as e:
        print(f"❌ Ultra-Optimized error: {str(e)}")
        results['ultra_optimized'] = {'success': False, 'error': str(e)}
    
    print()
    
    try:
        # Test 2: Original Ver4 Backtester
        print("🔄 TESTING ORIGINAL VER4 BACKTESTER")
        print("-" * 40)
        
        start_time = datetime.now()
        
        ver4_results = run_backtester_v4_analysis(
            ticker=ticker,
            date=date,
            start=start,
            end=end,
            exchange=exchange,
            tokenid=tokenid
        )
        ver4_time = (datetime.now() - start_time).total_seconds()
        
        if ver4_results['success']:
            print(f"✅ Ver4 completed in {ver4_time:.2f}s")
            print(f"📡 API Calls: {ver4_results.get('api_calls_used', 'N/A')}")
            print(f"🎯 Signals: {ver4_results.get('signals_generated', 'N/A')}")
            print(f"📥 Positions: {ver4_results.get('positions_opened', 'N/A')}")
            
            results['ver4'] = {
                'success': True,
                'execution_time': ver4_time,
                'api_calls': ver4_results.get('api_calls_used', 0),
                'signals': ver4_results.get('signals_generated', 0),
                'positions': ver4_results.get('positions_opened', 0),
                'signal_details': ver4_results.get('signals', []),
                'position_details': ver4_results.get('positions', [])
            }
        else:
            print(f"❌ Ver4 failed: {ver4_results.get('error')}")
            results['ver4'] = {'success': False, 'error': ver4_results.get('error')}
            
    except Exception as e:
        print(f"❌ Ver4 error: {str(e)}")
        results['ver4'] = {'success': False, 'error': str(e)}
    
    # Comparison Analysis
    print("\n📊 COMPARISON ANALYSIS")
    print("=" * 50)
    
    if results.get('ultra_optimized', {}).get('success') and results.get('ver4', {}).get('success'):
        ultra = results['ultra_optimized']
        ver4 = results['ver4']
        
        print(f"⚡ Performance Comparison:")
        print(f"   Ultra-Optimized: {ultra['execution_time']:.2f}s")
        print(f"   Ver4 Original:   {ver4['execution_time']:.2f}s")
        if ver4['execution_time'] > 0:
            speedup = ver4['execution_time'] / ultra['execution_time']
            print(f"   🚀 Speedup:       {speedup:.1f}x faster")
        
        print(f"\n📡 API Calls Comparison:")
        print(f"   Ultra-Optimized: {ultra['api_calls']}")
        print(f"   Ver4 Original:   {ver4['api_calls']}")
        if ver4['api_calls'] > 0:
            reduction = ver4['api_calls'] / ultra['api_calls']
            print(f"   📊 Reduction:     {reduction:.1f}x fewer calls")
        
        print(f"\n🎯 Signal Accuracy Comparison:")
        print(f"   Ultra-Optimized: {ultra['signals']} signals")
        print(f"   Ver4 Original:   {ver4['signals']} signals")
        
        signal_match = ultra['signals'] == ver4['signals']
        print(f"   ✅ Signal Count Match: {signal_match}")
        
        print(f"\n📥 Position Accuracy Comparison:")
        print(f"   Ultra-Optimized: {ultra['positions']} positions")
        print(f"   Ver4 Original:   {ver4['positions']} positions")
        
        position_match = ultra['positions'] == ver4['positions']
        print(f"   ✅ Position Count Match: {position_match}")
        
        # Overall accuracy
        overall_accuracy = signal_match and position_match
        print(f"\n🎯 OVERALL ACCURACY: {'✅ PERFECT MATCH' if overall_accuracy else '❌ DIFFERENCES DETECTED'}")
        
    else:
        print("❌ Cannot compare - one or both tests failed")
    
    # Save comparison results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"ultra_optimized_vs_ver4_comparison_{ticker}_{date.replace('-', '')}_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Comparison results saved to: {filename}")

if __name__ == "__main__":
    compare_backtesters()
