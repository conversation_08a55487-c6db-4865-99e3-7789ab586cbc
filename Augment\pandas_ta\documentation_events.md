Title: Events - Pandas TA

URL Source: https://www.pandas-ta.dev/api/events/

Published Time: Tue, 10 Jun 2025 22:38:27 GMT

Markdown Content:
Additional functions that attempt to identify states (above/below) or events (crossing).

above[#](https://www.pandas-ta.dev/api/events/#src.pandas_ta.utils._signals.above "Permanent link")
---------------------------------------------------------------------------------------------------

```
above(
    x: Series,
    y: Series,
    asint: bool = True,
    offset: Int = None,
    **kwargs,
) -> Series
```

Above

Determines if each `x` value is above (or `>=`) each `y` value.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `x` | `Series` | `x` | _required_ |
| `y` | `Series` | `y` | _required_ |
| `asint` | `bool` | Returns as `Int`. | `True` |
| `offset` | `Int` | Post shift. Default: `0` | `None` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | State where `x >= y`. |

Example

```
x = Series([4, 2, 0, -1, 1])
y = Series([1, 1, 1, 1, 1])

x_above_y = ta.above(x, y)
# x_above_y = Series([1, 1, 0, 0, 1])
```

above_value[#](https://www.pandas-ta.dev/api/events/#src.pandas_ta.utils._signals.above_value "Permanent link")
---------------------------------------------------------------------------------------------------------------

```
above_value(
    x: Series,
    value: IntFloat,
    asint: bool = True,
    offset: Int = None,
    **kwargs,
) -> Series
```

Above Value

Determines if each `x` value is above (or `>=`) a constant `value`.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `x` | `Series` | `x` | _required_ |
| `value` | `IntFloat` | Value to compare with `x`. | _required_ |
| `asint` | `bool` | Returns as `Int`. | `True` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | State where `x >= y`. |

Example

```
x = Series([4, 2, 0, -1, 1])
x_above_1 = ta.above_value(x, 1)
# x_above_1 = Series([1, 1, 0, 0, 1])
```

below[#](https://www.pandas-ta.dev/api/events/#src.pandas_ta.utils._signals.below "Permanent link")
---------------------------------------------------------------------------------------------------

```
below(
    x: Series,
    y: Series,
    asint: bool = True,
    offset: Int = None,
    **kwargs,
) -> Series
```

Below

Determines if each `x` value is below (or `<=`) each `y` value.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `x` | `Series` | `x` | _required_ |
| `y` | `Series` | `y` | _required_ |
| `asint` | `bool` | Returns as `Int`. | `True` |
| `offset` | `Int` | Post shift. Default: `0` | `None` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | State where `x <= y`. |

Example

```
x = Series([4, 2, 0, -1, 1])
y = Series([1, 1, 1, 1, 1])

x_below_y = ta.below(x, y)
# x_below_y = Series([0, 0, 1, 1, 1])
```

below_value[#](https://www.pandas-ta.dev/api/events/#src.pandas_ta.utils._signals.below_value "Permanent link")
---------------------------------------------------------------------------------------------------------------

```
below_value(
    x: Series,
    value: IntFloat,
    asint: bool = True,
    offset: Int = None,
    **kwargs,
) -> Series
```

Below Value

Determines if each `x` value is below (or `<=`) a constant `value`.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `x` | `Series` | `x` | _required_ |
| `value` | `IntFloat` | Value to compare with `x`. | _required_ |
| `asint` | `bool` | Returns as `Int`. | `True` |
| `offset` | `Int` | Post shift. Default: `0` | `None` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | State where `x <= y`. |

Example

```
x = Series([4, 2, 0, -1, 1])
x_below_1 = ta.below_value(x, 1)
# x_below_1 = Series([0, 0, 1, 1, 1])
```

cross[#](https://www.pandas-ta.dev/api/events/#src.pandas_ta.utils._signals.cross "Permanent link")
---------------------------------------------------------------------------------------------------

```
cross(
    x: Series,
    y: Series,
    above: bool = True,
    equal: bool = True,
    asint: bool = True,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Cross

Determines where `x` crosses `y`, either _above_ or _below_, strictly (_equal_) or not.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `x` | `Series` | `x` | _required_ |
| `y` | `Series` | `y` | _required_ |
| `above` | `bool` | Check above. Check below, set `above=False` | `True` |
| `equal` | `bool` | At least/most, `=`, check. | `True` |
| `asint` | `bool` | Returns as `Int`. | `True` |
| `offset` | `Int` | Post shift. Default: `0` | `None` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | Values where `x` crosses `y`. |

Example

```
x = Series([4, 2, 0, -1, 1])
y = Series([1, 1, 1, 1, 1])

# Cross Above Examples
x_xae_y = ta.cross(x, y, above=True, equal=True)
# x_xae_y = Series([0, 0, 0, 0, 1])

x_xa_y = ta.cross(x, y, above=True, equal=False)
# x_xa_y = Series([0, 0, 0, 0, 0])

# Cross Below Examples
x_xbe_y = ta.cross(x, y, above=False, equal=True)
# x_xbe_y = Series([0, 0, 1, 0, 1])

x_xb_y = ta.cross(x, y, above=False, equal=False)
# x_xb_y = Series([0, 0, 1, 0, 0])
```

cross_value[#](https://www.pandas-ta.dev/api/events/#src.pandas_ta.utils._signals.cross_value "Permanent link")
---------------------------------------------------------------------------------------------------------------

```
cross_value(
    x: Series,
    value: IntFloat,
    above: bool = True,
    equal: bool = True,
    asint: bool = True,
    offset: Int = None,
    **kwargs,
) -> Series
```

Cross Value

Determines where `x` crosses a constant `value`, either _above_ or _below_, strictly (_equal_) or not.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `x` | `Series` | `x` | _required_ |
| `value` | `IntFloat` | Value to compare with `x`. | _required_ |
| `above` | `bool` | Check above. Check below, set `above=False` | `True` |
| `equal` | `bool` | At least/most, `=`, check. | `True` |
| `asint` | `bool` | Returns as `Int`. | `True` |
| `offset` | `Int` | Post shift. Default: `0` | `None` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | Values where `x` crosses `y`. |

Example

```
x = Series([4, 2, 0, -1, 1])

# Cross Above Examples
x_xae_y = ta.cross_value(x, 1, above=True, equal=True)
# x_xae_y = Series([0, 0, 0, 0, 1])

x_xa_y = ta.cross_value(x, 1, above=True, equal=False)
# x_xa_y = Series([0, 0, 0, 0, 0])

# Cross Below Examples
x_xbe_y = ta.cross_value(x, 1, above=False, equal=True)
# x_xbe_y = Series([0, 0, 1, 0, 1])

x_xb_y = ta.cross_value(x, 1, above=False, equal=False)
# x_xb_y = Series([0, 0, 1, 0, 0])
```

signals[#](https://www.pandas-ta.dev/api/events/#src.pandas_ta.utils._signals.signals "Permanent link")
-------------------------------------------------------------------------------------------------------

```
signals(
    indicator: Series,
    xa: IntFloat = None,
    xb: IntFloat = None,
    cross_values: bool = None,
    xseries: Series = None,
    xseries_a: Series = None,
    xseries_b: Series = None,
    cross_series: bool = None,
    offset: Int = None,
) -> DataFrame
```

Signals

Mulitfuncational signal checker that determines whether an indicator crosses above/below value or Series.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `indicator` | `Series` | Indicator to check for signal crossings. | _required_ |
| `cross_values` | `bool` | Check if crossed value. | `None` |
| `xseries` | `Series` | Cross Series | `None` |
| `xseries_a` | `Series` | Cross Above Series | `None` |
| `xseries_b` | `Series` | Cross Below Series | `None` |
| `cross_series` | `bool` | Check if crossed `xseries`. | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `xa` | `IntFloat` | Crossing above value. |
| `xb` | `IntFloat` | Crossing below value. |
| `offset` | `Int` | Post shift. Default: `0` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

Note
See sources of: `er`, `macd`, `rsi`, and `rsx` for examples of use.

tsignals[#](https://www.pandas-ta.dev/api/events/#src.pandas_ta.utils._signals.tsignals "Permanent link")
---------------------------------------------------------------------------------------------------------

```
tsignals(
    trend: Series,
    asbool: bool = None,
    trade_offset: Int = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Trend Signals

This function creates Trend, Trades, Entries and Exit values per bar when given a trend condition e.g. `trend = close > sma(close, 50)`.

Source
*   Kevin Johnson

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `trend` | `Series` | `trend` Series. Boolean or integer values of `0` and `1` | _required_ |
| `asbool` | `bool` | Return booleans. Default: `False` | `None` |
| `trade_offset` | `value` | Shift trade entries/exits with live: `0` and backesting: `1`. Default: `0` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 4 columns |

Column Detail
*   Trends (trend: 1, no trend: 0)
*   Trades (Enter: 1, Exit: -1, Otherwise: 0)
*   Entries (entry: 1, nothing: 0)
*   Exits (exit: 1, nothing: 0)

Details
A `trend` is a state or condition, that is as simple as `Close > MA` or something more complex that has boolean or integer (trend: 1, no trend: 0) values.

VectorBT
*   For backtesting, set `trade_offset=1`.
*   Setting `asbool=True` is useful for backtesting with vectorbt's `Portfolio.from_signal(close, entries, exits)` method.

Example
These are two different outcomes for each (long/short) position and depends on the source and it's behavior.

Signals when `Close > SMA50(Close)`

```
ta.tsignals(close > ta.sma(close, 50), asbool=False)
```

Signals when `EMA(Close, 8) > EMA(Close, 21)`

```
ta.tsignals(ta.ema(close, 8) > ta.ema(close, 21), asbool=True)
```

Warning
Check ALL outcomes BEFORE making an Issue

xsignals[#](https://www.pandas-ta.dev/api/events/#src.pandas_ta.utils._signals.xsignals "Permanent link")
---------------------------------------------------------------------------------------------------------

```
xsignals(
    source: Series,
    xa: IntFloat | Series,
    xb: IntFloat | Series,
    above: bool = True,
    long: bool = True,
    asbool: bool = None,
    trade_offset: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Cross Signals

This function creates Trend, Trades, Entries and Exits values per bar for crossing events.

Sources
*   Kevin Johnson

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `source` | `Series` | `source` Signal | _required_ |
| `xa` | `Series` | Series the Signal crosses above if `above=True` | _required_ |
| `xb` | `Series` | Series the Signal crosses below if `above=True` | _required_ |
| `above` | `bool` | The `source` crossing; below is `False`. Default: `True` | `True` |
| `long` | `bool` | The `source` position; short is `False`. Default: `True` | `True` |
| `offset` | `int` | Post shift. Default: `0` | `None` |
| `asbool` | `bool` | Return booleans. Default: `False` | `None` |
| `trade_offset` | `value` | Shift trade entries/exits with live: `0` and backesting: `1`. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 4 columns |

Column Detail
*   Trends (trend: 1, no trend: 0)
*   Trades (Enter: 1, Exit: -1, Otherwise: 0)
*   Entries (entry: 1, nothing: 0)
*   Exits (exit: 1, nothing: 0)

VectorBT
*   For backtesting, set `trade_offset=1`.
*   Setting `asbool=True` is useful for backtesting with vectorbt's `Portfolio.from_signal(close, entries, exits)` method.

Example
These are two different outcomes for each (long/short) position and depends on the source and it's behavior.

```
rsi = df.ta.rsi()
```

When RSI crosses above 20 and then below 80 in a long position:

```
ta.xsignals(source=rsi, xa=20, xb=80, above=True, long=True)
# Simpler
# ta.xsignals(rsi, 20, 80, True, True)
```

When RSI crosses below 20 and then above 80 in a long position:

```
ta.xsignals(source=rsi, xa=20, xb=80, above=False, long=True)
# Simpler
# ta.xsignals(rsi, 20, 80, False, True)
```

*   Similarly, short positions (`long=False`) also differ depending on `above` state.

Warning
Check ALL parameter combination outcomes BEFORE making an Issue.

IMPORTANT

**Thanks** to all those that have sponsored and dontated to the library in the past! Your support has been greatly appreciated! ![Image 1: 🙏](https://www.pandas-ta.dev/assets/external/cdn.jsdelivr.net/gh/jdecked/twemoji@15.1.0/assets/svg/1f64f.svg)

Only **Installation Bugs/Issues** will addressed for releases between versions _0.4.25b_ and _0.4.66b_. Releases beyond version _0.4.66b_ will **only** be released after **significant** donations, sponsorships or yearly subscriptions have been received via [Buy Me a Coffee](https://www.buymeacoffee.com/twopirllc).

Support Tiers coming soon!