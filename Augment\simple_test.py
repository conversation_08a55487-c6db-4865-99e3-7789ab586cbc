"""
Simple Test for Enhanced Backtester

This script directly tests the key functions without running the main CLI.
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_token_search():
    """Test token search functionality"""
    print("🧪 Testing token search functionality...")
    
    try:
        from smart_vectorized_backtester import get_token_info
        from shared_api_manager import get_api
        
        api = get_api()
        
        # Test NSE
        print("🔍 Testing NSE - BATAINDIA...")
        token_info = get_token_info(api, 'BATAINDIA', 'NSE')
        if token_info:
            print(f"✅ NSE Success: {token_info['tsym']} (Token: {token_info['token']})")
        else:
            print("❌ NSE Failed")
        
        # Test MCX
        print("🔍 Testing MCX - SILVER...")
        token_info = get_token_info(api, 'SILVER', 'MCX')
        if token_info:
            print(f"✅ MCX Success: {token_info['tsym']} (Token: {token_info['token']})")
        else:
            print("❌ MCX Failed")
        
        # Test invalid
        print("🔍 Testing Invalid - INVALIDTICKER...")
        token_info = get_token_info(api, 'INVALIDTICKER', 'NSE')
        if token_info is None:
            print("✅ Invalid ticker correctly rejected")
        else:
            print("❌ Invalid ticker was accepted")
        
        return True
        
    except Exception as e:
        print(f"❌ Token search test failed: {str(e)}")
        return False

def test_backtester_creation():
    """Test backtester creation with different exchanges"""
    print("\n🧪 Testing backtester creation...")
    
    try:
        from smart_vectorized_backtester import SmartVectorizedBacktester
        from shared_api_manager import get_api
        
        api = get_api()
        
        # Test with NSE
        print("🔍 Testing NSE backtester creation...")
        try:
            backtester = SmartVectorizedBacktester(
                ticker='BATAINDIA',
                exchange='NSE',
                start='09:15',
                end='10:15',
                date='25-06-2025',
                tokenid='371',  # Known BATAINDIA token
                enable_momentum_validation=True,
                enable_realtime_detection=True
            )
            print("✅ NSE backtester created successfully")
        except Exception as e:
            print(f"❌ NSE backtester failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Backtester creation test failed: {str(e)}")
        return False

def test_time_functions():
    """Test time-related functions"""
    print("\n🧪 Testing time functions...")
    
    try:
        from smart_vectorized_backtester import get_current_time_info
        
        current_date, current_time = get_current_time_info()
        print(f"✅ Current time functions work: {current_date} {current_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ Time functions test failed: {str(e)}")
        return False

def main():
    """Run simple tests"""
    print("🚀 Starting Simple Enhanced Backtester Tests...")
    print("="*60)
    
    results = []
    
    # Test time functions (no API required)
    results.append(("Time Functions", test_time_functions()))
    
    # Test token search
    results.append(("Token Search", test_token_search()))
    
    # Test backtester creation
    results.append(("Backtester Creation", test_backtester_creation()))
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST RESULTS")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed.")

if __name__ == "__main__":
    main()
