"""
Compare Nadarya Watson Implementations

This script compares the standalone enhanced Nadarya Watson implementation
with the vectorized implementation to identify the differences.
"""

import sys
import os
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def compare_implementations():
    """Compare standalone vs vectorized Nadarya Watson implementations"""
    
    from shared_api_manager import get_api
    from enhanced_nadarya_watson_signal import check_vander_enhanced
    from intelligent_vectorized_backtester import IntelligentVectorizedBacktester
    
    # Test parameters
    ticker = 'BATAINDIA'
    date = '24-06-2025'
    start_time = '09:15'
    end_time = '12:31'  # Test with 12:31 which should have lower band signal
    exchange = 'NSE'
    tokenid = '371'
    
    print("\n" + "="*100)
    print("🔍 COMPARING STANDALONE vs VECTORIZED NADARYA WATSON IMPLEMENTATIONS")
    print("="*100)
    
    # Test 1: Standalone Enhanced Implementation
    print(f"\n1️⃣ TESTING STANDALONE ENHANCED IMPLEMENTATION")
    print("-" * 60)
    
    try:
        is_vander_standalone, vander_text_standalone = check_vander_enhanced(
            tokenid=tokenid,
            exchange=exchange,
            date_input=date,
            starttime_input=start_time,
            endtime_input=end_time,
            enable_momentum_validation=False,  # Disable for pure comparison
            enable_realtime_detection=True
        )
        
        print(f"✅ Standalone Result: {is_vander_standalone}")
        print(f"📝 Standalone Text: {vander_text_standalone}")
        
    except Exception as e:
        print(f"❌ Standalone Error: {str(e)}")
        is_vander_standalone = False
        vander_text_standalone = f"Error: {str(e)}"
    
    # Test 2: Vectorized Implementation
    print(f"\n2️⃣ TESTING VECTORIZED IMPLEMENTATION")
    print("-" * 60)
    
    try:
        # Create vectorized backtester
        backtester = IntelligentVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start=start_time,
            end=end_time,
            date=date,
            tokenid=tokenid,
            enable_momentum_validation=False,  # Disable for pure comparison
            enable_realtime_detection=True
        )
        
        # Get the data
        api = get_api()
        from enhanced_nadarya_watson_signal import get_start_end_timestamps
        start_timestamp, end_timestamp = get_start_end_timestamps(date, start_time, end_time)
        
        data = api.get_time_price_series(
            exchange='NSE', 
            token=tokenid, 
            starttime=start_timestamp, 
            endtime=end_timestamp, 
            interval=1
        )
        
        from enhanced_nadarya_watson_signal import live_data
        data_df = live_data(data)
        data_df = data_df.sort_values(by='time')
        
        print(f"📊 Data shape: {data_df.shape}")
        print(f"📊 Data range: {data_df.index[0]} to {data_df.index[-1]}")
        print(f"📊 Last few closes: {data_df['Close'].tail().values}")
        
        # Test vectorized Nadarya Watson calculation
        signal = backtester._calculate_nadarya_watson_signal_vectorized(
            data_df, 
            1.75, 
            enable_momentum_validation=False,
            enable_realtime_detection=True,
            debug_time="12:31"
        )
        
        print(f"✅ Vectorized Result: {signal}")
        
    except Exception as e:
        print(f"❌ Vectorized Error: {str(e)}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        signal = 0
    
    # Test 3: Direct Algorithm Comparison
    print(f"\n3️⃣ DIRECT ALGORITHM COMPARISON")
    print("-" * 60)
    
    try:
        # Get the same data for both
        api = get_api()
        from enhanced_nadarya_watson_signal import get_start_end_timestamps
        start_timestamp, end_timestamp = get_start_end_timestamps(date, start_time, end_time)
        
        data = api.get_time_price_series(
            exchange='NSE', 
            token=tokenid, 
            starttime=start_timestamp, 
            endtime=end_timestamp, 
            interval=1
        )
        
        from enhanced_nadarya_watson_signal import live_data
        data_df = live_data(data)
        data_df = data_df.sort_values(by='time')
        close_prices = data_df['Close'].values
        
        print(f"📊 Using {len(close_prices)} data points")
        print(f"📊 Close price range: {close_prices.min():.2f} to {close_prices.max():.2f}")
        print(f"📊 Last close: {close_prices[-1]:.2f}")
        
        # Implement the exact same algorithm as standalone
        import math
        import numpy as np
        
        h = 8
        k = 1.75
        src = close_prices
        y = []
        
        # Step 1: Calculate Nadarya Watson curve
        sum_e = 0
        for i in range(len(close_prices)):
            sum_val = 0
            sumw = 0
            for j in range(len(close_prices)):
                w = math.exp(-(math.pow(i-j, 2)/(h*h*2)))
                sum_val += src[j] * w
                sumw += w
            y2 = sum_val / sumw
            sum_e += abs(src[i] - y2)
            y.append(y2)
        
        # Step 2: Calculate MAE
        mae = sum_e / len(close_prices) * k
        
        print(f"📊 MAE: {mae:.4f}")
        print(f"📊 Last few y values: {y[-5:]}")
        
        # Step 3: Calculate bands and signals
        upper_band = []
        lower_band = []
        upper_band_signal = []
        lower_band_signal = []
        
        for i in range(len(close_prices)):
            upper_band.append(y[i] + mae * k)
            lower_band.append(y[i] - mae * k)
            
            if close_prices[i] > upper_band[i]:
                upper_band_signal.append(close_prices[i])
            else:
                upper_band_signal.append(np.nan)
                
            if close_prices[i] < lower_band[i]:
                lower_band_signal.append(close_prices[i])
            else:
                lower_band_signal.append(np.nan)
        
        # Check last few values
        print(f"\n📊 LAST 5 MINUTES ANALYSIS:")
        for i in range(max(0, len(close_prices)-5), len(close_prices)):
            time_idx = i
            close_val = close_prices[i]
            upper_val = upper_band[i]
            lower_val = lower_band[i]
            upper_touch = "YES" if close_val > upper_val else "NO"
            lower_touch = "YES" if close_val < lower_val else "NO"
            upper_sig = "YES" if not np.isnan(upper_band_signal[i]) else "NO"
            lower_sig = "YES" if not np.isnan(lower_band_signal[i]) else "NO"
            
            print(f"   {time_idx}: Close={close_val:.2f}, Upper={upper_val:.2f}, Lower={lower_val:.2f}")
            print(f"        Upper touch: {upper_touch}, Lower touch: {lower_touch}")
            print(f"        Upper signal: {upper_sig}, Lower signal: {lower_sig}")
        
        # Check current minute
        current_idx = len(close_prices) - 1
        current_upper_signal = not np.isnan(upper_band_signal[current_idx])
        current_lower_signal = not np.isnan(lower_band_signal[current_idx])
        
        print(f"\n🎯 CURRENT MINUTE ({current_idx}) ANALYSIS:")
        print(f"   Close: {close_prices[current_idx]:.2f}")
        print(f"   Upper band: {upper_band[current_idx]:.2f}")
        print(f"   Lower band: {lower_band[current_idx]:.2f}")
        print(f"   Upper signal: {current_upper_signal}")
        print(f"   Lower signal: {current_lower_signal}")
        
        if current_upper_signal:
            expected_signal = -1  # PUT
            signal_type = "PUT (Upper band)"
        elif current_lower_signal:
            expected_signal = 1   # CALL
            signal_type = "CALL (Lower band)"
        else:
            expected_signal = 0
            signal_type = "NONE"
        
        print(f"   Expected signal: {expected_signal} ({signal_type})")
        
    except Exception as e:
        print(f"❌ Direct comparison error: {str(e)}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        expected_signal = 0
    
    # Summary
    print(f"\n" + "="*100)
    print("📊 COMPARISON SUMMARY")
    print("="*100)
    print(f"Standalone result: {is_vander_standalone}")
    print(f"Vectorized result: {signal}")
    print(f"Expected result: {expected_signal}")
    print(f"Standalone matches expected: {'✅' if (is_vander_standalone != 0) == (expected_signal != 0) else '❌'}")
    print(f"Vectorized matches expected: {'✅' if signal == expected_signal else '❌'}")
    
    if signal != expected_signal:
        print(f"\n❌ VECTORIZED IMPLEMENTATION IS BROKEN!")
        print(f"   Expected: {expected_signal}")
        print(f"   Got: {signal}")
        print(f"   The vectorized Nadarya Watson calculation is not working correctly.")
    else:
        print(f"\n✅ VECTORIZED IMPLEMENTATION IS WORKING!")

def main():
    """Main execution function"""
    logger.info("🚀 Starting Nadarya Watson implementation comparison...")
    
    compare_implementations()
    
    logger.info("🎉 Comparison completed!")

if __name__ == "__main__":
    main()
