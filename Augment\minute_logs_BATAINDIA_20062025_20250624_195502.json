{"ticker": "BATAINDIA", "date": "20-06-2025", "start_time": "12:00", "end_time": "12:30", "total_minutes": 31, "minute_logs": [{"minute": 1, "time": "12:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": true, "stage1_nadarya": false, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage 1 failed", "action_taken": "STAGE1_FAILED", "notes": ""}, {"minute": 2, "time": "12:01", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": true, "stage1_nadarya": false, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage 1 failed", "action_taken": "STAGE1_FAILED", "notes": ""}, {"minute": 3, "time": "12:02", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": true, "stage1_nadarya": true, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": true, "stage2_nadarya": false, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage 2 failed", "action_taken": "STAGE2_FAILED", "notes": ""}, {"minute": 4, "time": "12:03", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": true, "stage1_nadarya": false, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage 1 failed", "action_taken": "STAGE1_FAILED", "notes": ""}, {"minute": 5, "time": "12:04", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": true, "stage1_nadarya": true, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": true, "stage2_nadarya": true, "stage2_pass": true, "signal_generated": 1, "signal_reason": "Lower band signal - CALL", "action_taken": "SIGNAL_CALL", "notes": ""}, {"minute": 6, "time": "12:05", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 7, "time": "12:06", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 8, "time": "12:07", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 9, "time": "12:08", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 10, "time": "12:09", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 11, "time": "12:10", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 12, "time": "12:11", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 13, "time": "12:12", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 14, "time": "12:13", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 15, "time": "12:14", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 16, "time": "12:15", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 17, "time": "12:16", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 18, "time": "12:17", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 19, "time": "12:18", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 20, "time": "12:19", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 21, "time": "12:20", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 22, "time": "12:21", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 23, "time": "12:22", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 24, "time": "12:23", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 25, "time": "12:24", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 26, "time": "12:25", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 27, "time": "12:26", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 28, "time": "12:27", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 29, "time": "12:28", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 30, "time": "12:29", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}, {"minute": 31, "time": "12:30", "has_position": true, "position_type": "CALL", "stage1_checked": false, "stage1_sideways": null, "stage1_nadarya": null, "stage1_pass": false, "stage2_checked": false, "stage2_sideways": null, "stage2_nadarya": null, "stage2_pass": false, "signal_generated": 0, "signal_reason": "", "action_taken": "SKIP_SIGNAL_CHECK", "notes": "Position active: CALL since 5"}], "position_events": [{"event": "POSITION_OPENED", "time": "12:04", "minute": 5, "position_type": "CALL", "reason": "CALL signal detected"}], "summary": {"total_minutes": 31, "stage1_checks": 5, "stage1_passes": 2, "stage1_pass_rate": 0.4, "stage2_checks": 2, "stage2_passes": 1, "stage2_pass_rate": 0.5, "signals_generated": 1, "call_signals": 1, "put_signals": 0, "signal_rate": 0.03225806451612903, "positions_opened": 1, "positions_closed": 0}}