"""
Quick API Test Script

This script tests the basic API functionality to ensure the shared API manager
works correctly with the actual ShoonyaApi methods.
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic_api_functionality():
    """Test basic API functionality"""
    try:
        logger.info("🧪 Testing basic API functionality...")
        
        # Import shared API manager
        from shared_api_manager import get_api, get_manager
        
        # Get API instance
        logger.info("🔐 Getting API instance...")
        api = get_api()
        logger.info("✅ API instance obtained")
        
        # Test 1: Get limits (basic connectivity test)
        logger.info("💰 Testing get_limits()...")
        limits = api.get_limits()
        if limits and limits.get('stat') == 'Ok':
            logger.info("✅ get_limits() successful")
            logger.info(f"📊 Cash available: {limits.get('cash', 'N/A')}")
        else:
            logger.error(f"❌ get_limits() failed: {limits}")
            return False
        
        # Test 2: Search for NIFTY symbol
        logger.info("🔍 Testing searchscrip() for NIFTY...")
        search_result = api.searchscrip(exchange='NSE', searchtext='NIFTY-EQ')
        if search_result and 'values' in search_result and search_result['values']:
            nifty_token = search_result['values'][0]['token']
            nifty_symbol = search_result['values'][0]['tsym']
            logger.info(f"✅ Found NIFTY: Token={nifty_token}, Symbol={nifty_symbol}")
        else:
            logger.error(f"❌ searchscrip() failed: {search_result}")
            return False
        
        # Test 3: Get quotes for NIFTY
        logger.info("📈 Testing get_quotes() for NIFTY...")
        quotes = api.get_quotes(exchange='NSE', token=nifty_token)
        if quotes and quotes.get('stat') == 'Ok':
            ltp = quotes.get('lp', 'N/A')
            logger.info(f"✅ NIFTY LTP: {ltp}")
        else:
            logger.error(f"❌ get_quotes() failed: {quotes}")
            return False
        
        # Test 4: Get time series data
        logger.info("📊 Testing get_time_price_series()...")
        
        # Get timestamps for yesterday
        yesterday = datetime.now() - timedelta(days=1)
        date_str = yesterday.strftime('%d-%m-%Y')
        
        # Create timestamps
        start_time = yesterday.replace(hour=9, minute=15, second=0, microsecond=0)
        end_time = yesterday.replace(hour=15, minute=30, second=0, microsecond=0)
        
        start_timestamp = start_time.timestamp()
        end_timestamp = end_time.timestamp()
        
        time_series = api.get_time_price_series(
            exchange='NSE',
            token=nifty_token,
            starttime=start_timestamp,
            endtime=end_timestamp,
            interval=1
        )
        
        if time_series and isinstance(time_series, list) and len(time_series) > 0:
            logger.info(f"✅ Time series data: {len(time_series)} candles")
            logger.info(f"📊 First candle: {time_series[0]}")
        else:
            logger.warning(f"⚠️ Time series data limited or empty: {len(time_series) if time_series else 0} candles")
        
        # Test 5: Test different ticker resolution
        logger.info("🔍 Testing ticker resolution for BATAINDIA...")
        search_result = api.searchscrip(exchange='NSE', searchtext='BATAINDIA-EQ')
        if search_result and 'values' in search_result and search_result['values']:
            bata_token = search_result['values'][0]['token']
            bata_symbol = search_result['values'][0]['tsym']
            logger.info(f"✅ Found BATAINDIA: Token={bata_token}, Symbol={bata_symbol}")
            
            # Get quotes for BATAINDIA
            bata_quotes = api.get_quotes(exchange='NSE', token=bata_token)
            if bata_quotes and bata_quotes.get('stat') == 'Ok':
                bata_ltp = bata_quotes.get('lp', 'N/A')
                logger.info(f"✅ BATAINDIA LTP: {bata_ltp}")
            else:
                logger.warning(f"⚠️ BATAINDIA quotes failed: {bata_quotes}")
        else:
            logger.warning(f"⚠️ BATAINDIA not found: {search_result}")
        
        # Test 6: Session info
        logger.info("📋 Testing session info...")
        manager = get_manager()
        session_info = manager.get_session_info()
        logger.info(f"✅ Session info: {session_info}")
        
        logger.info("🎉 All basic API tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ API test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_option_chain():
    """Test option chain functionality"""
    try:
        logger.info("🧪 Testing option chain functionality...")
        
        from shared_api_manager import get_api
        api = get_api()
        
        # Get NIFTY current price first
        search_result = api.searchscrip(exchange='NSE', searchtext='NIFTY-EQ')
        if not (search_result and 'values' in search_result and search_result['values']):
            logger.error("❌ Could not find NIFTY")
            return False
        
        nifty_token = search_result['values'][0]['token']
        quotes = api.get_quotes(exchange='NSE', token=nifty_token)
        if not (quotes and quotes.get('stat') == 'Ok'):
            logger.error("❌ Could not get NIFTY quotes")
            return False
        
        current_price = float(quotes.get('lp', 0))
        logger.info(f"📊 NIFTY current price: {current_price}")
        
        # Calculate strike price
        strike_price = round(current_price / 100) * 100
        logger.info(f"🎯 Strike price: {strike_price}")
        
        # Test option chain
        logger.info("🔗 Testing get_option_chain()...")
        
        # Use a typical NIFTY expiry format
        symbol_expiry = '26DEC24'  # Adjust this based on current date
        future_symbol = f"NIFTY{symbol_expiry}F"
        
        option_chain = api.get_option_chain('NFO', future_symbol, strike_price, 10)
        
        if option_chain and 'values' in option_chain and option_chain['values']:
            logger.info(f"✅ Option chain retrieved: {len(option_chain['values'])} options")
            
            # Show first few options
            for i, option in enumerate(option_chain['values'][:3]):
                logger.info(f"📋 Option {i+1}: {option['tsym']} - Strike: {option['strprc']} - Type: {option['optt']}")
        else:
            logger.warning(f"⚠️ Option chain failed or empty: {option_chain}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Option chain test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main test execution"""
    logger.info("🚀 Starting Quick API Test...")
    
    # Test basic functionality
    basic_test_passed = test_basic_api_functionality()
    
    if basic_test_passed:
        logger.info("✅ Basic API tests passed, testing option chain...")
        option_test_passed = test_option_chain()
        
        if option_test_passed:
            logger.info("🎉 All tests passed! API is working correctly.")
        else:
            logger.warning("⚠️ Option chain test failed, but basic API works.")
    else:
        logger.error("❌ Basic API tests failed. Check your credentials and connection.")
    
    logger.info("🏁 Quick API test completed.")

if __name__ == "__main__":
    main()
