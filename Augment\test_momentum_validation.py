"""
Test Momentum Validation Across Multiple Time Periods

This script tests the momentum validation logic across multiple time periods
to demonstrate how it filters false positive signals.
"""

import sys
import os
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_multiple_periods():
    """Test signal detection across multiple time periods"""
    
    # Import functions
    from shared_api_manager import get_api
    from enhanced_nadarya_watson_signal import check_vander, check_vander_original
    from shared_sideways_signal_helper import check_sideways
    
    # Test parameters
    ticker = 'BATAINDIA'
    date = '24-06-2025'
    exchange = 'NSE'
    
    # Get token ID
    api = get_api()
    search_result = api.searchscrip(exchange='NSE', searchtext=ticker + '-EQ')
    if search_result and 'values' in search_result and search_result['values']:
        tokenid = search_result['values'][0]['token']
        logger.info(f"📊 Found token ID: {tokenid} for {ticker}")
    else:
        logger.error(f"❌ Could not find token ID for {ticker}")
        return
    
    # Test periods based on your examples
    test_periods = [
        {'start': '09:15', 'end': '12:30', 'description': 'Lower band signal (12:29)'},
        {'start': '09:15', 'end': '12:31', 'description': 'Lower band signal (12:30)'},
        {'start': '09:15', 'end': '12:32', 'description': 'Lower band signal (12:31)'},
        {'start': '09:15', 'end': '12:50', 'description': 'Upper band signal (12:49) - weak'},
        {'start': '09:15', 'end': '12:51', 'description': 'Upper band signal (12:49) - weak continuation'},
        {'start': '09:15', 'end': '12:54', 'description': 'Upper band signal - should be strong'},
        {'start': '09:15', 'end': '12:55', 'description': 'Upper band signal - should be strong'},
        {'start': '09:15', 'end': '12:56', 'description': 'Upper band signal - should be strong'},
    ]
    
    print("\n" + "="*120)
    print("📊 MOMENTUM VALIDATION TEST RESULTS")
    print("="*120)
    
    print(f"\n{'Period':<15} | {'Sideways':<8} | {'Original':<8} | {'Enhanced':<8} | {'Effect':<20} | {'Description':<30}")
    print("-" * 120)
    
    results = []
    
    for period in test_periods:
        try:
            start_time = period['start']
            end_time = period['end']
            description = period['description']
            
            # Test sideways
            is_sideways, sideways_text = check_sideways(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date,
                starttime_input=start_time,
                endtime_input=end_time
            )
            
            # Test original Nadarya Watson
            is_vander_orig, vander_text_orig = check_vander_original(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date,
                starttime_input=start_time,
                endtime_input=end_time
            )
            
            # Test enhanced Nadarya Watson
            is_vander_enh, vander_text_enh = check_vander(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date,
                starttime_input=start_time,
                endtime_input=end_time
            )
            
            # Determine effect
            if is_vander_orig == is_vander_enh:
                effect = "No change"
            elif is_vander_orig and not is_vander_enh:
                effect = "Filtered false positive"
            else:
                effect = "Enhanced detection"
            
            # Final signal (Ver4 logic: both sideways and nadarya must be true)
            final_orig = is_sideways and is_vander_orig
            final_enh = is_sideways and is_vander_enh
            
            result = {
                'period': f"{end_time}",
                'sideways': is_sideways,
                'original': is_vander_orig,
                'enhanced': is_vander_enh,
                'effect': effect,
                'description': description,
                'final_orig': final_orig,
                'final_enh': final_enh,
                'vander_text_orig': vander_text_orig,
                'vander_text_enh': vander_text_enh
            }
            
            results.append(result)
            
            # Print summary row
            print(f"{result['period']:<15} | {str(result['sideways']):<8} | {str(result['original']):<8} | {str(result['enhanced']):<8} | {result['effect']:<20} | {result['description']:<30}")
            
        except Exception as e:
            logger.error(f"❌ Error testing period {period}: {str(e)}")
    
    # Print detailed analysis
    print("\n" + "="*120)
    print("📋 DETAILED ANALYSIS")
    print("="*120)
    
    for i, result in enumerate(results, 1):
        print(f"\n{i}. Period ending at {result['period']} - {result['description']}")
        print(f"   Sideways: {result['sideways']}")
        print(f"   Original Nadarya: {result['original']} - {result['vander_text_orig']}")
        print(f"   Enhanced Nadarya: {result['enhanced']} - {result['vander_text_enh']}")
        print(f"   Effect: {result['effect']}")
        print(f"   Final Signal - Original: {result['final_orig']}, Enhanced: {result['final_enh']}")
        
        if result['original'] != result['enhanced']:
            print(f"   🎯 MOMENTUM VALIDATION EFFECT: {'Prevented false positive' if result['original'] and not result['enhanced'] else 'Enhanced detection'}")
    
    # Summary statistics
    total_tests = len(results)
    original_signals = sum(1 for r in results if r['final_orig'])
    enhanced_signals = sum(1 for r in results if r['final_enh'])
    filtered_signals = sum(1 for r in results if r['final_orig'] and not r['final_enh'])
    
    print(f"\n" + "="*120)
    print("📊 SUMMARY STATISTICS")
    print("="*120)
    print(f"Total test periods: {total_tests}")
    print(f"Original signals: {original_signals}")
    print(f"Enhanced signals: {enhanced_signals}")
    print(f"Filtered false positives: {filtered_signals}")
    print(f"False positive rate reduction: {(filtered_signals/original_signals*100) if original_signals > 0 else 0:.1f}%")
    
    if filtered_signals > 0:
        print(f"\n🎯 SUCCESS: Momentum validation filtered {filtered_signals} false positive signals!")
    else:
        print(f"\n📊 INFO: No false positives detected in this test set")
    
    return results

def test_specific_momentum_scenarios():
    """Test specific momentum scenarios to validate the logic"""
    
    print("\n" + "="*80)
    print("🧪 TESTING SPECIFIC MOMENTUM SCENARIOS")
    print("="*80)
    
    # Test scenarios with known momentum patterns
    scenarios = [
        {
            'closes': [1200, 1201, 1202],  # Strong upward momentum
            'band_type': 'upper',
            'expected': True,
            'description': 'Strong upward momentum (2 consecutive higher closes)'
        },
        {
            'closes': [1200, 1199, 1198],  # Strong downward momentum
            'band_type': 'lower',
            'expected': True,
            'description': 'Strong downward momentum (2 consecutive lower closes)'
        },
        {
            'closes': [1200, 1201, 1200],  # Weak momentum (only 1 strong move)
            'band_type': 'upper',
            'expected': False,  # Should fail with only 1 strong move (need 2)
            'description': 'Weak upward momentum (1 strong move, then reversal)'
        },
        {
            'closes': [1200, 1200, 1200],  # No momentum
            'band_type': 'upper',
            'expected': False,
            'description': 'No momentum (flat prices)'
        },
        {
            'closes': [1200, 1199, 1201],  # Mixed momentum
            'band_type': 'upper',
            'expected': False,  # Should fail with only 1 strong move (need 2)
            'description': 'Mixed momentum (down then up)'
        }
    ]
    
    from enhanced_nadarya_watson_signal import validate_momentum_strength
    
    for i, scenario in enumerate(scenarios, 1):
        has_momentum, momentum_text = validate_momentum_strength(
            scenario['closes'], 
            scenario['band_type']
        )
        
        result = "✅ PASS" if has_momentum == scenario['expected'] else "❌ FAIL"
        
        print(f"\n{i}. {scenario['description']}")
        print(f"   Closes: {scenario['closes']}")
        print(f"   Band type: {scenario['band_type']}")
        print(f"   Expected: {scenario['expected']}, Got: {has_momentum}")
        print(f"   Result: {result}")
        print(f"   Details: {momentum_text}")

def main():
    """Main execution function"""
    logger.info("🚀 Starting momentum validation tests...")
    
    # Test specific momentum scenarios first
    test_specific_momentum_scenarios()
    
    # Test multiple periods
    test_multiple_periods()
    
    logger.info("🎉 Momentum validation tests completed!")

if __name__ == "__main__":
    main()
