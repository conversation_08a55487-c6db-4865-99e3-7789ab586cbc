{"success": true, "ticker": "BATAINDIA", "analysis_period": "12:00-12:30", "date": "20-06-2025", "performance_comparison": {"minute_by_minute_time": 571.066113, "vectorized_time": 226.573761, "performance_improvement_factor": 2.5204423957988675, "time_savings_seconds": 344.492352, "time_savings_percentage": 60.324425518836556}, "api_usage_comparison": {"minute_by_minute_api_calls": 62, "vectorized_api_calls": 1, "api_reduction_factor": 62.0, "api_savings_percentage": 98.38709677419355}, "signal_accuracy": {"total_minutes_compared": 31, "signal_matches": 31, "signal_differences": 0, "accuracy_percentage": 100.0, "minute_by_minute_signals": 0, "vectorized_signals": 0, "signal_count_match": true}, "position_accuracy": {"minute_by_minute_positions": 0, "vectorized_positions": 0, "position_count_match": true}, "ver4_logic_validation": {"signal_accuracy_perfect": true, "signal_count_identical": true, "position_count_identical": true, "overall_logic_preserved": true}, "detailed_comparison": {"total_minutes_compared": 31, "signal_matches": 31, "signal_differences_count": 0, "accuracy_percentage": 100.0, "signal_differences": [], "detailed_comparison": [{"minute": 1, "time": "12:00", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 2, "time": "12:01", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 3, "time": "12:02", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": true, "vec_stage1_pass": true, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 4, "time": "12:03", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 5, "time": "12:04", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": true, "vec_stage1_pass": true, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 6, "time": "12:05", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": true, "vec_stage1_pass": true, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 7, "time": "12:06", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": true, "vec_stage1_pass": true, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 8, "time": "12:07", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 9, "time": "12:08", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 10, "time": "12:09", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": true, "vec_stage1_pass": true, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 11, "time": "12:10", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": true, "vec_stage1_pass": true, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 12, "time": "12:11", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": true, "vec_stage1_pass": true, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 13, "time": "12:12", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 14, "time": "12:13", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 15, "time": "12:14", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 16, "time": "12:15", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 17, "time": "12:16", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 18, "time": "12:17", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 19, "time": "12:18", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 20, "time": "12:19", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 21, "time": "12:20", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 22, "time": "12:21", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 23, "time": "12:22", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 24, "time": "12:23", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 25, "time": "12:24", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 26, "time": "12:25", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": true, "vec_stage1_pass": true, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 27, "time": "12:26", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 28, "time": "12:27", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 29, "time": "12:28", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 30, "time": "12:29", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}, {"minute": 31, "time": "12:30", "mb_signal": 0, "vec_signal": 0, "match": true, "mb_stage1_pass": false, "vec_stage1_pass": false, "mb_stage2_pass": false, "vec_stage2_pass": false}]}, "minute_by_minute_results": {"approach": "minute_by_minute", "execution_time": 571.066113, "signals": [{"minute": 1, "time": "12:00", "datetime": "2025-06-20 12:00:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 2, "time": "12:01", "datetime": "2025-06-20 12:01:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 3, "time": "12:02", "datetime": "2025-06-20 12:02:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 1, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 4, "time": "12:03", "datetime": "2025-06-20 12:03:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 5, "time": "12:04", "datetime": "2025-06-20 12:04:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 1, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 6, "time": "12:05", "datetime": "2025-06-20 12:05:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 1, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 7, "time": "12:06", "datetime": "2025-06-20 12:06:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 1, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 8, "time": "12:07", "datetime": "2025-06-20 12:07:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 9, "time": "12:08", "datetime": "2025-06-20 12:08:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 10, "time": "12:09", "datetime": "2025-06-20 12:09:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 1, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 11, "time": "12:10", "datetime": "2025-06-20 12:10:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 1, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 12, "time": "12:11", "datetime": "2025-06-20 12:11:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 1, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 13, "time": "12:12", "datetime": "2025-06-20 12:12:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 14, "time": "12:13", "datetime": "2025-06-20 12:13:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 15, "time": "12:14", "datetime": "2025-06-20 12:14:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 16, "time": "12:15", "datetime": "2025-06-20 12:15:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 17, "time": "12:16", "datetime": "2025-06-20 12:16:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 18, "time": "12:17", "datetime": "2025-06-20 12:17:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 19, "time": "12:18", "datetime": "2025-06-20 12:18:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 20, "time": "12:19", "datetime": "2025-06-20 12:19:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 21, "time": "12:20", "datetime": "2025-06-20 12:20:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 22, "time": "12:21", "datetime": "2025-06-20 12:21:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 23, "time": "12:22", "datetime": "2025-06-20 12:22:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 24, "time": "12:23", "datetime": "2025-06-20 12:23:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 25, "time": "12:24", "datetime": "2025-06-20 12:24:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 26, "time": "12:25", "datetime": "2025-06-20 12:25:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 1, "stage1_pass": true, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 27, "time": "12:26", "datetime": "2025-06-20 12:26:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 28, "time": "12:27", "datetime": "2025-06-20 12:27:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 29, "time": "12:28", "datetime": "2025-06-20 12:28:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 30, "time": "12:29", "datetime": "2025-06-20 12:29:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 31, "time": "12:30", "datetime": "2025-06-20 12:30:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}], "position_events": [], "summary": {"total_minutes": 31, "signals_generated": 0, "positions_opened": 0, "estimated_api_calls": 62}}, "vectorized_results": {"approach": "vectorized", "execution_time": 226.573761, "signals": [{"time": "12:00", "datetime": "2025-06-20 12:00:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:18", "window_end": "12:00", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:48", "window_end": "12:00", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:01", "datetime": "2025-06-20 12:01:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:19", "window_end": "12:01", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:49", "window_end": "12:01", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:02", "datetime": "2025-06-20 12:02:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:20", "window_end": "12:02", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:50", "window_end": "12:02", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: PASS, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:03", "datetime": "2025-06-20 12:03:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:21", "window_end": "12:03", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:51", "window_end": "12:03", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:04", "datetime": "2025-06-20 12:04:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:22", "window_end": "12:04", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:52", "window_end": "12:04", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: PASS, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:05", "datetime": "2025-06-20 12:05:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:23", "window_end": "12:05", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:53", "window_end": "12:05", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: PASS, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:06", "datetime": "2025-06-20 12:06:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:24", "window_end": "12:06", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:54", "window_end": "12:06", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: PASS, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:07", "datetime": "2025-06-20 12:07:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:25", "window_end": "12:07", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:55", "window_end": "12:07", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:08", "datetime": "2025-06-20 12:08:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:26", "window_end": "12:08", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:56", "window_end": "12:08", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:09", "datetime": "2025-06-20 12:09:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:27", "window_end": "12:09", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:57", "window_end": "12:09", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: PASS, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:10", "datetime": "2025-06-20 12:10:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:28", "window_end": "12:10", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:58", "window_end": "12:10", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: PASS, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:11", "datetime": "2025-06-20 12:11:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:29", "window_end": "12:11", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:59", "window_end": "12:11", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: PASS, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:12", "datetime": "2025-06-20 12:12:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:30", "window_end": "12:12", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:00", "window_end": "12:12", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:13", "datetime": "2025-06-20 12:13:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:31", "window_end": "12:13", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:01", "window_end": "12:13", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:14", "datetime": "2025-06-20 12:14:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:32", "window_end": "12:14", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:02", "window_end": "12:14", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:15", "datetime": "2025-06-20 12:15:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:33", "window_end": "12:15", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:03", "window_end": "12:15", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:16", "datetime": "2025-06-20 12:16:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:34", "window_end": "12:16", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:04", "window_end": "12:16", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:17", "datetime": "2025-06-20 12:17:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:35", "window_end": "12:17", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:05", "window_end": "12:17", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:18", "datetime": "2025-06-20 12:18:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:36", "window_end": "12:18", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:06", "window_end": "12:18", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:19", "datetime": "2025-06-20 12:19:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:37", "window_end": "12:19", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:07", "window_end": "12:19", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:20", "datetime": "2025-06-20 12:20:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:38", "window_end": "12:20", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:08", "window_end": "12:20", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:21", "datetime": "2025-06-20 12:21:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:39", "window_end": "12:21", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:09", "window_end": "12:21", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:22", "datetime": "2025-06-20 12:22:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:40", "window_end": "12:22", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:10", "window_end": "12:22", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:23", "datetime": "2025-06-20 12:23:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:41", "window_end": "12:23", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:11", "window_end": "12:23", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:24", "datetime": "2025-06-20 12:24:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:42", "window_end": "12:24", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:12", "window_end": "12:24", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:25", "datetime": "2025-06-20 12:25:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:43", "window_end": "12:25", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:13", "window_end": "12:25", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: PASS, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:26", "datetime": "2025-06-20 12:26:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:44", "window_end": "12:26", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:14", "window_end": "12:26", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:27", "datetime": "2025-06-20 12:27:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:45", "window_end": "12:27", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:15", "window_end": "12:27", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:28", "datetime": "2025-06-20 12:28:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:46", "window_end": "12:28", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:16", "window_end": "12:28", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:29", "datetime": "2025-06-20 12:29:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:47", "window_end": "12:29", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:17", "window_end": "12:29", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:30", "datetime": "2025-06-20 12:30:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:48", "window_end": "12:30", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:18", "window_end": "12:30", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}], "position_events": [], "summary": {"total_minutes": 31, "signals_generated": 0, "positions_opened": 0, "api_calls_used": 1}}}