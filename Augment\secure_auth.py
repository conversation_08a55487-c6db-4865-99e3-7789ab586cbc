import yaml
import tkinter as tk
from tkinter import messagebox, simpledialog
import os
import sys

class SecureAuth:
    def __init__(self, cred_file='cred.yml'):
        self.cred_file = cred_file
        self.credentials = {}
        
    def load_credentials(self):
        """Load credentials from YAML file securely"""
        try:
            if not os.path.exists(self.cred_file):
                raise FileNotFoundError(f"Credentials file '{self.cred_file}' not found!")
            
            with open(self.cred_file, 'r') as file:
                self.credentials = yaml.safe_load(file)
            
            # Validate required fields
            required_fields = ['user', 'pwd', 'vc', 'apikey', 'imei']
            missing_fields = [field for field in required_fields if not self.credentials.get(field) or self.credentials[field] in ['', 'YOUR_USER_ID', 'YOUR_PASSWORD', 'YOUR_VENDOR_CODE', 'YOUR_API_KEY', 'YOUR_IMEI']]
            
            if missing_fields:
                raise ValueError(f"Please update the following fields in {self.cred_file}: {', '.join(missing_fields)}")
            
            return self.credentials
            
        except Exception as e:
            print(f"Error loading credentials: {e}")
            return None
    
    def get_otp_popup(self):
        """Show popup dialog to get OTP from user"""
        try:
            # Create root window and hide it
            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)  # Keep on top
            
            # Show OTP input dialog
            otp = simpledialog.askstring(
                "Two Factor Authentication",
                "Enter your OTP (6-digit code):",
                show='*'  # Hide characters for security
            )
            
            root.destroy()
            
            if otp is None:  # User cancelled
                print("OTP entry cancelled by user")
                return None
            
            if not otp or len(otp.strip()) == 0:
                messagebox.showerror("Error", "OTP cannot be empty!")
                return self.get_otp_popup()  # Retry
            
            return otp.strip()
            
        except Exception as e:
            print(f"Error getting OTP: {e}")
            return None
    
    def get_complete_credentials(self):
        """Get all credentials including OTP via popup"""
        # Load static credentials
        creds = self.load_credentials()
        if not creds:
            return None
        
        # Get OTP via popup
        print("Please enter your OTP in the popup window...")
        otp = self.get_otp_popup()
        if not otp:
            return None
        
        # Add OTP to credentials
        creds['factor2'] = otp
        
        print("Credentials loaded successfully!")
        return creds
    
    def validate_connection_test(self, api_instance):
        """Test if credentials work with a simple API call"""
        try:
            # Try to get limits (lightweight call since get_user_details doesn't exist)
            result = api_instance.get_limits()
            if result and result.get('stat') == 'Ok':
                print("Authentication successful!")
                return True
            else:
                print("Authentication failed - please check your credentials")
                return False
        except Exception as e:
            print(f"Connection test failed: {e}")
            return False

# Convenience function for easy import
def get_secure_credentials():
    """
    Easy-to-use function that returns complete credentials with OTP
    Usage: creds = get_secure_credentials()
    """
    auth = SecureAuth()
    return auth.get_complete_credentials()

# Example usage
if __name__ == "__main__":
    # Test the credential loading
    auth = SecureAuth()
    creds = auth.get_complete_credentials()
    if creds:
        print("Credentials loaded:", {k: '***' if k in ['pwd', 'factor2', 'apikey'] else v for k, v in creds.items()})
    else:
        print("Failed to load credentials") 