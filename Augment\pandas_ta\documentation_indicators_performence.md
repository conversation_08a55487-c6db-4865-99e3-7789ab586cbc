Title: Performance - Pandas TA

URL Source: https://www.pandas-ta.dev/api/performance/

Markdown Content:
* * *

drawdown[#](https://www.pandas-ta.dev/api/performance/#src.pandas_ta.performance.drawdown.drawdown "Permanent link")
--------------------------------------------------------------------------------------------------------------------

```
drawdown(
    close: Series, offset: Int = None, **kwargs: DictLike
) -> DataFrame
```

Drawdown

This indicator traces the peak-to-trough decline over a specific period. Commonly quoted as the percentage between the peak and the subsequent trough.

Sources
*   [investopedia](https://www.investopedia.com/terms/d/drawdown.asp)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series. | _required_ |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

* * *

log_return[#](https://www.pandas-ta.dev/api/performance/#src.pandas_ta.performance.log_return.log_return "Permanent link")
--------------------------------------------------------------------------------------------------------------------------

```
log_return(
    close: Series,
    length: Int = None,
    cumulative: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Log Return

Calculates the logarithmic return.

Sources
*   [stackoverflow](https://stackoverflow.com/questions/31287552/logarithmic-returns-in-pandas-dataframe)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `cumulative` | `bool` | If True, returns the cumulative returns. Default: `False` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

percent_return[#](https://www.pandas-ta.dev/api/performance/#src.pandas_ta.performance.percent_return.percent_return "Permanent link")
--------------------------------------------------------------------------------------------------------------------------------------

```
percent_return(
    close: Series,
    length: Int = None,
    cumulative: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Percent Return

Calculates the percent return.

Sources
*   [stackoverflow](https://stackoverflow.com/questions/31287552/logarithmic-returns-in-pandas-dataframe)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `1` | `None` |
| `cumulative` | `bool` | If True, returns the cumulative returns. Default: `False` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

