"""
Test Live Mode with Sliding Window

This script tests the enhanced live monitoring with sliding window approach.
"""

import sys
import os
from datetime import datetime, timedelta

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_live_monitoring_logic():
    """Test the live monitoring logic without actually running live"""
    print("🧪 Testing Live Monitoring Logic...")
    
    try:
        from smart_vectorized_backtester import get_current_time_info
        
        # Test current time functions
        current_date, current_time = get_current_time_info()
        print(f"✅ Current time: {current_date} {current_time}")
        
        # Test sliding window calculation
        current_datetime = datetime.strptime(f"{current_date} {current_time}", '%d-%m-%Y %H:%M')
        previous_minute = current_datetime - timedelta(minutes=1)
        window_start = previous_minute - timedelta(hours=3)
        
        print(f"✅ Previous minute: {previous_minute.strftime('%H:%M')}")
        print(f"✅ Window start: {window_start.strftime('%H:%M')}")
        print(f"✅ Window size: 3 hours")
        
        # Test minute tracking
        last_processed = previous_minute - timedelta(minutes=5)
        should_process = previous_minute > last_processed
        print(f"✅ Should process new minute: {should_process}")
        
        return True
        
    except Exception as e:
        print(f"❌ Live monitoring logic test failed: {str(e)}")
        return False

def test_sliding_window_backtester():
    """Test backtester with sliding window"""
    print("\n🧪 Testing Sliding Window Backtester...")
    
    try:
        from smart_vectorized_backtester import SmartVectorizedBacktester, get_token_info, get_current_time_info
        from shared_api_manager import get_api

        api = get_api()

        # Get current time for sliding window
        current_date, current_time = get_current_time_info()
        current_datetime = datetime.strptime(f"{current_date} {current_time}", '%d-%m-%Y %H:%M')
        previous_minute = current_datetime - timedelta(minutes=1)
        window_start = previous_minute - timedelta(hours=2)  # 2 hour window
        
        window_start_time = window_start.strftime('%H:%M')
        previous_time = previous_minute.strftime('%H:%M')
        
        print(f"📊 Testing sliding window: {window_start_time} to {previous_time}")
        
        # Test with NSE stock
        ticker = 'BATAINDIA'
        exchange = 'NSE'
        
        token_info = get_token_info(api, ticker, exchange)
        if not token_info:
            print(f"❌ Failed to get token info for {ticker}")
            return False
        
        print(f"✅ Token: {token_info['token']}")
        
        # Create backtester with sliding window
        backtester = SmartVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start=window_start_time,  # Sliding window start
            end=previous_time,        # Previous minute
            date=current_date,
            tokenid=token_info['token'],
            enable_momentum_validation=True,
            enable_realtime_detection=True
        )
        
        signals = backtester.run_smart_vectorized_backtest()
        
        # Filter for only the target minute
        target_signals = [s for s in signals if s['time'] == previous_time]
        
        print(f"✅ Sliding window test completed")
        print(f"📊 Total signals in window: {len(signals)}")
        print(f"📊 Signals for target minute ({previous_time}): {len(target_signals)}")
        
        if target_signals:
            for signal in target_signals:
                print(f"   🎯 {signal['time']}: {signal['signal_type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Sliding window backtester test failed: {str(e)}")
        return False

def main():
    """Run live mode tests"""
    print("🚀 Testing Enhanced Live Mode with Sliding Window...")
    print("="*70)
    
    results = []
    
    # Test live monitoring logic
    results.append(("Live Monitoring Logic", test_live_monitoring_logic()))
    
    # Test sliding window backtester
    results.append(("Sliding Window Backtester", test_sliding_window_backtester()))
    
    # Summary
    print("\n" + "="*70)
    print("📊 LIVE MODE TEST RESULTS")
    print("="*70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 Live mode enhancements working correctly!")
        print("\n💡 Key Improvements:")
        print("   ✅ Sliding window approach (only 3-hour data window)")
        print("   ✅ Minute-by-minute processing (no duplicate signals)")
        print("   ✅ User-selectable monitoring start time")
        print("   ✅ Efficient API usage (no full market recalculation)")
    else:
        print("⚠️ Some tests failed.")

if __name__ == "__main__":
    main()
