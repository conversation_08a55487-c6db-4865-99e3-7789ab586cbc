Title: Pandas TA Logo - Pandas TA

URL Source: https://www.pandas-ta.dev/

Published Time: Tue, 10 Jun 2025 22:38:27 GMT

Markdown Content:
![Image 1: Pandas TA Logo](https://www.pandas-ta.dev/assets/images/pta-logo.webp)[#](https://www.pandas-ta.dev/#_1 "Permanent link")
------------------------------------------------------------------------------------------------------------------------------------

A popular and comprehensive Technical Analysis Library in Python 3 that leverages [_numba_](http://numba.pydata.org/) and [_numpy_](https://numpy.org/) for accuracy and performance, and [_pandas_](https://pandas.pydata.org/) for simplicity and bulk processing. The library contains more than 150 indicators and utilities as well as 60 Candlestick Patterns when [TA Lib](https://ta-lib.org/) is installed.

![Image 2: Price Chart](https://www.pandas-ta.dev/assets/images/SPY_Chart.png)![Image 3: Volume Chart](https://www.pandas-ta.dev/assets/images/SPY_VOL.png)

Features[#](https://www.pandas-ta.dev/#features "Permanent link")
-----------------------------------------------------------------

*   **Easy to Use**

* * *

    *   Conventional calling similar to [TA Lib](https://ta-lib.org/).
    *   Simplified usage with the Pandas DataFrame Extension "ta".
    *   Data Source and post analytics agnostic.

*   **Large Library**

* * *

    *   Flat library structure.
    *   Has 150+ [indicators](https://www.pandas-ta.dev/api/) without **TA Lib**.
    *   [Candlestick](https://www.pandas-ta.dev/api/candle/) indicators _with_**TA Lib**.
    *   Custom Directory Support

*   **Accurate & Performant**

* * *

    *   _Numpy_ and _Numba_ benefits.
    *   Highly correlated with [TA Lib](https://ta-lib.org/) and sometimes [Trading View](https://www.tradingview.com/).



