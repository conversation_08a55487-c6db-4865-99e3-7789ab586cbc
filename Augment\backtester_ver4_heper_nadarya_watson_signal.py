#<PERSON><PERSON><PERSON> (check_vander) signal function to see if there is signal in last 3 minutes of selected script and time for MCX and NFO

import pandas as pd
import datetime
import os
import time
import matplotlib.pyplot as plt
import sys
import subprocess
def live_data(data):
    # pairs = "ltcusdt"  # self.pairs
    t_frame = "1min"  # self.time_frame
    import pandas as pd
    from datetime import datetime

    response = data

    time = []
    open_ = []
    high_ = []
    low_ = []
    close_ = []
    volume = []
    # print(data)
    for candle in response:
        time.append(datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S'))
        open_.append(float(candle['into']))
        high_.append(float(candle['inth']))
        low_.append(float(candle['intl']))
        close_.append(float(candle['intc']))
        volume.append(float(candle['intv']))

    candles = pd.DataFrame({
        "Open": open_,
        "High": high_,
        "Low": low_,
        "Close": close_,
        "volume": volume,
        "time": time
    })

    candles = candles.set_index("time")
    return candles

import datetime
def get_start_end_timestamps(date_input, starttime_input, endtime_input):
    # Parse the input date
    date_parts = date_input.split('-')
    year, month, day = int(date_parts[2]), int(date_parts[1]), int(date_parts[0])

    # Parse the input times
    start_time = datetime.datetime.strptime(starttime_input, '%H:%M').time()
    end_time = datetime.datetime.strptime(endtime_input, '%H:%M').time()

    # Combine the input date with the input times
    start_datetime = datetime.datetime(year, month, day, start_time.hour, start_time.minute)
    end_datetime = datetime.datetime(year, month, day, end_time.hour, end_time.minute)

    # # Adjust start_datetime and end_datetime if they are in the past
    # current_datetime = datetime.datetime.now()
    # if start_datetime < current_datetime:
    #     start_datetime += datetime.timedelta(days=0)
    # if end_datetime < current_datetime:
    #     end_datetime += datetime.timedelta(days=0)

    # print(start_datetime)
    # print(end_datetime)

    start_timestamp = start_datetime.timestamp()
    end_timestamp = end_datetime.timestamp()

    return start_timestamp, end_timestamp


def check_vander(tokenid, exchange, current=False, date_input =None, starttime_input=None, endtime_input=None):
        # print('inside check vander')
        isvander = False
        

        
        if date_input is None:
        # Get the current date and time
            now = datetime.datetime.now()
            
            # Extract the date, start time, and end time from the current datetime
            date_input = now.date().strftime('%d-%m-%Y')
            endtime_input = now.time().strftime('%H:%M')
            starttime_input = (now - datetime.timedelta(hours=1)).time().strftime('%H:%M')
        else:
            date_input = date_input
            endtime_input = endtime_input
            starttime_input = starttime_input
      
        
        start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
   
        max_retries = 3
        retries = 0
        while retries < max_retries:
            try:
                # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
                if exchange == 'NFO' or exchange == 'NSE':
                    # print('inside vander2 fetch data')
                    #ver6
                    data = api.get_time_price_series(exchange='NSE', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    # print(data)
                elif exchange == 'MCX' : 
                    # print(exchange)
                    data = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    # print(data)
                break  # If the API call is successful, exit the loop
            except Exception as e:
                # print(f"Error: {e}")
                retries += 1
                if retries == max_retries:
                    x=1
                    # print("Maximum number of retries reached. Unable to fetch data.")
                else:
                    # print(f"Retrying in 1 seconds... (Attempt {retries}/{max_retries})")
                    time.sleep(1)
        
        if retries < max_retries:
            new0=0
     
        data = live_data(data)
        data = data.sort_values(by='time')
        data1=data
        # print(data)
        
        data = data['Close'].values
        
        import math
        h      = 8
        mult   = 3
        src    = data
        k = 1.75
        y = []
        #..............#
        up = []
        dn = []
        up_signal = []
        dn_signal = []
        up_temp = 0
        dn_temp = 0
        #.................#
        upper_band = []
        lower_band = []
        upper_band_signal = []
        lower_band_signal = []
        #....................#
        sum_e = 0
        for i in range(len(data)):
            sum = 0
            sumw = 0   
            for j in range(len(data)):
                w = math.exp(-(math.pow(i-j,2)/(h*h*2)))
                sum += src[j]*w
                sumw += w
            y2 = sum/sumw
            sum_e += abs(src[i] - y2)
            y.insert(i,y2)
        # mae = sum_e/len(data)*mult
        mae = sum_e/len(data)*k
        #print(mae)
        import numpy as np
        for i  in range(len(data)):
                y2 = y[i]
                y1 = y[i-1]
                
                if y[i]>y[i-1]:
                    up.insert(i,y[i])
                    if up_temp == 0:
                        up_signal.insert(i,data[i])
                    else:
                        up_signal.insert(i,np.nan)
                    up_temp = 1
                else:
                    up_temp = 0
                    up.insert(i,np.nan)
                    up_signal.insert(i,np.nan)
                    
                if y[i]<y[i-1]:
                    dn.insert(i,y[i])
                    if dn_temp == 0:
                        dn_signal.insert(i,data[i])
                    else:
                        dn_signal.insert(i,np.nan)
                    dn_temp = 1
                else:
                    dn_temp = 0
                    dn.insert(i,np.nan)
                    dn_signal.insert(i,np.nan)
                    
                    
                # upper_band.insert(i,y[i]+mae)
                # lower_band.insert(i,y[i]-mae)
                upper_band.insert(i, y[i] + mae * k)  # Modify the upper band calculation
                lower_band.insert(i, y[i] - mae * k)  # Modify the lower band calculation
                if data[i]> upper_band[i]:
                    upper_band_signal.insert(i,data[i])
                else:
                    upper_band_signal.insert(i,np.nan)
                    
                if data[i]<lower_band[i]:
                    lower_band_signal.insert(i,data[i])
                else:
                    lower_band_signal.insert(i,np.nan)
        import pandas as pd
        Nadaraya_Watson = pd.DataFrame({
                    "Buy": up,
                    "Sell": dn,
                    "BUY_Signal": up_signal,
                    "Sell_Signal": dn_signal,
                    "Uppar_Band": upper_band,
                    "Lower_Band":lower_band,
                    "Upper_Band_signal":upper_band_signal,
                    "Lower_Band_Signal":lower_band_signal
                })
        # print(Nadaraya_Watson)
        
        # %matplotlib inline   
        # print('plotting:')
        import matplotlib.pyplot as plt
        plt.figure(figsize=(18,8))
        # fig1, plt2 = plt.subplots(figsize=(15, 8))
        # plt.figure(figsize=(10, 6), dpi=100)
        plt.plot(np.array(upper_band), color= 'green', linestyle='--', linewidth=2) 
        plt.plot(np.array(lower_band), color= 'red', linestyle='--', linewidth=2) 
        
    
        
        plt.plot(np.array(dn), color= 'red', label= 'Polynomial model') 
        plt.plot(np.array(up), color= 'green', label= 'Polynomial model')
        plt.plot(np.array(upper_band_signal), color= 'red', marker='v', linestyle='dashed', linewidth=2, markersize=15) 
        plt.plot(np.array(lower_band_signal), color= 'green', marker='^', linestyle='dashed', linewidth=2, markersize=15) 
        isvander = False
        
        plt.plot(np.array(data[:]), color= 'blue', label= 'Data')
    
        # Extract time from the index and display every 5th time value
        times = [time.strftime('%H:%M:%S') for time in data1.index[::5]]
        plt.xticks(np.arange(len(data1))[::5], times, rotation=45)
        plt.xlabel('Time')
        if exchange == 'NFO' or exchange == 'NSE':
            ret3 = api.get_quotes(exchange='NSE', token=tokenid)
            name1 = ret3['cname']
            # print(f'eq name:{name1}')
        elif exchange == 'MCX':   
            ret3 = api.get_quotes(exchange='MCX', token=tokenid)
            name1 = ret3['symname']
            # print(ret3)

        minutes_check = -3
    
        # plt.text(f'Overall Trend: {name1}', ha='left', va='bottom', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
        # plt.text(0.95, 0.05, f'Overall Trend: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
        # Check which signal (upper band or lower band) was present in the last 5 minutes
        upper_band_present_last_5 = any(x is not np.nan for x in upper_band_signal[minutes_check:])
        lower_band_present_last_5 = any(x is not np.nan for x in lower_band_signal[minutes_check:])
        
        if upper_band_present_last_5 and lower_band_present_last_5:
            signal_text = f'Last {minutes_check} mins: Both signals present'
            plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq name:{name1}')
            # plt.show()
            # time.sleep(2)
        elif upper_band_present_last_5:
            signal_text = f'Last {minutes_check} mins: Upper band signal present'
            plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq name:{name1}')
            # plt.show()
            # time.sleep(2)
        elif lower_band_present_last_5:
            signal_text = f'Last {minutes_check} mins: Lower band signal present'
            plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq name:{name1}')
            # plt.show()
            # time.sleep(2)
        else:
            if not any(x is not np.nan for x in upper_band_signal) and not any(x is not np.nan for x in lower_band_signal):
                signal_text = f'No signal present at all in last {minutes_check} minutes'
                plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
                plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
                isvander = False
            else:
                signal_text = f'Last {minutes_check} mins: No signal present'
                plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
                plt.text(0.35, 0.35, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
                
                isvander = False
        # Add text at the bottom right of the plot
        # plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
        # plt.text(0.95, 0.05, f'Overall Trend: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))

        # plt.show()
        # time.sleep(2)
        # plt.clf()
        # plt.close('all')
        # plt.close()
        return isvander, signal_text
        

    # except Exception as e:
    #     print(f"Error occurred while fetching data: {e}")

    # # Wait for the next minute
    # time.sleep(1)
    # # time.sleep(61 - (datetime.datetime.now().second % 60))

def check_vander1(tokenid, exchange,  current=False, date_input =None, starttime_input=None, endtime_input=None):
        # print('inside check vander1')
        isvander = False
        # plt.close('all')
         # Clear the output
        # sys.stdout.flush()
        # print("\033[H\033[J", end="")
        # # subprocess.run(['cls'])
        # os.system('cls' if os.name == 'nt' else 'clear')
        

        if date_input is None:
        # Get the current date and time
            now = datetime.datetime.now()
            
            # Extract the date, start time, and end time from the current datetime
            date_input = now.date().strftime('%d-%m-%Y')
            endtime_input = now.time().strftime('%H:%M')
            starttime_input = (now - datetime.timedelta(hours=1)).time().strftime('%H:%M')
        else:
            date_input = date_input
            endtime_input = endtime_input
            starttime_input = starttime_input
        # date_input = '27-05-2024'
        # starttime_input = '14:20'
        # endtime_input = '15:29'
            
        # print("date_input:", date_input)
        # print("starttime_input:", starttime_input)
        # print("endtime_input:", endtime_input)
        
        
        
        
        
        
        
        
        start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
        # print(f'Start timestamp: {start_timestamp}')
        # print(f'End timestamp: {end_timestamp}')
        # print()
        # Retry the API call up to 3 times if there is an error
        max_retries = 3
        retries = 0
        while retries < max_retries:
            try:
                # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
                if exchange == 'NFO' or exchange == 'NSE':
                    # print('inside vander1 fetch data')
                    data = api.get_time_price_series(exchange='NSE', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    # print(data)
                elif exchange == 'MCX' : 
                    data = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                break  # If the API call is successful, exit the loop
            except Exception as e:
                # print(f"Error: {e}")
                retries += 1
                if retries == max_retries:
                    x=1
                    # print("Maximum number of retries reached. Unable to fetch data.")
                else:
                    # print(f"Retrying in 2 seconds... (Attempt {retries}/{max_retries})")
                    time.sleep(1)
        
        if retries < max_retries:
            new0=0
      
        data = live_data(data)
        data = data.sort_values(by='time')
        data1=data
        # print(data)
        
        data = data['Close'].values
        
        import math
        h      = 8
        mult   = 3
        src    = data
        k = 1.5
        y = []
        #..............#
        up = []
        dn = []
        up_signal = []
        dn_signal = []
        up_temp = 0
        dn_temp = 0
        #.................#
        upper_band = []
        lower_band = []
        upper_band_signal = []
        lower_band_signal = []
        #....................#
        sum_e = 0
        for i in range(len(data)):
            sum = 0
            sumw = 0   
            for j in range(len(data)):
                w = math.exp(-(math.pow(i-j,2)/(h*h*2)))
                sum += src[j]*w
                sumw += w
            y2 = sum/sumw
            sum_e += abs(src[i] - y2)
            y.insert(i,y2)
        # mae = sum_e/len(data)*mult
        mae = sum_e/len(data)*k
        #print(mae)
        import numpy as np
        for i  in range(len(data)):
                y2 = y[i]
                y1 = y[i-1]
                
                if y[i]>y[i-1]:
                    up.insert(i,y[i])
                    if up_temp == 0:
                        up_signal.insert(i,data[i])
                    else:
                        up_signal.insert(i,np.nan)
                    up_temp = 1
                else:
                    up_temp = 0
                    up.insert(i,np.nan)
                    up_signal.insert(i,np.nan)
                    
                if y[i]<y[i-1]:
                    dn.insert(i,y[i])
                    if dn_temp == 0:
                        dn_signal.insert(i,data[i])
                    else:
                        dn_signal.insert(i,np.nan)
                    dn_temp = 1
                else:
                    dn_temp = 0
                    dn.insert(i,np.nan)
                    dn_signal.insert(i,np.nan)
                    
                    
                # upper_band.insert(i,y[i]+mae)
                # lower_band.insert(i,y[i]-mae)
                upper_band.insert(i, y[i] + mae * k)  # Modify the upper band calculation
                lower_band.insert(i, y[i] - mae * k)  # Modify the lower band calculation
                if data[i]> upper_band[i]:
                    upper_band_signal.insert(i,data[i])
                else:
                    upper_band_signal.insert(i,np.nan)
                    
                if data[i]<lower_band[i]:
                    lower_band_signal.insert(i,data[i])
                else:
                    lower_band_signal.insert(i,np.nan)
        import pandas as pd
        Nadaraya_Watson = pd.DataFrame({
                    "Buy": up,
                    "Sell": dn,
                    "BUY_Signal": up_signal,
                    "Sell_Signal": dn_signal,
                    "Uppar_Band": upper_band,
                    "Lower_Band":lower_band,
                    "Upper_Band_signal":upper_band_signal,
                    "Lower_Band_Signal":lower_band_signal
                })
       isvander = False
        
        # plt.plot(np.array(data[:]), color= 'blue', label= 'Data')
        
        # Set the x-axis ticks and labels using the time column from the data DataFrame
        # plt.xticks(np.arange(len(data1)), data1.index, rotation=45)
        # plt.xlabel('Time')
        
        # Extract time from the index and display every 5th time value
        times = [time.strftime('%H:%M:%S') for time in data1.index[::5]]
        # plt.xticks(np.arange(len(data1))[::5], times, rotation=45)
        # plt.xlabel('Time')
        if exchange == 'NFO' or exchange == 'NSE':
            ret3 = api.get_quotes(exchange='NSE', token=tokenid)
            name1 = ret3['cname']
        elif exchange == 'MCX':   
            ret3 = api.get_quotes(exchange='MCX', token=tokenid)
            name1 = ret3['symname']
            # print(ret3)
        
    
        # plt.text(f'Overall Trend: {name1}', ha='left', va='bottom', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
        # plt.text(0.95, 0.05, f'Overall Trend: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
        # Check which signal (upper band or lower band) was present in the last 5 minutes
        upper_band_present_last_5 = any(x is not np.nan for x in upper_band_signal[-3:])
        lower_band_present_last_5 = any(x is not np.nan for x in lower_band_signal[-3:])
        
        if upper_band_present_last_5 and lower_band_present_last_5:
            signal_text = 'Last 5 mins: Both signals present'
            # plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq_name:{name1}')
            # plt.show()
            # time.sleep(2)
        elif upper_band_present_last_5:
            signal_text = 'Last 5 mins: Upper band signal present'
            # print(f'eq_name:{name1}')
            isvander = True
            # plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            # isvander = True
            # plt.show()
            # time.sleep(2)
        elif lower_band_present_last_5:
            signal_text = 'Last 5 mins: Lower band signal present'
            # plt.text(0.95, 0.3, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
            # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
            isvander = True
            # print(f'eq_name:{name1}')
            # plt.show()
            # time.sleep(2)
        else:
            if not any(x is not np.nan for x in upper_band_signal) and not any(x is not np.nan for x in lower_band_signal):
                signal_text = 'No signal present at all'
                # plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
                # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
                isvander = False
            else:
                signal_text = 'Last 5 mins: No signal present'
                # plt.text(0.8, 0.5, signal_text, ha='right', va='top', transform=plt.gca().transAxes, fontsize=12)
                # plt.text(0.95, 0.05, f'stock: {name1}', ha='right', va='bottom', transform=plt.gca().transAxes, fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
                
                isvander = False
       
        return isvander, signal_text
        