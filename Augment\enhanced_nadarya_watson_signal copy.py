"""
Enhanced <PERSON><PERSON><PERSON> Watson Signal Detection with Momentum Validation

This enhanced version adds momentum validation to prevent false positive signals.
It ensures that band signals are only generated when there's strong momentum
confirmation in the last 2 minutes.

Key Enhancement:
- After detecting band touch, validates momentum strength
- For upper band: requires at least 1 candle closing higher than previous in last 2 minutes
- For lower band: requires at least 1 candle closing lower than previous in last 2 minutes
- Prevents false positives when price touches band but lacks momentum
"""

import pandas as pd
import datetime
import os
import time
import matplotlib.pyplot as plt
import sys
import numpy as np
import logging

# Import shared API manager
from shared_api_manager import get_api

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def live_data(data):
    """Ver4 exact logic for live data processing"""
    import pandas as pd
    from datetime import datetime

    response = data
    time = []
    open_ = []
    high_ = []
    low_ = []
    close_ = []
    volume = []
    
    for candle in response:
        time.append(datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S'))
        open_.append(float(candle['into']))
        high_.append(float(candle['inth']))
        low_.append(float(candle['intl']))
        close_.append(float(candle['intc']))
        volume.append(float(candle['intv']))

    candles = pd.DataFrame({
        "Open": open_,
        "High": high_,
        "Low": low_,
        "Close": close_,
        "volume": volume,
        "time": time
    })

    candles = candles.set_index("time")
    return candles

def get_start_end_timestamps(date_input, starttime_input, endtime_input):
    """Ver4 exact logic for timestamp generation"""
    date_parts = date_input.split('-')
    year, month, day = int(date_parts[2]), int(date_parts[1]), int(date_parts[0])

    start_time = datetime.datetime.strptime(starttime_input, '%H:%M').time()
    end_time = datetime.datetime.strptime(endtime_input, '%H:%M').time()

    start_datetime = datetime.datetime(year, month, day, start_time.hour, start_time.minute)
    end_datetime = datetime.datetime(year, month, day, end_time.hour, end_time.minute)

    start_timestamp = start_datetime.timestamp()
    end_timestamp = end_datetime.timestamp()

    return start_timestamp, end_timestamp

def validate_momentum_strength(close_prices, band_signal_type, momentum_window=2):
    """
    Validate momentum strength in the last N minutes
    
    Args:
        close_prices: Array of closing prices (most recent last)
        band_signal_type: 'upper' or 'lower'
        momentum_window: Number of minutes to check for momentum (default: 2)
    
    Returns:
        Tuple[bool, str]: (has_strong_momentum, momentum_description)
    """
    try:
        if len(close_prices) < momentum_window + 1:
            return False, f"Insufficient data for momentum validation (need {momentum_window + 1} candles)"
        
        # Get the last N+1 candles to check N momentum moves
        recent_closes = close_prices[-(momentum_window + 1):]
        
        strong_moves = 0
        momentum_details = []
        
        for i in range(1, len(recent_closes)):
            current_close = recent_closes[i]
            previous_close = recent_closes[i-1]
            
            if band_signal_type == 'upper':
                # For upper band signals, we want upward momentum
                if current_close > previous_close:
                    strong_moves += 1
                    momentum_details.append(f"Candle {i}: {current_close:.2f} > {previous_close:.2f} (Strong UP)")
                else:
                    momentum_details.append(f"Candle {i}: {current_close:.2f} <= {previous_close:.2f} (Weak)")
            
            elif band_signal_type == 'lower':
                # For lower band signals, we want downward momentum
                if current_close < previous_close:
                    strong_moves += 1
                    momentum_details.append(f"Candle {i}: {current_close:.2f} < {previous_close:.2f} (Strong DOWN)")
                else:
                    momentum_details.append(f"Candle {i}: {current_close:.2f} >= {previous_close:.2f} (Weak)")
        
        # Require at least 2 strong moves in the momentum window for truly strong signals
        # This prevents false positives when price touches band but lacks sustained momentum
        has_strong_momentum = strong_moves >= 2
        
        momentum_description = f"Momentum check ({momentum_window} candles): {strong_moves}/{momentum_window} strong moves (need ≥2 for signal). " + "; ".join(momentum_details)
        
        return has_strong_momentum, momentum_description
        
    except Exception as e:
        logger.error(f"❌ Error in momentum validation: {str(e)}")
        return False, f"Error in momentum validation: {str(e)}"

def check_vander_enhanced(tokenid, exchange, current=False, date_input=None, starttime_input=None, endtime_input=None, enable_momentum_validation=True, enable_realtime_detection=True):
    """
    Enhanced Nadarya Watson signal detection with momentum validation and real-time detection

    Args:
        tokenid: Token ID for the instrument
        exchange: Exchange (NSE, NFO, MCX)
        current: Whether to use current time
        date_input: Date in DD-MM-YYYY format
        starttime_input: Start time in HH:MM format
        endtime_input: End time in HH:MM format
        enable_momentum_validation: Whether to enable momentum validation (default: True)
        enable_realtime_detection: Whether to check current minute for band touch (default: True)

    Returns:
        Tuple[bool, str]: (is_vander_signal, signal_text)
    """
    try:
        logger.debug(f'Checking Enhanced Nadarya Watson signal for {exchange}:{tokenid}')
        
        # Get shared API instance
        api = get_api()
        
        isvander = False
        
        # Ver4 exact time logic
        if date_input is None:
            now = datetime.datetime.now()
            date_input = now.date().strftime('%d-%m-%Y')
            endtime_input = now.time().strftime('%H:%M')
            starttime_input = (now - datetime.timedelta(hours=1)).time().strftime('%H:%M')
        else:
            date_input = date_input
            endtime_input = endtime_input
            starttime_input = starttime_input
        
        start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
        
        # Ver4 exact retry logic
        max_retries = 3
        retries = 0
        data = None
        
        while retries < max_retries:
            try:
                if exchange == 'NFO' or exchange == 'NSE':
                    data = api.get_time_price_series(
                        exchange='NSE', 
                        token=tokenid, 
                        starttime=start_timestamp, 
                        endtime=end_timestamp, 
                        interval=1
                    )
                elif exchange == 'MCX':
                    data = api.get_time_price_series(
                        exchange='MCX', 
                        token=tokenid, 
                        starttime=start_timestamp, 
                        endtime=end_timestamp, 
                        interval=1
                    )
                break  # Success, exit retry loop
                
            except Exception as e:
                retries += 1
                if retries == max_retries:
                    logger.error(f"❌ Maximum retries reached for data fetch: {str(e)}")
                    return False, "Error fetching data"
                else:
                    logger.warning(f"⚠️ Retry {retries}/{max_retries}: {str(e)}")
                    time.sleep(1)
        
        if not data:
            logger.warning("No data received from API")
            return False, "No data available"
        
        # Ver4 exact data processing
        data_df = live_data(data)
        data_df = data_df.sort_values(by='time')
        
        close_prices = data_df['Close'].values
        
        # Ver4 exact Nadarya Watson calculation
        import math
        h = 8
        mult = 3
        src = close_prices
        k = 1.75
        y = []
        
        # Ver4 exact algorithm implementation
        up = []
        dn = []
        up_signal = []
        dn_signal = []
        up_temp = 0
        dn_temp = 0
        
        upper_band = []
        lower_band = []
        upper_band_signal = []
        lower_band_signal = []
        
        sum_e = 0
        for i in range(len(close_prices)):
            sum = 0
            sumw = 0   
            for j in range(len(close_prices)):
                w = math.exp(-(math.pow(i-j,2)/(h*h*2)))
                sum += src[j]*w
                sumw += w
            y2 = sum/sumw
            sum_e += abs(src[i] - y2)
            y.insert(i,y2)
        
        mae = sum_e/len(close_prices)*k
        
        for i in range(len(close_prices)):
            y2 = y[i]
            y1 = y[i-1] if i > 0 else y[i]
            
            if y[i] > y[i-1] if i > 0 else False:
                up.insert(i, y[i])
                if up_temp == 0:
                    up_signal.insert(i, close_prices[i])
                else:
                    up_signal.insert(i, np.nan)
                up_temp = 1
            else:
                up_temp = 0
                up.insert(i, np.nan)
                up_signal.insert(i, np.nan)
                
            if y[i] < y[i-1] if i > 0 else False:
                dn.insert(i, y[i])
                if dn_temp == 0:
                    dn_signal.insert(i, close_prices[i])
                else:
                    dn_signal.insert(i, np.nan)
                dn_temp = 1
            else:
                dn_temp = 0
                dn.insert(i, np.nan)
                dn_signal.insert(i, np.nan)
            
            # Ver4 exact band calculations
            upper_band.insert(i, y[i] + mae * k)
            lower_band.insert(i, y[i] - mae * k)
            
            if close_prices[i] > upper_band[i]:
                upper_band_signal.insert(i, close_prices[i])
            else:
                upper_band_signal.insert(i, np.nan)
                
            if close_prices[i] < lower_band[i]:
                lower_band_signal.insert(i, close_prices[i])
            else:
                lower_band_signal.insert(i, np.nan)
        
        # Ver4 exact signal detection logic
        if exchange == 'NFO' or exchange == 'NSE':
            ret3 = api.get_quotes(exchange='NSE', token=tokenid)
            name1 = ret3['cname']
        elif exchange == 'MCX':   
            ret3 = api.get_quotes(exchange='MCX', token=tokenid)
            name1 = ret3['symname']

        # Enhanced signal detection logic
        if enable_realtime_detection:
            # Check current minute (last candle) for band touch - fixes 1-minute lag
            current_upper_band = not np.isnan(upper_band_signal[-1]) if len(upper_band_signal) > 0 else False
            current_lower_band = not np.isnan(lower_band_signal[-1]) if len(lower_band_signal) > 0 else False

            # Also check last 2 minutes for additional confirmation
            minutes_check = -2  # Check last 2 minutes + current minute
            upper_band_present_recent = any(not np.isnan(x) for x in upper_band_signal[minutes_check:]) if len(upper_band_signal) >= abs(minutes_check) else current_upper_band
            lower_band_present_recent = any(not np.isnan(x) for x in lower_band_signal[minutes_check:]) if len(lower_band_signal) >= abs(minutes_check) else current_lower_band

            # Final signal: current minute OR recent minutes
            upper_band_present_last_3 = current_upper_band or upper_band_present_recent
            lower_band_present_last_3 = current_lower_band or lower_band_present_recent

            signal_timing = "current minute" if (current_upper_band or current_lower_band) else "last 2 minutes"
        else:
            # Original Ver4 logic - check last 3 minutes (with 1-minute lag)
            minutes_check = -3  # Ver4 exact check period
            upper_band_present_last_3 = any(not np.isnan(x) for x in upper_band_signal[minutes_check:]) if len(upper_band_signal) >= abs(minutes_check) else False
            lower_band_present_last_3 = any(not np.isnan(x) for x in lower_band_signal[minutes_check:]) if len(lower_band_signal) >= abs(minutes_check) else False
            signal_timing = "last 3 minutes"
        
        # Enhanced momentum validation
        momentum_validated = True
        momentum_text = ""
        
        if enable_momentum_validation and (upper_band_present_last_3 or lower_band_present_last_3):
            if upper_band_present_last_3 and lower_band_present_last_3:
                # Both signals present - validate both
                upper_momentum, upper_momentum_text = validate_momentum_strength(close_prices, 'upper')
                lower_momentum, lower_momentum_text = validate_momentum_strength(close_prices, 'lower')
                momentum_validated = upper_momentum or lower_momentum
                momentum_text = f"Upper: {upper_momentum_text}; Lower: {lower_momentum_text}"
            elif upper_band_present_last_3:
                # Only upper band signal
                momentum_validated, momentum_text = validate_momentum_strength(close_prices, 'upper')
            elif lower_band_present_last_3:
                # Only lower band signal
                momentum_validated, momentum_text = validate_momentum_strength(close_prices, 'lower')
        
        # Final signal determination
        if upper_band_present_last_3 and lower_band_present_last_3:
            if momentum_validated or not enable_momentum_validation:
                signal_text = f'{signal_timing}: Both signals present'
                if enable_momentum_validation:
                    signal_text += f' with momentum validation: {momentum_text}'
                isvander = True
            else:
                signal_text = f'{signal_timing}: Both signals present but FAILED momentum validation: {momentum_text}'
                isvander = False
        elif upper_band_present_last_3:
            if momentum_validated or not enable_momentum_validation:
                signal_text = f'{signal_timing}: Upper band signal present'
                if enable_momentum_validation:
                    signal_text += f' with momentum validation: {momentum_text}'
                isvander = True
            else:
                signal_text = f'{signal_timing}: Upper band signal present but FAILED momentum validation: {momentum_text}'
                isvander = False
        elif lower_band_present_last_3:
            if momentum_validated or not enable_momentum_validation:
                signal_text = f'{signal_timing}: Lower band signal present'
                if enable_momentum_validation:
                    signal_text += f' with momentum validation: {momentum_text}'
                isvander = True
            else:
                signal_text = f'{signal_timing}: Lower band signal present but FAILED momentum validation: {momentum_text}'
                isvander = False
        else:
            if not any(not np.isnan(x) for x in upper_band_signal) and not any(not np.isnan(x) for x in lower_band_signal):
                signal_text = f'No signal present at all in {signal_timing}'
                isvander = False
            else:
                signal_text = f'{signal_timing}: No signal present'
                isvander = False
        
        logger.debug(f'Enhanced Nadarya Watson result: {isvander}, {signal_text}')
        return isvander, signal_text
        
    except Exception as e:
        logger.error(f"❌ Error in check_vander_enhanced: {str(e)}")
        return False, f"Error: {str(e)}"

# Backward compatibility - use enhanced version by default
def check_vander(tokenid, exchange, current=False, date_input=None, starttime_input=None, endtime_input=None):
    """
    Backward compatible wrapper that uses enhanced version with momentum validation and real-time detection
    """
    return check_vander_enhanced(tokenid, exchange, current, date_input, starttime_input, endtime_input,
                                enable_momentum_validation=True, enable_realtime_detection=True)

def check_vander_original(tokenid, exchange, current=False, date_input=None, starttime_input=None, endtime_input=None):
    """
    Original version without momentum validation and with 1-minute lag (for comparison)
    """
    return check_vander_enhanced(tokenid, exchange, current, date_input, starttime_input, endtime_input,
                                enable_momentum_validation=False, enable_realtime_detection=False)
