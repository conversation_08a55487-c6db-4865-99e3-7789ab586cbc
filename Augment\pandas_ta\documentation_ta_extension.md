Title: TA DataFrame Extension - Pandas TA

URL Source: https://www.pandas-ta.dev/api/ta-extension/

Markdown Content:
Review the [Pandas](https://pandas.pydata.org/) documentation, if **not** proficient, and more specifically the following concepts of _Series_ and _DataFrames_ and _DataFrame Extensions_.

Pandas DataFrame Extension: "ta"

The "ta" extension simplifies the processing of concatenating Technical Analysis indicators onto the existing Pandas DataFrame. To do so, this extension assumes that the DataFrame includes a DateTime oriented index and columns named: "open", "high", "low", "close", "volume".

Returns:

| Type | Description |
| --- | --- |
| `Series | DataFrame | None` | See Notes |

Features
*   Attributes and methods for _ohlcv_ data.
*   Indicator Wrappers for each. Simplifies `sma = ta.sma(df["Close"]); df["sma"] = sma` to `df.ta.sma(append=True)`
*   A `study` method, that simplifies processing multiple indicators with or without multiprocessing. See: `help(ta.study)`

See Also
*   Pandas TA [DataFrame Extension](http://127.0.0.1:8000/docs/api/dataframe/) Documention
*   [Pandas DataFrame Accessor](https://pandas.pydata.org/docs/reference/api/pandas.api.extensions.register_dataframe_accessor.html#pandas.api.extensions.register_dataframe_accessor)

Notes
Most Indicators will return a Pandas Series. Others like MACD, BBANDS, KC, et al will return a Pandas DataFrame. Ichimoku on the other hand will return two DataFrames, the Ichimoku DataFrame for the known period and a Span DataFrame for the future of the Span values.

Documentation is formatted for [mkdocs](https://www.mkdocs.org/) and [mkdocs-docstrings](https://mkdocstrings.github.io/).

Tip
Remember to adjust the `cores` for maximum speed!

adjusted`property``writable`[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.adjusted "Permanent link")
--------------------------------------------------------------------------------------------------------------------------------------------

```
adjusted: str
```

Get/Set the **adjusted_close** column.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `name` | `str` | The column `name`, to set as the _adjusted_ column. Default: `None` | _required_ |

Returns:

| Type | Description |
| --- | --- |
| `str` | The _adjusted_ column. |

cores`property``writable`[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.cores "Permanent link")
--------------------------------------------------------------------------------------------------------------------------------------

```
cores: Int
```

Manage the number of `cpus` to utilize for multiprocessing.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `cpus` | `Int` | Number of `cores` Default: `cpu_count()` | _required_ |

Returns:

| Type | Description |
| --- | --- |
| `Int` | The number of `cores` set for multiprocessing |

Disable multiprocessing

Set: `df.ta.cores = 0`

Multiprocessing caveats

*   Multiprocessing **will not** work when using the following keys: `"col_names"`.
*   Multiprocessing **may not** work or complete as expected when indicator _chaining_.
    *   Chained indicators use non-default, _ohlcv_, sources as input Series.

exchange`property``writable`[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.exchange "Permanent link")
--------------------------------------------------------------------------------------------------------------------------------------------

```
exchange: str
```

A label property of the major _exchange_ the _ohlcv_ data is associated with.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `value` | `str` | The `exchange`. Must be one of the following `ta.EXCHANGE_TZ.keys()`. Default: `"NYSE"` | _required_ |

Returns:

| Type | Description |
| --- | --- |
| `str` | Exchange label |

time_range`property``writable`[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.time_range "Permanent link")
------------------------------------------------------------------------------------------------------------------------------------------------

```
time_range: IntFloat
```

Useful for annualization.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `value` | `str` | `"months", "weeks", "days", "hours", "minutes", "seconds"`. Default: `"years"` | _required_ |

Returns:

| Type | Description |
| --- | --- |
| `float` | Time elapsed between the first and last DateTimeIndex |

baseline[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.baseline "Permanent link")
------------------------------------------------------------------------------------------------------------------------

```
baseline(
    zero: bool = False,
    index: int = 0,
    k: IntFloat = 1,
    to_log: bool = False,
    save: bool = False,
) -> DataFrame
```

baseline

This method updates the DataFrame _ohlc_ values with a baseline of `k=1`. Useful for comparisons.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `zero` | `bool` | Zero the _ohlc_ data. | `False` |
| `index` | `bool` | Index to baseline at. | `0` |
| `k` | `IntFloat` | Scaler. | `1` |
| `to_log` | `bool` | Pre apply `np.log`. | `False` |
| `save` | `bool` | Preserve _ohlc_ when using `to_log`. | `False` |

categories[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.categories "Permanent link")
----------------------------------------------------------------------------------------------------------------------------

```
categories() -> ListStr
```

categories

List of categories.

Returns:

| Type | Description |
| --- | --- |
| `ListStr` | List of the indicator categories. |

constants[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.constants "Permanent link")
--------------------------------------------------------------------------------------------------------------------------

```
constants(
    append: bool, values: Array | List
) -> PandasDTypeLike | None
```

constants

Concatenate / Drop constant(s) to the DataFrame.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `append` | `bool` | Concatenate if `True`. Drop if `False`. Default: `None` | _required_ |
| `values` | `Array` | List/Numpy array of `values` to append/drop from the DataFrame. | _required_ |

Returns:

| Type | Description |
| --- | --- |
| `(Series, DataFrame, None)` | Depends upon parameters. |

See Also
*   [TA DataFrame Constants](https://www.pandas-ta.dev/support/how-to/)

datetime_ordered[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.datetime_ordered "Permanent link")
----------------------------------------------------------------------------------------------------------------------------------------

```
datetime_ordered() -> bool
```

datetime_ordered

DataFrame DateTime ordered?

Returns:

| Type | Description |
| --- | --- |
| `bool` | `True` if the DataFrame is DateTime ordered, otherwise `False`. |

help[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.help "Permanent link")
----------------------------------------------------------------------------------------------------------------

```
help(s: str = '') -> None | TextIO
```

help

Help!

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `s` | `str` | String to search for. Default: `""` | `''` |

Returns:

| Type | Description |
| --- | --- |
| `None | TextIO` | Opens web browser to relevant Pandas TA website page or prints all search keywords. |

indicators[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.indicators "Permanent link")
----------------------------------------------------------------------------------------------------------------------------

```
indicators(
    as_list: bool = None, exclude: ListStr = None
) -> TextIO | ListStr
```

indicators

List indicators.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `as_list` | `bool` | Return as a list. Default: `False` | `None` |
| `exclude` | `ListStr` | The passed in list will be excluded from the indicators list. Default: `None` | `None` |

Returns:

| Type | Description |
| --- | --- |
| `TextIO | ListStr` | Prints list or returns a `ListStr`. |

last_run[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.last_run "Permanent link")
------------------------------------------------------------------------------------------------------------------------

```
last_run() -> str
```

last_run

Detailed string of last run time.

Returns:

| Type | Description |
| --- | --- |
| `str` | Detailed date and time of the lastest run. |

reverse[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.reverse "Permanent link")
----------------------------------------------------------------------------------------------------------------------

```
reverse() -> None
```

reverse

Reverse the DataFrame inplace.

Returns:

| Type | Description |
| --- | --- |
| `None` | DataFrame reversed inplace. |

study[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.study "Permanent link")
------------------------------------------------------------------------------------------------------------------

```
study(*args: Args, **kwargs: DictLike) -> dataclass
```

study

Applies the `ta` listed in a [`Study`](https://www.pandas-ta.dev/api/studies/).

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `chunksize` | `int` | Multiprocessing Pool chunksize. Default: `df.ta.cores` |
| `cores` | `int` | Number of Multiprocessing cores. Default: `df.ta.cores` |
| `exclude` | `ListStr` | List of indicator names. Default: `[]` |
| `ordered` | `bool` | Run `ta` in order. Default: `True` |
| `returns` | `bool` | Return the DataFrame. Default: `False` |
| `timed` | `bool` | Print the process time. Default: `False` |
| `verbose` | `bool` | More verbose output. Default: `False` |

Multiprocessing
Multiprocessing is **not** viable or efficient for some cases. Testing is required per case. See [Multiprocessing](https://docs.python.org/3.12/library/multiprocessing.html) for more information.

ticker[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.ticker "Permanent link")
--------------------------------------------------------------------------------------------------------------------

```
ticker(
    ticker: str = None,
    period: str = None,
    interval: str = None,
    study: Study = None,
    proxy: dict = None,
    timed: bool = False,
    **kwargs: DictLike,
)
```

ticker

Download Historical _ohlcv_ data as a Pandas DataFrame if _yfinance_ package is installed. It also can run a `ta.Study` afterwards.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `ticker` | `str` | Any string for a ticker you would use with `yfinance`. Default: `"SPY"` | `None` |
| `period` | `str` | See the yfinance `history()` method for more options. Default: `"max"` | `None` |
| `interval` | `str` |  | `None` |
| `study` | `ta.Study | str` | After downloading, apply `Study` Default: `None` | `None` |
| `proxy` | `dict` | Proxy dictionary. Default: `{}` | `None` |
| `timed` | `bool` | Print download time to stdout. Default: `False` | `False` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame | None` | _ohlcv_`df` or `None` |

YFinance `history` parameters
*   [_yfinance_](https://ranaroussi.github.io/yfinance/index.html)
*   _yfinance_[`history()`](https://github.com/ranaroussi/yfinance/blob/main/yfinance/scrapers/history.py)

Example

```
import panadas as pd
import pandas_ta as ta

# Simple
df = pd.DataFrame().ta.ticker("SPY", period="2y", timed=True)

# Built In Study
df = pd.DataFrame().ta.ticker("SPY", period="2y", study=ta.AllStudy, timed=True)
```

to_utc[#](https://www.pandas-ta.dev/api/ta-extension/#src.pandas_ta.core.AnalysisIndicators.to_utc "Permanent link")
--------------------------------------------------------------------------------------------------------------------

```
to_utc() -> None
```

to_utc

Set the DataFrame index to UTC.

Returns:

| Type | Description |
| --- | --- |
| `None` | Performs the operation. |




