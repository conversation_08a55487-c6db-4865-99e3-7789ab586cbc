"""
Test Enhanced Smart Vectorized Backtester

This script tests the enhanced backtester with multiple exchanges and error handling.
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_nse_historical():
    """Test NSE historical backtest"""
    print("\n" + "="*80)
    print("🧪 TESTING NSE HISTORICAL BACKTEST")
    print("="*80)
    
    try:
        from smart_vectorized_backtester import SmartVectorizedBacktester, get_token_info
        from shared_api_manager import get_api
        
        api = get_api()
        
        # Test NSE stock
        ticker = 'BATAINDIA'
        exchange = 'NSE'
        
        print(f"🔍 Getting token info for {ticker} on {exchange}...")
        token_info = get_token_info(api, ticker, exchange)
        
        if not token_info:
            print(f"❌ Failed to get token info for {ticker}")
            return False
        
        print(f"✅ Token: {token_info['token']}, Symbol: {token_info['tsym']}")
        
        # Create backtester
        backtester = SmartVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start='09:15',
            end='13:00',
            date='25-06-2025',  # Today's date
            tokenid=token_info['token'],
            enable_momentum_validation=True,
            enable_realtime_detection=True
        )
        
        signals = backtester.run_smart_vectorized_backtest()
        
        print(f"✅ NSE Test completed: {len(signals)} signals found")
        for signal in signals[:3]:  # Show first 3 signals
            print(f"   {signal['time']}: {signal['signal_type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ NSE Test failed: {str(e)}")
        return False

def test_mcx_historical():
    """Test MCX historical backtest"""
    print("\n" + "="*80)
    print("🧪 TESTING MCX HISTORICAL BACKTEST")
    print("="*80)
    
    try:
        from smart_vectorized_backtester import SmartVectorizedBacktester, get_token_info
        from shared_api_manager import get_api
        
        api = get_api()
        
        # Test MCX commodity
        ticker = 'SILVER'  # Try simpler ticker first
        exchange = 'MCX'
        
        print(f"🔍 Getting token info for {ticker} on {exchange}...")
        token_info = get_token_info(api, ticker, exchange)
        
        if not token_info:
            print(f"❌ Failed to get token info for {ticker}")
            # Try alternative ticker
            ticker = 'GOLD'
            print(f"🔍 Trying alternative: {ticker} on {exchange}...")
            token_info = get_token_info(api, ticker, exchange)
        
        if not token_info:
            print(f"❌ Failed to get token info for MCX tickers")
            return False
        
        print(f"✅ Token: {token_info['token']}, Symbol: {token_info['tsym']}")
        
        # Create backtester
        backtester = SmartVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start='09:15',
            end='13:00',
            date='25-06-2025',  # Today's date
            tokenid=token_info['token'],
            enable_momentum_validation=True,
            enable_realtime_detection=True
        )
        
        signals = backtester.run_smart_vectorized_backtest()
        
        print(f"✅ MCX Test completed: {len(signals)} signals found")
        for signal in signals[:3]:  # Show first 3 signals
            print(f"   {signal['time']}: {signal['signal_type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ MCX Test failed: {str(e)}")
        return False

def test_error_handling():
    """Test error handling for invalid tickers"""
    print("\n" + "="*80)
    print("🧪 TESTING ERROR HANDLING")
    print("="*80)
    
    try:
        from smart_vectorized_backtester import SmartVectorizedBacktester, get_token_info
        from shared_api_manager import get_api
        
        api = get_api()
        
        # Test invalid ticker
        ticker = 'INVALIDTICKER123'
        exchange = 'NSE'
        
        print(f"🔍 Testing invalid ticker: {ticker} on {exchange}...")
        token_info = get_token_info(api, ticker, exchange)
        
        if token_info is None:
            print(f"✅ Error handling works: Invalid ticker correctly rejected")
            return True
        else:
            print(f"❌ Error handling failed: Invalid ticker was accepted")
            return False
        
    except Exception as e:
        print(f"✅ Error handling works: Exception caught - {str(e)}")
        return True

def main():
    """Run all tests"""
    logger.info("🚀 Starting Enhanced Backtester Tests...")

    results = []

    # Test Error Handling first (doesn't require API data)
    results.append(("Error Handling", test_error_handling()))

    # Test NSE
    results.append(("NSE Historical", test_nse_historical()))

    # Summary
    print("\n" + "="*80)
    print("📊 TEST RESULTS SUMMARY")
    print("="*80)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n🎯 OVERALL: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 All tests passed! Enhanced backtester is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the error messages above.")

    logger.info("🎉 Testing completed!")

if __name__ == "__main__":
    # Prevent the smart_vectorized_backtester main from running
    import smart_vectorized_backtester
    # Override the main function temporarily
    original_main = smart_vectorized_backtester.main
    smart_vectorized_backtester.main = lambda: None

    main()

    # Restore original main
    smart_vectorized_backtester.main = original_main
