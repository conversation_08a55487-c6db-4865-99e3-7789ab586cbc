Title: Volume - Pandas TA

URL Source: https://www.pandas-ta.dev/api/volume/

Markdown Content:
* * *

ad[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.ad.ad "Permanent link")
----------------------------------------------------------------------------------------

```
ad(
    high: Series,
    low: Series,
    close: Series,
    volume: Series,
    open_: Series = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Accumulation/Distribution

This indicator attempts to quantify accumulation/distribution from a relative position within it's High-Low range and volume.

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/accumulationdistribution-ad/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `open_` | `Series` | Optional `open` Series | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

adosc[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.adosc.adosc "Permanent link")
-------------------------------------------------------------------------------------------------

```
adosc(
    high: Series,
    low: Series,
    close: Series,
    volume: Series,
    open_: Series = None,
    fast: Int = None,
    slow: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Accumulation/Distribution Oscillator

This indicator is an AD oscillator. It is interpreted similarly to MACD and APO.

Sources
*   [investopedia](https://www.investopedia.com/articles/active-trading/031914/understanding-chaikin-oscillator.asp)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `open_` | `Series` | `open` Series | `None` |
| `volume` | `Series` | `volume` Series | _required_ |
| `fast` | `int` | Fast MA period. Default: `12` | `None` |
| `slow` | `int` | Slow MA period. Default: `26` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
Also known as Chaikin Oscillator

Warning
TA-Lib Correlation: `np.float64(0.9989721423605135)`

Tip
Corrective contributions welcome!

* * *

aobv[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.aobv.aobv "Permanent link")
----------------------------------------------------------------------------------------------

```
aobv(
    close: Series,
    volume: Series,
    fast: Int = None,
    slow: Int = None,
    max_lookback: Int = None,
    min_lookback: Int = None,
    mamode: str = None,
    run_length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Archer On Balance Volume

This indicator, by Kevin Johnson, attempts to identify OBV trends using two moving averages. It also attempts to identify if the moving averages are in a long_run or short_run. Finally, it also calculates the rolling maximum and minimum of OBV.

Sources
*   Kevin Johnson
*   [tradingview](https://www.tradingview.com/script/Co1ksara-Trade-Archer-On-balance-Volume-Moving-Averages-v1/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `fast` | `int` | Fast MA period. Default: `4` | `None` |
| `slow` | `int` | Slow MA period. Default: `12` | `None` |
| `max_lookback` | `int` | Maximum OBV period. Default: `2` | `None` |
| `min_lookback` | `int` | Minimum OBV period. Default: `2` | `None` |
| `run_length` | `int` | Long and short run period. Default: `2` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 6 columns |

Note
*   [long_run](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.long_run.long_run)
*   [short_run](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.short_run.short_run)

* * *

cmf[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.cmf.cmf "Permanent link")
-------------------------------------------------------------------------------------------

```
cmf(
    high: Series,
    low: Series,
    close: Series,
    volume: Series,
    open_: Series = None,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Chaikin Money Flow

This indicator attempts to quantify money flow.

Sources
*   [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:chaikin_money_flow_cmf)
*   [tradingview](https://www.tradingview.com/wiki/Chaikin_Money_Flow_(CMF))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `open_` | `Series` | Optional `open` Series. Default: `None` |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
Commonly used with Accumulation/Distribution [ad](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.ad.ad)

efi[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.efi.efi "Permanent link")
-------------------------------------------------------------------------------------------

```
efi(
    close: Series,
    volume: Series,
    length: Int = None,
    mamode: str = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Elder's Force Index

This indicator attempts to quantify movement magnitude as well as potential reversals and price corrections.

Sources
*   [motivewave](https://www.motivewave.com/studies/elders_force_index.htm)
*   [tradingview](https://www.tradingview.com/wiki/Elder%27s_Force_Index_(EFI))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `length` | `int` | The period. Default: `13` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

eom[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.eom.eom "Permanent link")
-------------------------------------------------------------------------------------------

```
eom(
    high: Series,
    low: Series,
    close: Series,
    volume: Series,
    length: Int = None,
    divisor: IntFloat = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Ease of Movement

This indicator is an oscillator that attempts to quantify the relationship with HLC and volume.

Sources
*   [motivewave](https://www.motivewave.com/studies/ease_of_movement.htm)
*   [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:ease_of_movement_emv)
*   [tradingview](https://www.tradingview.com/wiki/Ease_of_Movement_(EOM))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `divisor` | `float` | Divisor. Default: `100_000_000` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

kvo[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.kvo.kvo "Permanent link")
-------------------------------------------------------------------------------------------

```
kvo(
    high: Series,
    low: Series,
    close: Series,
    volume: Series,
    fast: Int = None,
    slow: Int = None,
    signal: Int = None,
    mamode: str = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Klinger Volume Oscillator

This indicator, by Stephen J. Klinger., attempts to predict price reversals.

Sources
*   [daytrading](https://www.daytrading.com/klinger-volume-oscillator)
*   [investopedia](https://www.investopedia.com/terms/k/klingeroscillator.asp)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `fast` | `int` | Fast MA period. Default: `34` | `None` |
| `slow` | `int` | Slow MA period. Default: `55` | `None` |
| `signal` | `int` | Signal period. Default: `13` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

* * *

mfi[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.mfi.mfi "Permanent link")
-------------------------------------------------------------------------------------------

```
mfi(
    high: Series,
    low: Series,
    close: Series,
    volume: Series,
    length: Int = None,
    talib: bool = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Money Flow Index

This indicator is an oscillator that attempts to quantify buying and selling pressure.

Sources
*   [tradingview](https://www.tradingview.com/wiki/Money_Flow_(MFI))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Warning
TA-Lib Correlation: `np.float64(0.9959302104966524)`

Tip
Corrective contributions welcome!

* * *

nvi[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.nvi.nvi "Permanent link")
-------------------------------------------------------------------------------------------

```
nvi(
    close: Series,
    volume: Series,
    length: Int = None,
    initial: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Negative Volume Index

This indicator attempts to identify where smart money is active.

Sources
*   [motivewave](https://www.motivewave.com/studies/negative_volume_index.htm)
*   [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:negative_volume_inde)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `length` | `int` | The period. Default: `13` | `None` |
| `initial` | `int` | Initial value. Default: `1000` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
Commonly paired with [pvi](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.pvi.pvi)

* * *

obv[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.obv.obv "Permanent link")
-------------------------------------------------------------------------------------------

```
obv(
    close: Series,
    volume: Series,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

On Balance Volume

This indicator attempts to quantify buying and selling pressure.

Sources
*   [motivewave](https://www.motivewave.com/studies/on_balance_volume.htm)
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/on-balance-volume-obv/)
*   [tradingview](https://www.tradingview.com/wiki/On_Balance_Volume_(OBV))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

pvi[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.pvi.pvi "Permanent link")
-------------------------------------------------------------------------------------------

```
pvi(
    close: Series,
    volume: Series,
    length: Int = None,
    initial: Int = None,
    mamode: str = None,
    overlay: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Positive Volume Index

This indicator attempts to identify where smart money is active.

Sources
*   [investopedia](https://www.investopedia.com/terms/p/pvi.asp)
*   [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=101)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `length` | `int` | The period. Default: `255` | `None` |
| `initial` | `int` | Initial value. Default: `100` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `overlay` | `bool` | Overlay `initial`. Default: `False` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

Note
Commonly paired with [nvi](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.nvi.nvi)

* * *

pvo[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.pvo.pvo "Permanent link")
-------------------------------------------------------------------------------------------

```
pvo(
    volume: Series,
    fast: Int = None,
    slow: Int = None,
    signal: Int = None,
    scalar: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Percentage Volume Oscillator

This indicator is a volume momentum oscillator.

Sources
*   [fmlabs](https://www.fmlabs.com/reference/default.htm?url=PVO.htm)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `volume` | `Series` | `volume` Series | _required_ |
| `fast` | `int` | Fast MA period. Default: `12` | `None` |
| `slow` | `int` | Slow MA period. Default: `26` | `None` |
| `signal` | `int` | Signal period. Default: `9` | `None` |
| `scalar` | `float` | Scalar. Default: `100` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

pvol[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.pvol.pvol "Permanent link")
----------------------------------------------------------------------------------------------

```
pvol(
    close: Series,
    volume: Series,
    signed: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Price-Volume

This indicator returns the product of Price & Volume (Price * Volume).

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `signed` | `bool` | Return with signs. Default: `False` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

pvr[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.pvr.pvr "Permanent link")
-------------------------------------------------------------------------------------------

```
pvr(
    close: Series,
    volume: Series,
    drift: Int = None,
    **kwargs: DictLike,
) -> Series
```

Price Volume Rank

This indicator, by Anthony J. Macek, is a simple rank computation with close and volume values.

Sources
*   Anthony J. Macek, June, 1994 issue of Technical Analysis of Stocks & Commodities (TASC) Magazine
*   [fmlabs](https://www.fmlabs.com/reference/default.htm?url=PVrank.htm)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `drift` | `int` | Difference amount. Default: `1` | `None` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Signals
*   Buy < 2.5
*   Sell > 2.5

* * *

pvt[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.pvt.pvt "Permanent link")
-------------------------------------------------------------------------------------------

```
pvt(
    close: Series,
    volume: Series,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Price-Volume Trend

This indicator attempts to quantify money flow.

Sources
*   [tradingview](https://www.tradingview.com/wiki/Price_Volume_Trend_(PVT))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

tsv[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.tsv.tsv "Permanent link")
-------------------------------------------------------------------------------------------

```
tsv(
    close: Series,
    volume: Series,
    length: Int = None,
    signal: Int = None,
    mamode: str = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Time Segmented Value

This indicator, by Worden Brothers Inc., attempts to quantify the amount of money flowing at various time segments of price and time; similar to On Balance Volume.

Sources
*   [tc2000](https://help.tc2000.com/m/69404/l/747088-time-segmented-volume)
*   [tradingview](https://www.tradingview.com/script/6GR4ht9X-Time-Segmented-Volume/)
*   [usethinkscript](https://usethinkscript.com/threads/time-segmented-volume-for-thinkorswim.519/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `length` | `int` | The period. Default: `18` | `None` |
| `signal` | `int` | Signal period. Default: `10` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"sma"` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

Note
*   The zero line is called the baseline.
*   Entries and exits signals occur when crossing the baseline.

* * *

vhm[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.vhm.vhm "Permanent link")
-------------------------------------------------------------------------------------------

```
vhm(
    volume: Series,
    length: Int = None,
    std_length=None,
    mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Volume Heatmap

This indicator attempts to quantify volume trend strength of specified length.

Sources
*   [tradingview](https://www.tradingview.com/script/unWex8N4-Heatmap-Volume-xdecow/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `volume` | `Series` | `volume` Series | _required_ |
| `length` | `int` | The period. Default: `610` | `None` |
| `std_length` | `int` | Standard devation. Default: `610` | `None` |
| `mamode` | `str` | Mean MA. See `help(ta.ma)`. Default: `"sma"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Signals
*   Extremely Cold: `vhm <= -0.5`
*   Cold: `-0.5 < vhm <= 1.0`
*   Medium: `1.0 < vhm <= 2.5`
*   Hot: `2.5 < vhm <= 4.0`
*   Extremely Hot: `vhm >= 4`

* * *

vp[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.vp.vp "Permanent link")
----------------------------------------------------------------------------------------

```
vp(
    close: Series,
    volume: Series,
    width: Int = None,
    sort: bool = None,
    **kwargs: DictLike,
) -> DataFrame
```

Volume Profile

This indicator attempts to quantify volume across binned price ranges of certain width.

Sources
*   [ranchodinero](http://www.ranchodinero.com/volume-tpo-essentials/)
*   [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:volume_by_price)
*   [tradingtechnologies](https://www.tradingtechnologies.com/blog/2013/05/15/volume-at-price/)
*   [tradingview](https://www.tradingview.com/wiki/Volume_Profile)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `width` | `int` | Source distrubution count. Default: `10` | `None` |
| `sort` | `value` | Sort `close` before splitting into ranges. Default: `False` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 5 columns |

Note
*   By default, sorts by date index or chronological.
*   Value Area is not calculated.

Warning
**Volume Profile** not a Time `Series`. It is a volume distribution snapshot for an arbitrary `DateTime` Index and thus can not be concatenated onto the existing `DataFrame`.

* * *

vwap[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.vwap.vwap "Permanent link")
----------------------------------------------------------------------------------------------

```
vwap(
    high: Series,
    low: Series,
    close: Series,
    volume: Series,
    anchor: str = None,
    bands: List = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Volume Weighted Average Price

This indicator computes the Volume Weighted Average Price.

Sources
*   [tradingview](https://www.tradingview.com/wiki/Volume_Weighted_Average_Price_(VWAP))
*   [Trading Technologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/volume-weighted-average-price-vwap/)
*   [Stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:vwap_intraday)
*   [Sierra Chart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=108&Name=Volume_Weighted_Average_Price_-_VWAP_-_with_Standard_Deviation_Lines)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `anchor` | `str` | VWAP Anchor. Default: `"D"`. | `None` |
| `bands` | `list` | List of positive `IntFloat` deviations. Default: `[]` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series | pd.DataFrame` | `DataFrame` when `bands` is set. Default: `Series` |

Note
*   Commonly used with intraday charts to identify general direction.
*   Depending on the index values, it will implement various [Timeseries Offset Aliases](https://pandas.pydata.org/pandas-docs/stable/user_guide/timeseries.html#timeseries-offset-aliases)

Tip
*   Negative bands are computed automatically.

* * *

vwma[#](https://www.pandas-ta.dev/api/volume/#src.pandas_ta.volume.vwma.vwma "Permanent link")
----------------------------------------------------------------------------------------------

```
vwma(
    close: Series,
    volume: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Volume Weighted Moving Average

Computes a weighted average using price and volume.

Sources
*   [motivewave](https://www.motivewave.com/studies/volume_weighted_moving_average.htm)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |
