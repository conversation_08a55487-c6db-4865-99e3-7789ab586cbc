---
author: Pandas TA
description: Recommended library usage for custom and bulk processing
generator: mkdocs-1.6.1, mkdocs-material-9.6.14
lang: en
title: Usage - Pandas TA
viewport: width=device-width,initial-scale=1
---

:::::::::::::::::::::::::::::::::::::: {.md-main role="main" md-component="main"}
::::::::::::::::::::::::::::::::::::: {.md-main__inner .md-grid}
:::::: {.md-sidebar .md-sidebar--primary md-component="sidebar" md-type="navigation"}
::::: md-sidebar__scrollwrap
:::: md-sidebar__inner
[![](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdib3g9IjAgMCAyNCAyNCI+PHBhdGggZD0ibTE2IDYgMi4yOSAyLjI5LTQuODggNC44OC00LTRMMiAxNi41OSAzLjQxIDE4bDYtNiA0IDQgNi4zLTYuMjlMMjIgMTJWNnoiIC8+PC9zdmc+)](../../index.html "Pandas TA"){.md-nav__button
.md-logo aria-label="Pandas TA" md-component="logo"} Pandas TA

::: {.md-nav__link .md-nav__container}
[[ Home ]{.md-ellipsis}](../../index.html){.md-nav__link}
[]{.md-nav__icon .md-icon}
:::

[]{.md-nav__icon .md-icon} Home

[[ Installation
]{.md-ellipsis}](../installation/index.html){.md-nav__link}

[ Usage ]{.md-ellipsis} []{.md-nav__icon .md-icon} [[ Usage
]{.md-ellipsis}](index.html){.md-nav__link .md-nav__link--active}

[]{.md-nav__icon .md-icon} Table of contents

- [[ General ]{.md-ellipsis}](index.html#general){.md-nav__link}
  - [[ Version ]{.md-ellipsis}](index.html#version){.md-nav__link}
  - [[ Help ]{.md-ellipsis}](index.html#help){.md-nav__link}
  - [[ Indicators and Library Version
    ]{.md-ellipsis}](index.html#indicators-and-library-version){.md-nav__link}
- [[ Programming Conventions
  ]{.md-ellipsis}](index.html#programming-conventions){.md-nav__link}
  - [[ Example Setup Code
    ]{.md-ellipsis}](index.html#example-setup-code){.md-nav__link}
  - [[ Standard Convention
    ]{.md-ellipsis}](index.html#standard-convention){.md-nav__link}
  - [[ DataFrame \"ta\" Extension Convention
    ]{.md-ellipsis}](index.html#dataframe-ta-extension-convention){.md-nav__link}
  - [[ Pandas \"ta\" Study Convention
    ]{.md-ellipsis}](index.html#pandas-ta-study-convention){.md-nav__link}

[[ Support ]{.md-ellipsis} []{.md-nav__icon
.md-icon}](../../support/index.html){.md-nav__link}

[[ Documentation ]{.md-ellipsis} []{.md-nav__icon
.md-icon}](../../api/index.html){.md-nav__link}

[[ Legal ]{.md-ellipsis} []{.md-nav__icon
.md-icon}](../../legal/index.html){.md-nav__link}
::::
:::::
::::::

::::: {.md-sidebar .md-sidebar--secondary md-component="sidebar" md-type="toc"}
:::: md-sidebar__scrollwrap
::: md-sidebar__inner
[]{.md-nav__icon .md-icon} Table of contents

- [[ General ]{.md-ellipsis}](index.html#general){.md-nav__link}
  - [[ Version ]{.md-ellipsis}](index.html#version){.md-nav__link}
  - [[ Help ]{.md-ellipsis}](index.html#help){.md-nav__link}
  - [[ Indicators and Library Version
    ]{.md-ellipsis}](index.html#indicators-and-library-version){.md-nav__link}
- [[ Programming Conventions
  ]{.md-ellipsis}](index.html#programming-conventions){.md-nav__link}
  - [[ Example Setup Code
    ]{.md-ellipsis}](index.html#example-setup-code){.md-nav__link}
  - [[ Standard Convention
    ]{.md-ellipsis}](index.html#standard-convention){.md-nav__link}
  - [[ DataFrame \"ta\" Extension Convention
    ]{.md-ellipsis}](index.html#dataframe-ta-extension-convention){.md-nav__link}
  - [[ Pandas \"ta\" Study Convention
    ]{.md-ellipsis}](index.html#pandas-ta-study-convention){.md-nav__link}
:::
::::
:::::

::::::::::::::::::::::::::::: {.md-content md-component="content"}
# Usage

Basic commands and **recommended** usage for custom and bulk processing.

## General[\#](index.html#general "Permanent link"){.headerlink}

### Version[\#](index.html#version "Permanent link"){.headerlink}

Prints out the version.

::: {.language-py .highlight}
+-----------------------------------+-----------------------------------+
| ::: linenodiv                     | <div>                             |
|     1                             |                                   |
|     2                             |     import pandas_ta as ta        |
| :::                               |     print(ta.version)             |
|                                   |                                   |
|                                   | </div>                            |
+-----------------------------------+-----------------------------------+
:::

::: {.admonition .danger}
Versions ≤ *0.4.19b*

Bugs encountered in *0.4.19b* (or earlier) are no longer being addressd.
Before making a [bug](../../support/bugs-and-features/index.html)
report, validate that the bug does **not exist**.
:::

\

### Help[\#](index.html#help "Permanent link"){.headerlink}

Ways to utilize Python\'s `help()` command for the library.

::: {.language-py .highlight}
+-----------------------------------+---------------------------------------------------------------+
| ::: linenodiv                     | <div>                                                         |
|      1                            |                                                               |
|      2                            |     import pandas as pd                                       |
|      3                            |     import pandas_ta as ta                                    |
|      4                            |                                                               |
|      5                            |     # Create an empty DataFrame to access the "ta" extension. |
|      6                            |     df = pd.DataFrame()                                       |
|      7                            |                                                               |
|      8                            |     # Indicator Help                                          |
|      9                            |     help(ta.macd)                                             |
|     10                            |                                                               |
|     11                            |     # Help about the DataFrame Extension "ta"                 |
| :::                               |     help(df.ta)                                               |
|                                   |                                                               |
|                                   | </div>                                                        |
+-----------------------------------+---------------------------------------------------------------+
:::

\

### Indicators and Library Version[\#](index.html#indicators-and-library-version "Permanent link"){.headerlink}

Prints out the list of indicators.

::: {.language-py .highlight}
+-----------------------------------+---------------------------------------------------------------+
| ::: linenodiv                     | <div>                                                         |
|     1                             |                                                               |
|     2                             |     import pandas as pd                                       |
|     3                             |     import pandas_ta as ta                                    |
|     4                             |                                                               |
|     5                             |     # Create an empty DataFrame to access the "ta" extension. |
|     6                             |     df = pd.DataFrame()                                       |
|     7                             |                                                               |
| :::                               |     print(df.ta.indicators())                                 |
|                                   |                                                               |
|                                   | </div>                                                        |
+-----------------------------------+---------------------------------------------------------------+
:::

\

## Programming Conventions[\#](index.html#programming-conventions "Permanent link"){.headerlink}

This library supports *three* programming conventions: **Standard** \"TA
Lib\" Convention, Pandas \"**ta**\" DataFrame Extension Convention and
the Pandas \"**ta**\" *study()* Convention.

::: {.admonition .tip}
Best Practice

For maximum control and flexibility, it is **recommended** to use the
`study()` method.
:::

::: {.admonition .warning}
TA Lib

If [**TA Lib**](https://www.ta-lib.org) installed, **TA Lib** integrated
indicators defaults to **TA Lib**, i.e. `talib=True`.
:::

Each \"Programming Convention\" showcases:

- The use of *default* and *user* specified parameters.

- Enabling/disabling *ta-lib*.

  ::: {.admonition .example}
  Standard OHLCV Source Indicators Examples

  - `ema()`
    - Default parameters
  - `macd(fast=5, slow=20, talib=False)`
    - Disable **TA Lib** computation
  :::

- Indicator chaining/composition.

  ::: {.admonition .example}
  Chained/Composed Indicators Examples

  - `ohlc4()`
    - Source
  - `ema(close=ohlc4)`
    - Use `ohlc4` as input instead of `close`
  - `macd(close=ohlc4, fast=5, slow=20, talib=False)`
    - Disable **TA Lib** computation
  :::

\

::: {.admonition .danger}
IMPORTANT

**Thanks** to all those that have sponsored and dontated to the library
in the past! Your support has been greatly appreciated!
![🙏](../../assets/external/cdn.jsdelivr.net/gh/jdecked/twemoji@15.1.0/assets/svg/1f64f.svg ":pray:"){.twemoji}

Only **Installation Bugs/Issues** will addressed for releases between
versions *0.4.25b* and *0.4.66b*. Releases beyond version *0.4.66b* will
**only** be released after **significant** donations, sponsorships or
yearly subscriptions have been received via [Buy Me a
Coffee](https://www.buymeacoffee.com/twopirllc){style="color: #ff9200;"}.

Support Tiers coming soon!
:::

\

### Example Setup Code[\#](index.html#example-setup-code "Permanent link"){.headerlink}

Also examples assumes `yfinance` returns at least the following column
names: \"Open\", \"High\", \"Low\", \"Close\", and \"Volume\";
additional columns are *not* needed for the library.

::: {.language-py .highlight}
+-----------------------------------+--------------------------------------------+
| ::: linenodiv                     | <div>                                      |
|     1                             |                                            |
|     2                             |     import pandas as pd                    |
|     3                             |     import pandas_ta as ta                 |
|     4                             |                                            |
|     5                             |     _df = pd.DataFrame()                   |
|     6                             |                                            |
|     7                             |     df = # df with ohlcv columns           |
|     8                             |     # OR                                   |
| :::                               |     df = _df.ta.ticker("spy", period="5y") |
|                                   |                                            |
|                                   | </div>                                     |
+-----------------------------------+--------------------------------------------+
:::

::: {.admonition .note}
yfinance

`yfinance` must be installed to use `df.ta.ticker()` method.
:::

\

### Standard Convention[\#](index.html#standard-convention "Permanent link"){.headerlink}

With the above \"Example Setup Code\", *Standard* Convention is
*similar* to [TA Lib](https://ta-lib.org) but with lowercase indicator
names. The **Standard** Convention requires more explicit handling of
indicator inputs and outputs as well as concatenating to the DataFrame
(if needed).

:::: {.admonition .example}
Standard OHLCV Source Indicators Examples

::: {.language-py .highlight}
+-----------------------------------+------------------------------------------------------------------------------------+
| ::: linenodiv                     | <div>                                                                              |
|     1                             |                                                                                    |
|     2                             |     # Since ema() returns a Series, it can immediately be concatenated to the df   |
|     3                             |     df["EMA_10"] = ta.ema(df["Close"])                                             |
|     4                             |                                                                                    |
|     5                             |     # Since macd() returns a DataFrame, it must be manually concatenated to the df |
|     6                             |     macddf = ta.macd(df["Close"], fast=5, slow=20, talib=False)                    |
| :::                               |     df = pd.concat([df, macddf])                                                   |
|                                   |                                                                                    |
|                                   | </div>                                                                             |
+-----------------------------------+------------------------------------------------------------------------------------+
:::
::::

:::: {.admonition .example}
Chained/Composed Indicators Examples

::: {.language-py .highlight}
+-----------------------------------+------------------------------------------------------------------------------------+
| ::: linenodiv                     | <div>                                                                              |
|      1                            |                                                                                    |
|      2                            |     ohlc4 = ohlc4(df["Open"], df["High"], df["Low"], df["Close"])                  |
|      3                            |                                                                                    |
|      4                            |     # Since ema() returns a Series, it can immediately be concatenated to the df   |
|      5                            |     df["EMAohlc4_10"] = ema(close=ohlc4)                                           |
|      6                            |                                                                                    |
|      7                            |     # Since macd() returns a DataFrame, it must be manually concatenated to the df |
|      8                            |     macddf = macd(close=ohlc4, fast=5, slow=20, talib=False)                       |
|      9                            |     # Alternatively ...                                                            |
|     10                            |     # macddf = macd(close=df["EMAohlc4_10"], fast=5, slow=20, talib=False)         |
| :::                               |     df = pd.concat([df, macddf])                                                   |
|                                   |                                                                                    |
|                                   | </div>                                                                             |
+-----------------------------------+------------------------------------------------------------------------------------+
:::
::::

\

### DataFrame \"ta\" Extension Convention[\#](index.html#dataframe-ta-extension-convention "Permanent link"){.headerlink}

With the above \"Example Setup Code\", the *DataFrame \"ta\" Extension*
Convention simplifies the number of steps to calculate and concatenate
the indicators onto the `df`.

::: {.admonition .note}
Pandas \"ta\" DataFrame Columns

The DataFrame \"ta\" Extension assumes the following columns: \"Open\",
\"High\", \"Low\", \"Close\", and \"Volume\" exist; additional columns
are not needed for the library.
:::

::: {.admonition .info}
DataFrame Concatenation

DataFrame Concatenation is **not** automatic. Use `append=True`, to
concatenate the indicators to the `df`.
:::

:::: {.admonition .example}
Standard OHLCV Source Indicators Examples

::: {.language-py .highlight}
+-----------------------------------+-----------------------------------------------------------+
| ::: linenodiv                     | <div>                                                     |
|     1                             |                                                           |
|     2                             |     df.ta.ema(append=True)                                |
| :::                               |     df.ta.macd(fast=5, slow=20, talib=False, append=True) |
|                                   |                                                           |
|                                   | </div>                                                    |
+-----------------------------------+-----------------------------------------------------------+
:::
::::

:::: {.admonition .example}
Chained/Composed Indicators Examples

::: {.language-py .highlight}
+-----------------------------------+------------------------------------------------------------------------------+
| ::: linenodiv                     | <div>                                                                        |
|     1                             |                                                                              |
|     2                             |     # One way ...                                                            |
|     3                             |     df.ta.ohlc4(append=True)                                                 |
|     4                             |     df.ta.ema(close=df["OHLC4"], append=True)                                |
|     5                             |     df.ta.macd(close=df["OHLC4"], fast=5, slow=20, talib=False, append=True) |
|     6                             |                                                                              |
|     7                             |     # Alternatively ...                                                      |
|     8                             |     ohlc4 = df.ta.ohlc4(append=True)                                         |
|     9                             |     df.ta.ema(close=ohlc4, append=True)                                      |
| :::                               |     df.ta.macd(close=ohlc4, fast=5, slow=20, talib=False, append=True)       |
|                                   |                                                                              |
|                                   | </div>                                                                       |
+-----------------------------------+------------------------------------------------------------------------------+
:::
::::

\

### Pandas \"ta\" Study Convention[\#](index.html#pandas-ta-study-convention "Permanent link"){.headerlink}

With the above \"Example Setup Code\", the *Pandas \"ta\" `study()`*
Convention also assumes the following columns: \"Open\", \"High\",
\"Low\", \"Close\", and \"Volume\" exist in the `df`.

:::: {.admonition .example}
Standard OHLCV Source Indicators Examples

::: {.language-py .highlight}
+-----------------------------------+-----------------------------------------------------------------------+
| ::: linenodiv                     | <div>                                                                 |
|     1                             |                                                                       |
|     2                             |     std_example = ta.Study(                                           |
|     3                             |         name="Standard OHLCV Example",                                |
|     4                             |         cores=0,                                                      |
|     5                             |         ta=[                                                          |
|     6                             |             {"kind": "ema"},                                          |
|     7                             |             {"kind": "macd", "fast": 5, "slow": : 20, "talib": False} |
|     8                             |         ]                                                             |
|     9                             |     )                                                                 |
| :::                               |     df.ta.study(std_example, verbose=True)                            |
|                                   |                                                                       |
|                                   | </div>                                                                |
+-----------------------------------+-----------------------------------------------------------------------+
:::
::::

::: {.admonition .note}
Column Prefixes/Suffixes

The *study()* also accepts `prefix` and `suffix` parameters for
additional or simplified naming options. This is useful for indicator
chaining to avoid overwriting the results of another indicator with a
different source.
:::

:::: {.admonition .example}
Chained/Composed Indicators Examples

::: {.language-py .highlight}
+-----------------------------------+---------------------------------------------------------------------+
| ::: linenodiv                     | <div>                                                               |
|      1                            |                                                                     |
|      2                            |     chained_example = ta.Study(                                     |
|      3                            |         name="Chained Example",                                     |
|      4                            |         cores=0,                                                    |
|      5                            |         ta=[                                                        |
|      6                            |             { "kind": "ohlc4" },                                    |
|      7                            |             { "kind": "ema", "close": "OHLC4", "prefix": "OHLC4" }, |
|      8                            |             {                                                       |
|      9                            |                 "kind": "macd", "close": "OHLC4",                   |
|     10                            |                 "fast": 5, "slow": 20,                              |
|     11                            |                 "talib": False, "prefix": "OHLC4"                   |
|     12                            |             }                                                       |
|     13                            |         ]                                                           |
|     14                            |     )                                                               |
| :::                               |     df.ta.study(chained_example, verbose=True)                      |
|                                   |                                                                     |
|                                   | </div>                                                              |
+-----------------------------------+---------------------------------------------------------------------+
:::
::::

::: {.admonition .note}
Concatenating

The Pandas *study()* automatically concatenates to the DataFrame.
:::

::: {.admonition .tip}
Multiprocessing

Multiprocessing is **only** available when using Pandas TA\'s
`df.ta.study()` method.
:::


