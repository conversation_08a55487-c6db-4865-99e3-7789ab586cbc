"""
Minute-by-Minute Activity Logger for Ver4 vs Ver6 Comparison

This module creates detailed logs of every minute's activity:
- Signal detection attempts
- Stage 1 and Stage 2 results
- Position status
- Entry/exit events
- Comparison between Ver4 and Ver6 implementations
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MinuteByMinuteLogger:
    """Log every minute's activity for detailed comparison"""
    
    def __init__(self, ticker, date, start_time, end_time, exchange='NSE'):
        self.ticker = ticker
        self.date = date
        self.start_time = start_time
        self.end_time = end_time
        self.exchange = exchange
        self.tokenid = None
        
        # Activity logs
        self.minute_logs = []
        self.position_events = []
        self.signal_events = []
        
        # Import shared components
        from shared_api_manager import get_api
        from shared_nadarya_watson_signal import check_vander
        from shared_sideways_signal_helper import check_sideways
        
        self.api = get_api()
        self.check_vander = check_vander
        self.check_sideways = check_sideways
        
        # Resolve token
        self._resolve_token()
        
        # Position tracking
        self.current_position = None
        self.position_entry_time = None
        self.position_type = None
    
    def _resolve_token(self):
        """Resolve ticker to token ID"""
        try:
            if self.exchange == 'NSE':
                search_result = self.api.searchscrip(exchange='NSE', searchtext=self.ticker + '-EQ')
                if search_result and 'values' in search_result and search_result['values']:
                    self.tokenid = search_result['values'][0]['token']
                    logger.info(f"📊 Resolved {self.ticker} to token: {self.tokenid}")
                else:
                    raise Exception(f"Symbol {self.ticker} not found")
            else:
                raise Exception(f"Exchange {self.exchange} not supported yet")
        except Exception as e:
            logger.error(f"❌ Error resolving token: {str(e)}")
            raise
    
    def log_minute_activity(self):
        """Log activity for every minute from start to end time"""
        logger.info(f"🔍 Starting minute-by-minute logging for {self.ticker}")
        logger.info(f"📅 Date: {self.date}, Time: {self.start_time} to {self.end_time}")
        
        # Parse start and end times
        start_dt = datetime.strptime(self.start_time, '%H:%M')
        end_dt = datetime.strptime(self.end_time, '%H:%M')
        
        # If end time is before start time, assume next day
        if end_dt < start_dt:
            end_dt += timedelta(days=1)
        
        current_time = start_dt
        minute_count = 0
        
        while current_time <= end_dt:
            minute_count += 1
            minute_str = current_time.strftime('%H:%M')
            
            # Log current minute
            logger.info(f"⏰ Minute {minute_count}: {minute_str}")
            
            # Initialize minute log
            minute_log = {
                'minute': minute_count,
                'time': minute_str,
                'has_position': self.current_position is not None,
                'position_type': self.current_position,
                'stage1_checked': False,
                'stage1_sideways': None,
                'stage1_nadarya': None,
                'stage1_pass': False,
                'stage2_checked': False,
                'stage2_sideways': None,
                'stage2_nadarya': None,
                'stage2_pass': False,
                'signal_generated': 0,
                'signal_reason': '',
                'action_taken': '',
                'notes': ''
            }
            
            try:
                # Check if we have a position
                if self.current_position is not None:
                    minute_log['action_taken'] = 'SKIP_SIGNAL_CHECK'
                    minute_log['notes'] = f'Position active: {self.current_position} since {self.position_entry_time}'
                    logger.info(f"📍 Position active ({self.current_position}), skipping signal check")
                    
                    # In real implementation, this would check exit conditions
                    # For logging purposes, we'll simulate position closure after some time
                    if minute_count - self.position_entry_time > 30:  # Close after 30 minutes
                        self._close_position(minute_str, "Simulated exit after 30 minutes")
                        minute_log['action_taken'] = 'POSITION_CLOSED'
                        minute_log['notes'] += ' | Position closed'
                
                else:
                    # No position, check for signals
                    minute_log['action_taken'] = 'CHECK_SIGNALS'
                    
                    # Calculate window times
                    window_07h = timedelta(hours=0.7)
                    window_12h = timedelta(hours=1.2)
                    market_start = datetime.strptime('09:15', '%H:%M')
                    
                    # Check if we have enough data (minimum 0.7 hours from market start)
                    time_from_start = (current_time - market_start).total_seconds() / 3600
                    
                    if time_from_start < 0.7:
                        minute_log['action_taken'] = 'INSUFFICIENT_DATA'
                        minute_log['notes'] = f'Only {time_from_start:.2f}h from market start, need 0.7h minimum'
                        logger.info(f"⏭️ Insufficient data ({time_from_start:.2f}h), skipping")
                    else:
                        # Stage 1: Check 0.7 hour window
                        start_07h = max(current_time - window_07h, market_start)
                        end_07h = current_time
                        
                        minute_log['stage1_checked'] = True
                        logger.info(f"🔍 Stage 1: Checking {start_07h.strftime('%H:%M')}-{end_07h.strftime('%H:%M')}")
                        
                        # Check sideways for 0.7h
                        issideways1, sideways_text1 = self.check_sideways(
                            tokenid=self.tokenid,
                            exchange=self.exchange,
                            date_input=self.date,
                            starttime_input=start_07h.strftime('%H:%M'),
                            endtime_input=end_07h.strftime('%H:%M')
                        )
                        
                        # Check Nadarya for 0.7h
                        isvander1, vander_text1 = self.check_vander(
                            tokenid=self.tokenid,
                            exchange=self.exchange,
                            date_input=self.date,
                            starttime_input=start_07h.strftime('%H:%M'),
                            endtime_input=end_07h.strftime('%H:%M')
                        )
                        
                        minute_log['stage1_sideways'] = bool(issideways1)
                        minute_log['stage1_nadarya'] = bool(isvander1)
                        minute_log['stage1_pass'] = bool(issideways1 and isvander1)
                        
                        logger.info(f"📊 Stage 1: Sideways={issideways1}, Nadarya={isvander1}, Pass={minute_log['stage1_pass']}")
                        
                        if minute_log['stage1_pass']:
                            # Stage 2: Check 1.2 hour window
                            start_12h = max(current_time - window_12h, market_start)
                            end_12h = current_time
                            
                            minute_log['stage2_checked'] = True
                            logger.info(f"🔍 Stage 2: Checking {start_12h.strftime('%H:%M')}-{end_12h.strftime('%H:%M')}")
                            
                            # Check sideways for 1.2h
                            issideways2, sideways_text2 = self.check_sideways(
                                tokenid=self.tokenid,
                                exchange=self.exchange,
                                date_input=self.date,
                                starttime_input=start_12h.strftime('%H:%M'),
                                endtime_input=end_12h.strftime('%H:%M')
                            )
                            
                            # Check Nadarya for 1.2h
                            isvander2, vander_text2 = self.check_vander(
                                tokenid=self.tokenid,
                                exchange=self.exchange,
                                date_input=self.date,
                                starttime_input=start_12h.strftime('%H:%M'),
                                endtime_input=end_12h.strftime('%H:%M')
                            )
                            
                            minute_log['stage2_sideways'] = bool(issideways2)
                            minute_log['stage2_nadarya'] = bool(isvander2)
                            minute_log['stage2_pass'] = bool(issideways2 and isvander2)
                            
                            logger.info(f"📊 Stage 2: Sideways={issideways2}, Nadarya={isvander2}, Pass={minute_log['stage2_pass']}")
                            
                            if minute_log['stage2_pass']:
                                # Determine signal based on Nadarya band
                                if 'Lower band signal present' in vander_text2:
                                    minute_log['signal_generated'] = 1
                                    minute_log['signal_reason'] = 'Lower band signal - CALL'
                                    minute_log['action_taken'] = 'SIGNAL_CALL'
                                    self._open_position(minute_count, minute_str, 'CALL')
                                    logger.info(f"🟢 CALL SIGNAL at {minute_str}")
                                elif 'Upper band signal present' in vander_text2:
                                    minute_log['signal_generated'] = -1
                                    minute_log['signal_reason'] = 'Upper band signal - PUT'
                                    minute_log['action_taken'] = 'SIGNAL_PUT'
                                    self._open_position(minute_count, minute_str, 'PUT')
                                    logger.info(f"🔴 PUT SIGNAL at {minute_str}")
                                else:
                                    minute_log['signal_reason'] = 'Stage 2 passed but no band signal'
                                    minute_log['action_taken'] = 'NO_BAND_SIGNAL'
                                    logger.info(f"⚪ No band signal at {minute_str}")
                            else:
                                minute_log['signal_reason'] = 'Stage 2 failed'
                                minute_log['action_taken'] = 'STAGE2_FAILED'
                                logger.info(f"❌ Stage 2 failed at {minute_str}")
                        else:
                            minute_log['signal_reason'] = 'Stage 1 failed'
                            minute_log['action_taken'] = 'STAGE1_FAILED'
                            logger.info(f"❌ Stage 1 failed at {minute_str}")
                
            except Exception as e:
                minute_log['action_taken'] = 'ERROR'
                minute_log['notes'] = f'Error: {str(e)}'
                logger.error(f"❌ Error at {minute_str}: {str(e)}")
            
            # Add to logs
            self.minute_logs.append(minute_log)
            
            # Move to next minute
            current_time += timedelta(minutes=1)
        
        logger.info(f"✅ Completed logging {minute_count} minutes")
        return self.minute_logs
    
    def _open_position(self, minute_count, time_str, position_type):
        """Open a position"""
        self.current_position = position_type
        self.position_entry_time = minute_count
        
        position_event = {
            'event': 'POSITION_OPENED',
            'time': time_str,
            'minute': minute_count,
            'position_type': position_type,
            'reason': f'{position_type} signal detected'
        }
        
        self.position_events.append(position_event)
        logger.info(f"📍 Position opened: {position_type} at {time_str}")
    
    def _close_position(self, time_str, reason):
        """Close current position"""
        if self.current_position:
            position_event = {
                'event': 'POSITION_CLOSED',
                'time': time_str,
                'position_type': self.current_position,
                'reason': reason
            }
            
            self.position_events.append(position_event)
            logger.info(f"📍 Position closed: {self.current_position} at {time_str} - {reason}")
            
            self.current_position = None
            self.position_entry_time = None
    
    def save_logs(self, filename_prefix="minute_logs"):
        """Save logs to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save minute logs
        minute_file = f"{filename_prefix}_{self.ticker}_{self.date.replace('-', '')}_{timestamp}.json"
        with open(minute_file, 'w') as f:
            json.dump({
                'ticker': self.ticker,
                'date': self.date,
                'start_time': self.start_time,
                'end_time': self.end_time,
                'total_minutes': len(self.minute_logs),
                'minute_logs': self.minute_logs,
                'position_events': self.position_events,
                'summary': self._generate_summary()
            }, f, indent=2)
        
        logger.info(f"💾 Logs saved to {minute_file}")
        return minute_file
    
    def _generate_summary(self):
        """Generate summary statistics"""
        total_minutes = len(self.minute_logs)
        stage1_checks = sum(1 for log in self.minute_logs if log['stage1_checked'])
        stage1_passes = sum(1 for log in self.minute_logs if log['stage1_pass'])
        stage2_checks = sum(1 for log in self.minute_logs if log['stage2_checked'])
        stage2_passes = sum(1 for log in self.minute_logs if log['stage2_pass'])
        signals_generated = sum(1 for log in self.minute_logs if log['signal_generated'] != 0)
        call_signals = sum(1 for log in self.minute_logs if log['signal_generated'] == 1)
        put_signals = sum(1 for log in self.minute_logs if log['signal_generated'] == -1)
        
        return {
            'total_minutes': total_minutes,
            'stage1_checks': stage1_checks,
            'stage1_passes': stage1_passes,
            'stage1_pass_rate': stage1_passes / stage1_checks if stage1_checks > 0 else 0,
            'stage2_checks': stage2_checks,
            'stage2_passes': stage2_passes,
            'stage2_pass_rate': stage2_passes / stage2_checks if stage2_checks > 0 else 0,
            'signals_generated': signals_generated,
            'call_signals': call_signals,
            'put_signals': put_signals,
            'signal_rate': signals_generated / total_minutes if total_minutes > 0 else 0,
            'positions_opened': len([e for e in self.position_events if e['event'] == 'POSITION_OPENED']),
            'positions_closed': len([e for e in self.position_events if e['event'] == 'POSITION_CLOSED'])
        }
    
    def print_summary(self):
        """Print detailed summary"""
        summary = self._generate_summary()
        
        print("\n" + "="*80)
        print(f"📊 MINUTE-BY-MINUTE ACTIVITY SUMMARY")
        print(f"Ticker: {self.ticker} | Date: {self.date} | Time: {self.start_time}-{self.end_time}")
        print("="*80)
        
        print(f"\n📈 OVERALL STATISTICS:")
        print(f"   Total Minutes Analyzed: {summary['total_minutes']}")
        print(f"   Stage 1 Checks: {summary['stage1_checks']}")
        print(f"   Stage 1 Passes: {summary['stage1_passes']} ({summary['stage1_pass_rate']:.1%})")
        print(f"   Stage 2 Checks: {summary['stage2_checks']}")
        print(f"   Stage 2 Passes: {summary['stage2_passes']} ({summary['stage2_pass_rate']:.1%})")
        
        print(f"\n🎯 SIGNAL STATISTICS:")
        print(f"   Total Signals: {summary['signals_generated']}")
        print(f"   Call Signals: {summary['call_signals']}")
        print(f"   Put Signals: {summary['put_signals']}")
        print(f"   Signal Rate: {summary['signal_rate']:.1%}")
        
        print(f"\n📍 POSITION STATISTICS:")
        print(f"   Positions Opened: {summary['positions_opened']}")
        print(f"   Positions Closed: {summary['positions_closed']}")
        
        # Show signal times
        signal_minutes = [log for log in self.minute_logs if log['signal_generated'] != 0]
        if signal_minutes:
            print(f"\n🕐 SIGNAL TIMES:")
            for i, log in enumerate(signal_minutes, 1):
                signal_type = "CALL" if log['signal_generated'] == 1 else "PUT"
                print(f"   {i}. {log['time']} - {signal_type}: {log['signal_reason']}")
        
        # Show position events
        if self.position_events:
            print(f"\n📍 POSITION EVENTS:")
            for i, event in enumerate(self.position_events, 1):
                print(f"   {i}. {event['time']} - {event['event']}: {event.get('position_type', '')} - {event['reason']}")
        
        print("\n" + "="*80)

def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Log minute-by-minute trading activity')
    parser.add_argument('--ticker', default='BATAINDIA', help='Ticker symbol')
    parser.add_argument('--date', default='20-06-2025', help='Date in DD-MM-YYYY format')
    parser.add_argument('--start', default='09:15', help='Start time in HH:MM format')
    parser.add_argument('--end', default='15:30', help='End time in HH:MM format')
    parser.add_argument('--exchange', default='NSE', help='Exchange')
    
    args = parser.parse_args()
    
    logger.info(f"🚀 Starting minute-by-minute logging for {args.ticker}")
    
    # Create logger
    activity_logger = MinuteByMinuteLogger(
        ticker=args.ticker,
        date=args.date,
        start_time=args.start,
        end_time=args.end,
        exchange=args.exchange
    )
    
    # Log all activity
    activity_logger.log_minute_activity()
    
    # Save logs
    log_file = activity_logger.save_logs()
    
    # Print summary
    activity_logger.print_summary()
    
    logger.info(f"🎉 Logging completed! Results saved to {log_file}")

if __name__ == "__main__":
    main()
