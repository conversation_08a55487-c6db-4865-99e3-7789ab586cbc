{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pandas TA ([pandas_ta](https://github.com/twopirllc/pandas-ta)) Strategies for Custom Technical Analysis\n", "\n", "## Topics\n", "- What is a Pandas TA Strategy?\n", "    - Builtin Strategies: __AllStrategy__ and __CommonStrategy__\n", "    - Creating Strategies\n", "- Watchlist Class\n", "    - Strategy Management and Execution\n", "    - **NOTE:** The **watchlist** module is independent of Pandas TA. To easily use it, copy it from your local pandas_ta installation directory into your project directory.\n", "- Indicator Composition/Chaining for more Complex Strategies\n", "    - Comprehensive Example: _MACD and RSI Momo with BBANDS and SMAs 50 & 200 and Cumulative Log Returns_"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Pandas TA v0.2.64b0\n", "To install the Latest Version:\n", "$ pip install -U git+https://github.com/twopirllc/pandas-ta\n", "\n", "Populating the interactive namespace from numpy and matplotlib\n"]}], "source": ["%matplotlib inline\n", "import datetime as dt\n", "\n", "import pandas as pd\n", "import pandas_ta as ta\n", "from alphaVantageAPI.alphavantage import AlphaVantage  # pip install alphaVantage-api\n", "\n", "from watchlist import Watchlist # Is this failing? If so, copy it locally. See above.\n", "\n", "print(f\"\\nPandas TA v{ta.version}\\nTo install the Latest Version:\\n$ pip install -U git+https://github.com/twopirllc/pandas-ta\\n\")\n", "%pylab inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# What is a Pandas TA Strategy?\n", "A _Strategy_ is a simple way to name and group your favorite TA indicators. Technically, a _Strategy_ is a simple Data Class to contain list of indicators and their parameters. __Note__: _Strategy_ is experimental and subject to change. Pandas TA comes with two basic Strategies: __AllStrategy__ and __CommonStrategy__.\n", "\n", "## Strategy Requirements:\n", "- _name_: Some short memorable string.  _Note_: Case-insensitive \"All\" is reserved.\n", "- _ta_: A list of dicts containing keyword arguments to identify the indicator and the indicator's arguments\n", "\n", "## Optional Requirements:\n", "- _description_: A more detailed description of what the Strategy tries to capture. Default: None\n", "- _created_: At datetime string of when it was created. Default: Automatically generated.\n", "\n", "### Things to note:\n", "- A Strategy will __fail__ when consumed by Pandas TA if there is no {\"kind\": \"indicator name\"} attribute."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Builtin Examples"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### All"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name = All\n", "description = All the indicators with their default settings. Pandas TA default.\n", "created = Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)\n", "ta = None\n"]}], "source": ["AllStrategy = ta.AllStrategy\n", "print(\"name =\", AllStrategy.name)\n", "print(\"description =\", AllStrategy.description)\n", "print(\"created =\", AllStrategy.created)\n", "print(\"ta =\", AllStrategy.ta)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Common"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name = Common Price and Volume SMAs\n", "description = Common Price SMAs: 10, 20, 50, 200 and Volume SMA: 20.\n", "created = Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)\n", "ta = [{'kind': 'sma', 'length': 10}, {'kind': 'sma', 'length': 20}, {'kind': 'sma', 'length': 50}, {'kind': 'sma', 'length': 200}, {'kind': 'sma', 'close': 'volume', 'length': 20, 'prefix': 'VOL'}]\n"]}], "source": ["CommonStrategy = ta.CommonStrategy\n", "print(\"name =\", CommonStrategy.name)\n", "print(\"description =\", CommonStrategy.description)\n", "print(\"created =\", CommonStrategy.created)\n", "print(\"ta =\", CommonStrategy.ta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Creating Strategies"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simple Strategy A"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='A', ta=[{'kind': 'sma', 'length': 50}, {'kind': 'sma', 'length': 200}], description='TA Description', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["custom_a = ta.Strategy(name=\"A\", ta=[{\"kind\": \"sma\", \"length\": 50}, {\"kind\": \"sma\", \"length\": 200}])\n", "custom_a"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simple Strategy B"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='B', ta=[{'kind': 'ema', 'length': 8}, {'kind': 'ema', 'length': 21}, {'kind': 'log_return', 'cumulative': True}, {'kind': 'rsi'}, {'kind': 'supertrend'}], description='TA Description', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["custom_b = ta.Strategy(name=\"B\", ta=[{\"kind\": \"ema\", \"length\": 8}, {\"kind\": \"ema\", \"length\": 21}, {\"kind\": \"log_return\", \"cumulative\": True}, {\"kind\": \"rsi\"}, {\"kind\": \"supertrend\"}])\n", "custom_b"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Bad Strategy. (Misspelled Indicator)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='Runtime Failure', ta=[{'kind': 'percet_return'}], description='TA Description', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Misspelled indicator, will fail later when ran with Pandas TA\n", "custom_run_failure = ta.Strategy(name=\"Runtime Failure\", ta=[{\"kind\": \"percet_return\"}])\n", "custom_run_failure"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Strategy Management and Execution with _Watchlist_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initialize AlphaVantage Data Source"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["AlphaVantage(\n", "  end_point:str = https://www.alphavantage.co/query,\n", "  api_key:str = YOUR API KEY,\n", "  export:bool = True,\n", "  export_path:str = .,\n", "  output_size:str = full,\n", "  output:str = csv,\n", "  datatype:str = json,\n", "  clean:bool = True,\n", "  proxy:dict = {}\n", ")"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["AV = AlphaVantage(\n", "    api_key=\"YOUR API KEY\", premium=False,\n", "    output_size='full', clean=True,\n", "    export_path=\".\", export=True\n", ")\n", "AV"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create Watchlist and set it's 'ds' to AlphaVantage"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["data_source = \"av\" # Default\n", "# data_source = \"yahoo\"\n", "watch = Watchlist([\"SPY\", \"IWM\"], ds_name=data_source, timed=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Info about the Watchlist. Note, the default Strategy is \"All\""]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["Watch(name='Watch: SPY, IWM', ds_name='av', tickers[2]='SPY, IWM', tf='D', strategy[5]='Common Price and Volume SMAs')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["watch"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Help about Watchlist"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on class Watchlist in module watchlist:\n", "\n", "class Watchlist(builtins.object)\n", " |  Watchlist(tickers: list, tf: str = None, name: str = None, strategy: pandas_ta.core.Strategy = None, ds_name: str = 'av', **kwargs)\n", " |  \n", " |  # Watchlist Class (** This is subject to change! **)\n", " |  A simple Class to load/download financial market data and automatically\n", " |  apply Technical Analysis indicators with a Pandas TA Strategy.\n", " |  \n", " |  Default Strategy: pandas_ta.CommonStrategy\n", " |  \n", " |  ## Package Support:\n", " |  ### Data Source (Default: AlphaVantage)\n", " |  - AlphaVantage (pip install alphaVantage-api).\n", " |  - Python Binance (pip install python-binance). # Future Support\n", " |  - Yahoo Finance (pip install yfinance). # Almost Supported\n", " |  \n", " |  # Technical Analysis:\n", " |  - Pandas TA (pip install pandas_ta)\n", " |  \n", " |  ## Required Arguments:\n", " |  - tickers: A list of strings containing tickers. Example: [\"SPY\", \"AAPL\"]\n", " |  \n", " |  Methods defined here:\n", " |  \n", " |  __init__(self, tickers: list, tf: str = None, name: str = None, strategy: pandas_ta.core.Strategy = None, ds_name: str = 'av', **kwargs)\n", " |      Initialize self.  See help(type(self)) for accurate signature.\n", " |  \n", " |  __repr__(self) -> str\n", " |      Return repr(self).\n", " |  \n", " |  indicators(self, *args, **kwargs) -> <built-in function any>\n", " |      Returns the list of indicators that are available with Pandas Ta.\n", " |  \n", " |  load(self, ticker: str = None, tf: str = None, index: str = 'date', drop: list = [], plot: bool = False, **kwargs) -> pandas.core.frame.DataFrame\n", " |      Loads or Downloads (if a local csv does not exist) the data from the\n", " |      Data Source. When successful, it returns a Data Frame for the requested\n", " |      ticker. If no tickers are given, it loads all the tickers.\n", " |  \n", " |  ----------------------------------------------------------------------\n", " |  Data descriptors defined here:\n", " |  \n", " |  __dict__\n", " |      dictionary for instance variables (if defined)\n", " |  \n", " |  __weakref__\n", " |      list of weak references to the object (if defined)\n", " |  \n", " |  data\n", " |      When not None, it contains a dictionary of DataFrames keyed by ticker. data = {\"SPY\": pd.DataFrame, ...}\n", " |  \n", " |  name\n", " |      The name of the Watchlist. Default: \"Watchlist: {Watchlist.tickers}\".\n", " |  \n", " |  strategy\n", " |      Sets a valid Strategy. Default: pandas_ta.CommonStrategy\n", " |  \n", " |  tf\n", " |      Alias for timeframe. Default: 'D'\n", " |  \n", " |  tickers\n", " |      tickers\n", " |      \n", " |      If a string, it it converted to a list. Example: \"AAPL\" -> [\"AAPL\"]\n", " |          * Does not accept, comma seperated strings.\n", " |      If a list, checks if it is a list of strings.\n", " |  \n", " |  verbose\n", " |      Toggle the verbose property. Default: False\n", "\n"]}], "source": ["help(Watchlist)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Default Strategy is \"Common\""]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[!] Loading All: SPY, IWM\n", "[+] Downloading[av]: SPY[D]\n", "[+] Strategy: Common Price and Volume SMAs\n", "[i] Indicator arguments: {'timed': False, 'append': True}\n", "[i] Multiprocessing 5 indicators with 7 chunks and 8/8 cpus.\n", "[i] Total indicators: 5\n", "[i] Columns added: 5\n", "[i] Last Run: Saturday April 10, 2021, NYSE: 5:39:04, Local: 9:39:04 PDT, Day 100/365 (27.0%)\n", "[+] Downloading[av]: IWM[D]\n", "[+] Strategy: Common Price and Volume SMAs\n", "[i] Indicator arguments: {'timed': False, 'append': True}\n", "[i] Multiprocessing 5 indicators with 7 chunks and 8/8 cpus.\n", "[i] Total indicators: 5\n", "[i] Columns added: 5\n", "[i] Last Run: Saturday April 10, 2021, NYSE: 5:39:21, Local: 9:39:21 PDT, Day 100/365 (27.0%)\n"]}], "source": ["# No arguments loads all the tickers and applies the Strategy to each ticker.\n", "# The result can be accessed with Watchlist's 'data' property which returns a \n", "# dictionary keyed by ticker and DataFrames as values \n", "watch.load(verbose=True)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'SPY':                 open      high       low     close      volume   SMA_10  \\\n", " date                                                                      \n", " 1999-11-01  136.5000  137.0000  135.5625  135.5625   4006500.0      NaN   \n", " 1999-11-02  135.9687  137.2500  134.5937  134.5937   6516900.0      NaN   \n", " 1999-11-03  136.0000  136.3750  135.1250  135.5000   7222300.0      NaN   \n", " 1999-11-04  136.7500  137.3593  135.7656  136.5312   7907500.0      NaN   \n", " 1999-11-05  138.6250  139.1093  136.7812  137.8750   7431500.0      NaN   \n", " ...              ...       ...       ...       ...         ...      ...   \n", " 2021-04-05  403.4600  406.9400  403.3800  406.3600  91684764.0  394.910   \n", " 2021-04-06  405.7600  407.2400  405.4000  406.1200  62020953.0  396.263   \n", " 2021-04-07  405.9400  406.9600  405.4500  406.5900  55836280.0  397.972   \n", " 2021-04-08  407.9300  408.5800  406.9300  408.5200  57863114.0  400.072   \n", " 2021-04-09  408.3900  411.6700  408.2600  411.4900  61104559.0  402.251   \n", " \n", "              SMA_20    SMA_50    SMA_200   VOL_SMA_20  \n", " date                                                   \n", " 1999-11-01      NaN       NaN        NaN          NaN  \n", " 1999-11-02      NaN       NaN        NaN          NaN  \n", " 1999-11-03      NaN       NaN        NaN          NaN  \n", " 1999-11-04      NaN       NaN        NaN          NaN  \n", " 1999-11-05      NaN       NaN        NaN          NaN  \n", " ...             ...       ...        ...          ...  \n", " 2021-04-05  393.285  388.3778  355.07135  97644589.35  \n", " 2021-04-06  394.505  388.8426  355.54305  94588174.75  \n", " 2021-04-07  395.476  389.2866  356.03280  91698310.95  \n", " 2021-04-08  396.423  389.7812  356.52230  89096496.15  \n", " 2021-04-09  397.321  390.5228  357.01950  87839472.10  \n", " \n", " [5394 rows x 10 columns],\n", " 'IWM':               open     high     low   close      volume   SMA_10    SMA_20  \\\n", " date                                                                         \n", " 2000-05-26   91.06   91.440   90.63   91.44     37400.0      NaN       NaN   \n", " 2000-05-30   92.75   94.810   92.75   94.81     28800.0      NaN       NaN   \n", " 2000-05-31   95.13   96.380   95.13   95.75     18000.0      NaN       NaN   \n", " 2000-06-01   97.11   97.310   97.11   97.31      3500.0      NaN       NaN   \n", " 2000-06-02  101.70  102.400  101.70  102.40     14700.0      NaN       NaN   \n", " ...            ...      ...     ...     ...         ...      ...       ...   \n", " 2021-04-05  226.40  226.535  223.57  224.97  27826550.0  219.366  223.9090   \n", " 2021-04-06  225.00  226.690  223.84  224.31  24907760.0  219.274  224.1875   \n", " 2021-04-07  224.23  224.370  219.94  220.69  26233700.0  219.637  224.0550   \n", " 2021-04-08  221.84  222.820  219.39  222.56  23989440.0  220.689  223.8220   \n", " 2021-04-09  222.49  223.090  221.24  222.59  23267373.0  221.282  223.3405   \n", " \n", "               SMA_50    SMA_200   VOL_SMA_20  \n", " date                                          \n", " 2000-05-26       NaN        NaN          NaN  \n", " 2000-05-30       NaN        NaN          NaN  \n", " 2000-05-31       NaN        NaN          NaN  \n", " 2000-06-01       NaN        NaN          NaN  \n", " 2000-06-02       NaN        NaN          NaN  \n", " ...              ...        ...          ...  \n", " 2021-04-05  221.2644  179.31385  34417859.25  \n", " 2021-04-06  221.4506  179.72680  33632261.05  \n", " 2021-04-07  221.5686  180.12530  33331582.25  \n", " 2021-04-08  221.7538  180.52610  32690454.25  \n", " 2021-04-09  222.0178  180.92405  32590989.45  \n", " \n", " [5250 rows x 10 columns]}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["watch.data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>SMA_10</th>\n", "      <th>SMA_20</th>\n", "      <th>SMA_50</th>\n", "      <th>SMA_200</th>\n", "      <th>VOL_SMA_20</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-05</th>\n", "      <td>403.4600</td>\n", "      <td>406.9400</td>\n", "      <td>403.3800</td>\n", "      <td>406.3600</td>\n", "      <td>91684764.0</td>\n", "      <td>394.910</td>\n", "      <td>393.285</td>\n", "      <td>388.3778</td>\n", "      <td>355.07135</td>\n", "      <td>97644589.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-06</th>\n", "      <td>405.7600</td>\n", "      <td>407.2400</td>\n", "      <td>405.4000</td>\n", "      <td>406.1200</td>\n", "      <td>62020953.0</td>\n", "      <td>396.263</td>\n", "      <td>394.505</td>\n", "      <td>388.8426</td>\n", "      <td>355.54305</td>\n", "      <td>94588174.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-07</th>\n", "      <td>405.9400</td>\n", "      <td>406.9600</td>\n", "      <td>405.4500</td>\n", "      <td>406.5900</td>\n", "      <td>55836280.0</td>\n", "      <td>397.972</td>\n", "      <td>395.476</td>\n", "      <td>389.2866</td>\n", "      <td>356.03280</td>\n", "      <td>91698310.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-08</th>\n", "      <td>407.9300</td>\n", "      <td>408.5800</td>\n", "      <td>406.9300</td>\n", "      <td>408.5200</td>\n", "      <td>57863114.0</td>\n", "      <td>400.072</td>\n", "      <td>396.423</td>\n", "      <td>389.7812</td>\n", "      <td>356.52230</td>\n", "      <td>89096496.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-09</th>\n", "      <td>408.3900</td>\n", "      <td>411.6700</td>\n", "      <td>408.2600</td>\n", "      <td>411.4900</td>\n", "      <td>61104559.0</td>\n", "      <td>402.251</td>\n", "      <td>397.321</td>\n", "      <td>390.5228</td>\n", "      <td>357.01950</td>\n", "      <td>87839472.10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5394 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close      volume   SMA_10  \\\n", "date                                                                      \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625   4006500.0      NaN   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937   6516900.0      NaN   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000   7222300.0      NaN   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312   7907500.0      NaN   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750   7431500.0      NaN   \n", "...              ...       ...       ...       ...         ...      ...   \n", "2021-04-05  403.4600  406.9400  403.3800  406.3600  91684764.0  394.910   \n", "2021-04-06  405.7600  407.2400  405.4000  406.1200  62020953.0  396.263   \n", "2021-04-07  405.9400  406.9600  405.4500  406.5900  55836280.0  397.972   \n", "2021-04-08  407.9300  408.5800  406.9300  408.5200  57863114.0  400.072   \n", "2021-04-09  408.3900  411.6700  408.2600  411.4900  61104559.0  402.251   \n", "\n", "             SMA_20    SMA_50    SMA_200   VOL_SMA_20  \n", "date                                                   \n", "1999-11-01      NaN       NaN        NaN          NaN  \n", "1999-11-02      NaN       NaN        NaN          NaN  \n", "1999-11-03      NaN       NaN        NaN          NaN  \n", "1999-11-04      NaN       NaN        NaN          NaN  \n", "1999-11-05      NaN       NaN        NaN          NaN  \n", "...             ...       ...        ...          ...  \n", "2021-04-05  393.285  388.3778  355.07135  97644589.35  \n", "2021-04-06  394.505  388.8426  355.54305  94588174.75  \n", "2021-04-07  395.476  389.2866  356.03280  91698310.95  \n", "2021-04-08  396.423  389.7812  356.52230  89096496.15  \n", "2021-04-09  397.321  390.5228  357.01950  87839472.10  \n", "\n", "[5394 rows x 10 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["watch.data[\"SPY\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>SMA_10</th>\n", "      <th>SMA_20</th>\n", "      <th>SMA_50</th>\n", "      <th>SMA_200</th>\n", "      <th>VOL_SMA_20</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-05</th>\n", "      <td>403.4600</td>\n", "      <td>406.9400</td>\n", "      <td>403.3800</td>\n", "      <td>406.3600</td>\n", "      <td>91684764.0</td>\n", "      <td>394.910</td>\n", "      <td>393.285</td>\n", "      <td>388.3778</td>\n", "      <td>355.07135</td>\n", "      <td>97644589.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-06</th>\n", "      <td>405.7600</td>\n", "      <td>407.2400</td>\n", "      <td>405.4000</td>\n", "      <td>406.1200</td>\n", "      <td>62020953.0</td>\n", "      <td>396.263</td>\n", "      <td>394.505</td>\n", "      <td>388.8426</td>\n", "      <td>355.54305</td>\n", "      <td>94588174.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-07</th>\n", "      <td>405.9400</td>\n", "      <td>406.9600</td>\n", "      <td>405.4500</td>\n", "      <td>406.5900</td>\n", "      <td>55836280.0</td>\n", "      <td>397.972</td>\n", "      <td>395.476</td>\n", "      <td>389.2866</td>\n", "      <td>356.03280</td>\n", "      <td>91698310.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-08</th>\n", "      <td>407.9300</td>\n", "      <td>408.5800</td>\n", "      <td>406.9300</td>\n", "      <td>408.5200</td>\n", "      <td>57863114.0</td>\n", "      <td>400.072</td>\n", "      <td>396.423</td>\n", "      <td>389.7812</td>\n", "      <td>356.52230</td>\n", "      <td>89096496.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-09</th>\n", "      <td>408.3900</td>\n", "      <td>411.6700</td>\n", "      <td>408.2600</td>\n", "      <td>411.4900</td>\n", "      <td>61104559.0</td>\n", "      <td>402.251</td>\n", "      <td>397.321</td>\n", "      <td>390.5228</td>\n", "      <td>357.01950</td>\n", "      <td>87839472.10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5394 rows × 10 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close      volume   SMA_10  \\\n", "date                                                                      \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625   4006500.0      NaN   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937   6516900.0      NaN   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000   7222300.0      NaN   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312   7907500.0      NaN   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750   7431500.0      NaN   \n", "...              ...       ...       ...       ...         ...      ...   \n", "2021-04-05  403.4600  406.9400  403.3800  406.3600  91684764.0  394.910   \n", "2021-04-06  405.7600  407.2400  405.4000  406.1200  62020953.0  396.263   \n", "2021-04-07  405.9400  406.9600  405.4500  406.5900  55836280.0  397.972   \n", "2021-04-08  407.9300  408.5800  406.9300  408.5200  57863114.0  400.072   \n", "2021-04-09  408.3900  411.6700  408.2600  411.4900  61104559.0  402.251   \n", "\n", "             SMA_20    SMA_50    SMA_200   VOL_SMA_20  \n", "date                                                   \n", "1999-11-01      NaN       NaN        NaN          NaN  \n", "1999-11-02      NaN       NaN        NaN          NaN  \n", "1999-11-03      NaN       NaN        NaN          NaN  \n", "1999-11-04      NaN       NaN        NaN          NaN  \n", "1999-11-05      NaN       NaN        NaN          NaN  \n", "...             ...       ...        ...          ...  \n", "2021-04-05  393.285  388.3778  355.07135  97644589.35  \n", "2021-04-06  394.505  388.8426  355.54305  94588174.75  \n", "2021-04-07  395.476  389.2866  356.03280  91698310.95  \n", "2021-04-08  396.423  389.7812  356.52230  89096496.15  \n", "2021-04-09  397.321  390.5228  357.01950  87839472.10  \n", "\n", "[5394 rows x 10 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 1152x720 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["watch.load(\"SPY\", plot=True, mas=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Easy to swap Strategies and run them"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running Simple Strategy A"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='A', ta=[{'kind': 'sma', 'length': 50}, {'kind': 'sma', 'length': 200}], description='TA Description', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load custom_a into Watchlist and verify\n", "watch.strategy = custom_a\n", "# watch.debug = True\n", "watch.strategy"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded IWM[D]: IWM_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>SMA_50</th>\n", "      <th>SMA_200</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2000-05-26</th>\n", "      <td>91.06</td>\n", "      <td>91.440</td>\n", "      <td>90.63</td>\n", "      <td>91.44</td>\n", "      <td>37400.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-05-30</th>\n", "      <td>92.75</td>\n", "      <td>94.810</td>\n", "      <td>92.75</td>\n", "      <td>94.81</td>\n", "      <td>28800.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-05-31</th>\n", "      <td>95.13</td>\n", "      <td>96.380</td>\n", "      <td>95.13</td>\n", "      <td>95.75</td>\n", "      <td>18000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-06-01</th>\n", "      <td>97.11</td>\n", "      <td>97.310</td>\n", "      <td>97.11</td>\n", "      <td>97.31</td>\n", "      <td>3500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2000-06-02</th>\n", "      <td>101.70</td>\n", "      <td>102.400</td>\n", "      <td>101.70</td>\n", "      <td>102.40</td>\n", "      <td>14700.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-05</th>\n", "      <td>226.40</td>\n", "      <td>226.535</td>\n", "      <td>223.57</td>\n", "      <td>224.97</td>\n", "      <td>27826550.0</td>\n", "      <td>221.2644</td>\n", "      <td>179.31385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-06</th>\n", "      <td>225.00</td>\n", "      <td>226.690</td>\n", "      <td>223.84</td>\n", "      <td>224.31</td>\n", "      <td>24907760.0</td>\n", "      <td>221.4506</td>\n", "      <td>179.72680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-07</th>\n", "      <td>224.23</td>\n", "      <td>224.370</td>\n", "      <td>219.94</td>\n", "      <td>220.69</td>\n", "      <td>26233700.0</td>\n", "      <td>221.5686</td>\n", "      <td>180.12530</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-08</th>\n", "      <td>221.84</td>\n", "      <td>222.820</td>\n", "      <td>219.39</td>\n", "      <td>222.56</td>\n", "      <td>23989440.0</td>\n", "      <td>221.7538</td>\n", "      <td>180.52610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-09</th>\n", "      <td>222.49</td>\n", "      <td>223.090</td>\n", "      <td>221.24</td>\n", "      <td>222.59</td>\n", "      <td>23267373.0</td>\n", "      <td>222.0178</td>\n", "      <td>180.92405</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5250 rows × 7 columns</p>\n", "</div>"], "text/plain": ["              open     high     low   close      volume    SMA_50    SMA_200\n", "date                                                                        \n", "2000-05-26   91.06   91.440   90.63   91.44     37400.0       NaN        NaN\n", "2000-05-30   92.75   94.810   92.75   94.81     28800.0       NaN        NaN\n", "2000-05-31   95.13   96.380   95.13   95.75     18000.0       NaN        NaN\n", "2000-06-01   97.11   97.310   97.11   97.31      3500.0       NaN        NaN\n", "2000-06-02  101.70  102.400  101.70  102.40     14700.0       NaN        NaN\n", "...            ...      ...     ...     ...         ...       ...        ...\n", "2021-04-05  226.40  226.535  223.57  224.97  27826550.0  221.2644  179.31385\n", "2021-04-06  225.00  226.690  223.84  224.31  24907760.0  221.4506  179.72680\n", "2021-04-07  224.23  224.370  219.94  220.69  26233700.0  221.5686  180.12530\n", "2021-04-08  221.84  222.820  219.39  222.56  23989440.0  221.7538  180.52610\n", "2021-04-09  222.49  223.090  221.24  222.59  23267373.0  222.0178  180.92405\n", "\n", "[5250 rows x 7 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["watch.load(\"IWM\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running Simple Strategy B"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='B', ta=[{'kind': 'ema', 'length': 8}, {'kind': 'ema', 'length': 21}, {'kind': 'log_return', 'cumulative': True}, {'kind': 'rsi'}, {'kind': 'supertrend'}], description='TA Description', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load custom_b into Watchlist and verify\n", "watch.strategy = custom_b\n", "watch.strategy"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>EMA_8</th>\n", "      <th>EMA_21</th>\n", "      <th>CUMLOGRET_1</th>\n", "      <th>RSI_14</th>\n", "      <th>SUPERT_7_3.0</th>\n", "      <th>SUPERTd_7_3.0</th>\n", "      <th>SUPERTl_7_3.0</th>\n", "      <th>SUPERTs_7_3.0</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.007172</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.000461</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.007120</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.016915</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-05</th>\n", "      <td>403.4600</td>\n", "      <td>406.9400</td>\n", "      <td>403.3800</td>\n", "      <td>406.3600</td>\n", "      <td>91684764.0</td>\n", "      <td>397.751097</td>\n", "      <td>393.567248</td>\n", "      <td>1.097807</td>\n", "      <td>67.670567</td>\n", "      <td>390.310373</td>\n", "      <td>1</td>\n", "      <td>390.310373</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-06</th>\n", "      <td>405.7600</td>\n", "      <td>407.2400</td>\n", "      <td>405.4000</td>\n", "      <td>406.1200</td>\n", "      <td>62020953.0</td>\n", "      <td>399.610853</td>\n", "      <td>394.708407</td>\n", "      <td>1.097216</td>\n", "      <td>67.264341</td>\n", "      <td>392.803177</td>\n", "      <td>1</td>\n", "      <td>392.803177</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-07</th>\n", "      <td>405.9400</td>\n", "      <td>406.9600</td>\n", "      <td>405.4500</td>\n", "      <td>406.5900</td>\n", "      <td>55836280.0</td>\n", "      <td>401.161775</td>\n", "      <td>395.788552</td>\n", "      <td>1.098373</td>\n", "      <td>67.673599</td>\n", "      <td>393.972009</td>\n", "      <td>1</td>\n", "      <td>393.972009</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-08</th>\n", "      <td>407.9300</td>\n", "      <td>408.5800</td>\n", "      <td>406.9300</td>\n", "      <td>408.5200</td>\n", "      <td>57863114.0</td>\n", "      <td>402.796936</td>\n", "      <td>396.945956</td>\n", "      <td>1.103108</td>\n", "      <td>69.367185</td>\n", "      <td>396.416722</td>\n", "      <td>1</td>\n", "      <td>396.416722</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-09</th>\n", "      <td>408.3900</td>\n", "      <td>411.6700</td>\n", "      <td>408.2600</td>\n", "      <td>411.4900</td>\n", "      <td>61104559.0</td>\n", "      <td>404.728728</td>\n", "      <td>398.268142</td>\n", "      <td>1.110352</td>\n", "      <td>71.814343</td>\n", "      <td>398.785047</td>\n", "      <td>1</td>\n", "      <td>398.785047</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5394 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close      volume       EMA_8  \\\n", "date                                                                         \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625   4006500.0         NaN   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937   6516900.0         NaN   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000   7222300.0         NaN   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312   7907500.0         NaN   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750   7431500.0         NaN   \n", "...              ...       ...       ...       ...         ...         ...   \n", "2021-04-05  403.4600  406.9400  403.3800  406.3600  91684764.0  397.751097   \n", "2021-04-06  405.7600  407.2400  405.4000  406.1200  62020953.0  399.610853   \n", "2021-04-07  405.9400  406.9600  405.4500  406.5900  55836280.0  401.161775   \n", "2021-04-08  407.9300  408.5800  406.9300  408.5200  57863114.0  402.796936   \n", "2021-04-09  408.3900  411.6700  408.2600  411.4900  61104559.0  404.728728   \n", "\n", "                EMA_21  CUMLOGRET_1     RSI_14  SUPERT_7_3.0  SUPERTd_7_3.0  \\\n", "date                                                                          \n", "1999-11-01         NaN          NaN        NaN      0.000000              1   \n", "1999-11-02         NaN    -0.007172        NaN           NaN              1   \n", "1999-11-03         NaN    -0.000461        NaN           NaN              1   \n", "1999-11-04         NaN     0.007120        NaN           NaN              1   \n", "1999-11-05         NaN     0.016915        NaN           NaN              1   \n", "...                ...          ...        ...           ...            ...   \n", "2021-04-05  393.567248     1.097807  67.670567    390.310373              1   \n", "2021-04-06  394.708407     1.097216  67.264341    392.803177              1   \n", "2021-04-07  395.788552     1.098373  67.673599    393.972009              1   \n", "2021-04-08  396.945956     1.103108  69.367185    396.416722              1   \n", "2021-04-09  398.268142     1.110352  71.814343    398.785047              1   \n", "\n", "            SUPERTl_7_3.0  SUPERTs_7_3.0  \n", "date                                      \n", "1999-11-01            NaN            NaN  \n", "1999-11-02            NaN            NaN  \n", "1999-11-03            NaN            NaN  \n", "1999-11-04            NaN            NaN  \n", "1999-11-05            NaN            NaN  \n", "...                   ...            ...  \n", "2021-04-05     390.310373            NaN  \n", "2021-04-06     392.803177            NaN  \n", "2021-04-07     393.972009            NaN  \n", "2021-04-08     396.416722            NaN  \n", "2021-04-09     398.785047            NaN  \n", "\n", "[5394 rows x 13 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["watch.load(\"SPY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running Bad Strategy. (Misspelled indicator)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='Runtime Failure', ta=[{'kind': 'percet_return'}], description='TA Description', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load custom_run_failure into Watchlist and verify\n", "watch.strategy = custom_run_failure\n", "watch.strategy"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded IWM[D]: IWM_D.csv\n", "[X] Oops! 'AnalysisIndicators' object has no attribute 'percet_return'\n"]}], "source": ["try:\n", "    iwm = watch.load(\"IWM\")\n", "except Attribute<PERSON><PERSON><PERSON> as error:\n", "    print(f\"[X] Oops! {error}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Indicator Composition/Chaining\n", "- When you need an indicator to depend on the value of a prior indicator\n", "- Utilitze _prefix_ or _suffix_ to help identify unique columns or avoid column name clashes."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Volume MAs and MA chains"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='Volume MAs and Price MA chain', ta=[{'kind': 'ema', 'close': 'volume', 'length': 10, 'prefix': 'VOLUME'}, {'kind': 'sma', 'close': 'volume', 'length': 20, 'prefix': 'VOLUME'}, {'kind': 'ema', 'length': 5}, {'kind': 'linreg', 'close': 'EMA_5', 'length': 8, 'prefix': 'EMA_5'}], description='TA Description', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set EMA's and SMA's 'close' to 'volume' to create Volume MAs, prefix 'volume' MAs with 'VOLUME' so easy to identify the column\n", "# Take a price EMA and apply LINREG from EMA's output\n", "volmas_price_ma_chain = [\n", "    {\"kind\":\"ema\", \"close\": \"volume\", \"length\": 10, \"prefix\": \"VOLUME\"},\n", "    {\"kind\":\"sma\", \"close\": \"volume\", \"length\": 20, \"prefix\": \"VOLUME\"},\n", "    {\"kind\":\"ema\", \"length\": 5},\n", "    {\"kind\":\"linreg\", \"close\": \"EMA_5\", \"length\": 8, \"prefix\": \"EMA_5\"},\n", "]\n", "vp_ma_chain_ta = ta.Strategy(\"Volume MAs and Price MA chain\", volmas_price_ma_chain)\n", "vp_ma_chain_ta"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Volume MAs and Price MA chain'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update the Watchlist\n", "watch.strategy = vp_ma_chain_ta\n", "watch.strategy.name"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>VOLUME_EMA_10</th>\n", "      <th>VOLUME_SMA_20</th>\n", "      <th>EMA_5</th>\n", "      <th>EMA_5_LR_8</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>136.012480</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-05</th>\n", "      <td>403.4600</td>\n", "      <td>406.9400</td>\n", "      <td>403.3800</td>\n", "      <td>406.3600</td>\n", "      <td>91684764.0</td>\n", "      <td>9.904359e+07</td>\n", "      <td>97644589.35</td>\n", "      <td>399.857343</td>\n", "      <td>397.030535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-06</th>\n", "      <td>405.7600</td>\n", "      <td>407.2400</td>\n", "      <td>405.4000</td>\n", "      <td>406.1200</td>\n", "      <td>62020953.0</td>\n", "      <td>9.231221e+07</td>\n", "      <td>94588174.75</td>\n", "      <td>401.944895</td>\n", "      <td>399.235555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-07</th>\n", "      <td>405.9400</td>\n", "      <td>406.9600</td>\n", "      <td>405.4500</td>\n", "      <td>406.5900</td>\n", "      <td>55836280.0</td>\n", "      <td>8.568022e+07</td>\n", "      <td>91698310.95</td>\n", "      <td>403.493264</td>\n", "      <td>401.232830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-08</th>\n", "      <td>407.9300</td>\n", "      <td>408.5800</td>\n", "      <td>406.9300</td>\n", "      <td>408.5200</td>\n", "      <td>57863114.0</td>\n", "      <td>8.062256e+07</td>\n", "      <td>89096496.15</td>\n", "      <td>405.168842</td>\n", "      <td>403.269982</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-09</th>\n", "      <td>408.3900</td>\n", "      <td>411.6700</td>\n", "      <td>408.2600</td>\n", "      <td>411.4900</td>\n", "      <td>61104559.0</td>\n", "      <td>7.707384e+07</td>\n", "      <td>87839472.10</td>\n", "      <td>407.275895</td>\n", "      <td>405.405385</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5394 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close      volume  VOLUME_EMA_10  \\\n", "date                                                                            \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625   4006500.0            NaN   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937   6516900.0            NaN   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000   7222300.0            NaN   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312   7907500.0            NaN   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750   7431500.0            NaN   \n", "...              ...       ...       ...       ...         ...            ...   \n", "2021-04-05  403.4600  406.9400  403.3800  406.3600  91684764.0   9.904359e+07   \n", "2021-04-06  405.7600  407.2400  405.4000  406.1200  62020953.0   9.231221e+07   \n", "2021-04-07  405.9400  406.9600  405.4500  406.5900  55836280.0   8.568022e+07   \n", "2021-04-08  407.9300  408.5800  406.9300  408.5200  57863114.0   8.062256e+07   \n", "2021-04-09  408.3900  411.6700  408.2600  411.4900  61104559.0   7.707384e+07   \n", "\n", "            VOLUME_SMA_20       EMA_5  EMA_5_LR_8  \n", "date                                               \n", "1999-11-01            NaN         NaN         NaN  \n", "1999-11-02            NaN         NaN         NaN  \n", "1999-11-03            NaN         NaN         NaN  \n", "1999-11-04            NaN         NaN         NaN  \n", "1999-11-05            NaN  136.012480         NaN  \n", "...                   ...         ...         ...  \n", "2021-04-05    97644589.35  399.857343  397.030535  \n", "2021-04-06    94588174.75  401.944895  399.235555  \n", "2021-04-07    91698310.95  403.493264  401.232830  \n", "2021-04-08    89096496.15  405.168842  403.269982  \n", "2021-04-09    87839472.10  407.275895  405.405385  \n", "\n", "[5394 rows x 9 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["spy = watch.load(\"SPY\")\n", "spy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### MACD BBANDS"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='MACD BBands', ta=[{'kind': 'macd'}, {'kind': 'bbands', 'close': 'MACD_12_26_9', 'length': 20, 'prefix': 'MACD'}], description='BBANDS_20 applied to MACD', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# MACD is the initial indicator that BBANDS depends on.\n", "# Set BBANDS's 'close' to MACD's main signal, in this case 'MACD_12_26_9' and add a prefix (or suffix) so it's easier to identify\n", "macd_bands_ta = [\n", "    {\"kind\":\"macd\"},\n", "    {\"kind\":\"bbands\", \"close\": \"MACD_12_26_9\", \"length\": 20, \"prefix\": \"MACD\"}\n", "]\n", "macd_bands_ta = ta.Strategy(\"MACD BBands\", macd_bands_ta, f\"BBANDS_{macd_bands_ta[1]['length']} applied to MACD\")\n", "macd_bands_ta"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["'MACD BBands'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update the Watchlist\n", "watch.strategy = macd_bands_ta\n", "watch.strategy.name"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>MACD_12_26_9</th>\n", "      <th>MACDh_12_26_9</th>\n", "      <th>MACDs_12_26_9</th>\n", "      <th>MACD_BBL_20_2.0</th>\n", "      <th>MACD_BBM_20_2.0</th>\n", "      <th>MACD_BBU_20_2.0</th>\n", "      <th>MACD_BBB_20_2.0</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1999-11-01</th>\n", "      <td>136.5000</td>\n", "      <td>137.0000</td>\n", "      <td>135.5625</td>\n", "      <td>135.5625</td>\n", "      <td>4006500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-02</th>\n", "      <td>135.9687</td>\n", "      <td>137.2500</td>\n", "      <td>134.5937</td>\n", "      <td>134.5937</td>\n", "      <td>6516900.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-03</th>\n", "      <td>136.0000</td>\n", "      <td>136.3750</td>\n", "      <td>135.1250</td>\n", "      <td>135.5000</td>\n", "      <td>7222300.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-04</th>\n", "      <td>136.7500</td>\n", "      <td>137.3593</td>\n", "      <td>135.7656</td>\n", "      <td>136.5312</td>\n", "      <td>7907500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1999-11-05</th>\n", "      <td>138.6250</td>\n", "      <td>139.1093</td>\n", "      <td>136.7812</td>\n", "      <td>137.8750</td>\n", "      <td>7431500.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-05</th>\n", "      <td>403.4600</td>\n", "      <td>406.9400</td>\n", "      <td>403.3800</td>\n", "      <td>406.3600</td>\n", "      <td>91684764.0</td>\n", "      <td>3.496649</td>\n", "      <td>1.051942</td>\n", "      <td>2.444707</td>\n", "      <td>0.173035</td>\n", "      <td>1.949555</td>\n", "      <td>3.726075</td>\n", "      <td>182.248798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-06</th>\n", "      <td>405.7600</td>\n", "      <td>407.2400</td>\n", "      <td>405.4000</td>\n", "      <td>406.1200</td>\n", "      <td>62020953.0</td>\n", "      <td>4.043917</td>\n", "      <td>1.279368</td>\n", "      <td>2.764549</td>\n", "      <td>0.396500</td>\n", "      <td>2.153128</td>\n", "      <td>3.909756</td>\n", "      <td>163.169877</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-07</th>\n", "      <td>405.9400</td>\n", "      <td>406.9600</td>\n", "      <td>405.4500</td>\n", "      <td>406.5900</td>\n", "      <td>55836280.0</td>\n", "      <td>4.464097</td>\n", "      <td>1.359638</td>\n", "      <td>3.104459</td>\n", "      <td>0.570354</td>\n", "      <td>2.365544</td>\n", "      <td>4.160734</td>\n", "      <td>151.778160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-08</th>\n", "      <td>407.9300</td>\n", "      <td>408.5800</td>\n", "      <td>406.9300</td>\n", "      <td>408.5200</td>\n", "      <td>57863114.0</td>\n", "      <td>4.896384</td>\n", "      <td>1.433541</td>\n", "      <td>3.462844</td>\n", "      <td>0.658913</td>\n", "      <td>2.580554</td>\n", "      <td>4.502194</td>\n", "      <td>148.932443</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-09</th>\n", "      <td>408.3900</td>\n", "      <td>411.6700</td>\n", "      <td>408.2600</td>\n", "      <td>411.4900</td>\n", "      <td>61104559.0</td>\n", "      <td>5.416195</td>\n", "      <td>1.562681</td>\n", "      <td>3.853514</td>\n", "      <td>0.613266</td>\n", "      <td>2.791236</td>\n", "      <td>4.969206</td>\n", "      <td>156.057773</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5394 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                open      high       low     close      volume  MACD_12_26_9  \\\n", "date                                                                           \n", "1999-11-01  136.5000  137.0000  135.5625  135.5625   4006500.0           NaN   \n", "1999-11-02  135.9687  137.2500  134.5937  134.5937   6516900.0           NaN   \n", "1999-11-03  136.0000  136.3750  135.1250  135.5000   7222300.0           NaN   \n", "1999-11-04  136.7500  137.3593  135.7656  136.5312   7907500.0           NaN   \n", "1999-11-05  138.6250  139.1093  136.7812  137.8750   7431500.0           NaN   \n", "...              ...       ...       ...       ...         ...           ...   \n", "2021-04-05  403.4600  406.9400  403.3800  406.3600  91684764.0      3.496649   \n", "2021-04-06  405.7600  407.2400  405.4000  406.1200  62020953.0      4.043917   \n", "2021-04-07  405.9400  406.9600  405.4500  406.5900  55836280.0      4.464097   \n", "2021-04-08  407.9300  408.5800  406.9300  408.5200  57863114.0      4.896384   \n", "2021-04-09  408.3900  411.6700  408.2600  411.4900  61104559.0      5.416195   \n", "\n", "            MACDh_12_26_9  MACDs_12_26_9  MACD_BBL_20_2.0  MACD_BBM_20_2.0  \\\n", "date                                                                         \n", "1999-11-01            NaN            NaN              NaN              NaN   \n", "1999-11-02            NaN            NaN              NaN              NaN   \n", "1999-11-03            NaN            NaN              NaN              NaN   \n", "1999-11-04            NaN            NaN              NaN              NaN   \n", "1999-11-05            NaN            NaN              NaN              NaN   \n", "...                   ...            ...              ...              ...   \n", "2021-04-05       1.051942       2.444707         0.173035         1.949555   \n", "2021-04-06       1.279368       2.764549         0.396500         2.153128   \n", "2021-04-07       1.359638       3.104459         0.570354         2.365544   \n", "2021-04-08       1.433541       3.462844         0.658913         2.580554   \n", "2021-04-09       1.562681       3.853514         0.613266         2.791236   \n", "\n", "            MACD_BBU_20_2.0  MACD_BBB_20_2.0  \n", "date                                          \n", "1999-11-01              NaN              NaN  \n", "1999-11-02              NaN              NaN  \n", "1999-11-03              NaN              NaN  \n", "1999-11-04              NaN              NaN  \n", "1999-11-05              NaN              NaN  \n", "...                     ...              ...  \n", "2021-04-05         3.726075       182.248798  \n", "2021-04-06         3.909756       163.169877  \n", "2021-04-07         4.160734       151.778160  \n", "2021-04-08         4.502194       148.932443  \n", "2021-04-09         4.969206       156.057773  \n", "\n", "[5394 rows x 12 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["spy = watch.load(\"SPY\")\n", "spy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive Strategy"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MACD and RSI Momentum with BBANDS and SMAs and Cumulative Log Returns"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='Momo, Bands and SMAs and Cumulative Log Returns', ta=[{'kind': 'sma', 'length': 50}, {'kind': 'sma', 'length': 200}, {'kind': 'bbands', 'length': 20}, {'kind': 'macd'}, {'kind': 'rsi'}, {'kind': 'log_return', 'cumulative': True}, {'kind': 'sma', 'close': 'CUMLOGRET_1', 'length': 5, 'suffix': 'CUMLOGRET'}], description='MACD and RSI Momo with BBANDS and SMAs 50 & 200 and Cumulative Log Returns', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["momo_bands_sma_ta = [\n", "    {\"kind\":\"sma\", \"length\": 50},\n", "    {\"kind\":\"sma\", \"length\": 200},\n", "    {\"kind\":\"bbands\", \"length\": 20},\n", "    {\"kind\":\"macd\"},\n", "    {\"kind\":\"rsi\"},\n", "    {\"kind\":\"log_return\", \"cumulative\": True},\n", "    {\"kind\":\"sma\", \"close\": \"CUMLOGRET_1\", \"length\": 5, \"suffix\": \"CUMLOGRET\"},\n", "]\n", "momo_bands_sma_strategy = ta.Strategy(\n", "    \"Momo, Bands and SMAs and Cumulative Log Returns\", # name\n", "    momo_bands_sma_ta, # ta\n", "    \"MACD and RSI Momo with BBANDS and SMAs 50 & 200 and Cumulative Log Returns\" # description\n", ")\n", "momo_bands_sma_strategy"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Momo, Bands and SMAs and Cumulative Log Returns'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update the Watchlist\n", "watch.strategy = momo_bands_sma_strategy\n", "watch.strategy.name"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>SMA_50</th>\n", "      <th>SMA_200</th>\n", "      <th>BBL_20_2.0</th>\n", "      <th>BBM_20_2.0</th>\n", "      <th>BBU_20_2.0</th>\n", "      <th>BBB_20_2.0</th>\n", "      <th>MACD_12_26_9</th>\n", "      <th>MACDh_12_26_9</th>\n", "      <th>MACDs_12_26_9</th>\n", "      <th>RSI_14</th>\n", "      <th>CUMLOGRET_1</th>\n", "      <th>SMA_5_CUMLOGRET</th>\n", "      <th>0</th>\n", "      <th>30</th>\n", "      <th>70</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-04-05</th>\n", "      <td>403.46</td>\n", "      <td>406.94</td>\n", "      <td>403.38</td>\n", "      <td>406.36</td>\n", "      <td>91684764.0</td>\n", "      <td>388.3778</td>\n", "      <td>355.07135</td>\n", "      <td>382.834363</td>\n", "      <td>393.285</td>\n", "      <td>403.735637</td>\n", "      <td>5.314536</td>\n", "      <td>3.496649</td>\n", "      <td>1.051942</td>\n", "      <td>2.444707</td>\n", "      <td>67.670567</td>\n", "      <td>1.097807</td>\n", "      <td>1.078874</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-06</th>\n", "      <td>405.76</td>\n", "      <td>407.24</td>\n", "      <td>405.40</td>\n", "      <td>406.12</td>\n", "      <td>62020953.0</td>\n", "      <td>388.8426</td>\n", "      <td>355.54305</td>\n", "      <td>384.042695</td>\n", "      <td>394.505</td>\n", "      <td>404.967305</td>\n", "      <td>5.304016</td>\n", "      <td>4.043917</td>\n", "      <td>1.279368</td>\n", "      <td>2.764549</td>\n", "      <td>67.264341</td>\n", "      <td>1.097216</td>\n", "      <td>1.084032</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-07</th>\n", "      <td>405.94</td>\n", "      <td>406.96</td>\n", "      <td>405.45</td>\n", "      <td>406.59</td>\n", "      <td>55836280.0</td>\n", "      <td>389.2866</td>\n", "      <td>356.03280</td>\n", "      <td>384.334301</td>\n", "      <td>395.476</td>\n", "      <td>406.617699</td>\n", "      <td>5.634577</td>\n", "      <td>4.464097</td>\n", "      <td>1.359638</td>\n", "      <td>3.104459</td>\n", "      <td>67.673599</td>\n", "      <td>1.098373</td>\n", "      <td>1.089953</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-08</th>\n", "      <td>407.93</td>\n", "      <td>408.58</td>\n", "      <td>406.93</td>\n", "      <td>408.52</td>\n", "      <td>57863114.0</td>\n", "      <td>389.7812</td>\n", "      <td>356.52230</td>\n", "      <td>384.272821</td>\n", "      <td>396.423</td>\n", "      <td>408.573179</td>\n", "      <td>6.129906</td>\n", "      <td>4.896384</td>\n", "      <td>1.433541</td>\n", "      <td>3.462844</td>\n", "      <td>69.367185</td>\n", "      <td>1.103108</td>\n", "      <td>1.096012</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-09</th>\n", "      <td>408.39</td>\n", "      <td>411.67</td>\n", "      <td>408.26</td>\n", "      <td>411.49</td>\n", "      <td>61104559.0</td>\n", "      <td>390.5228</td>\n", "      <td>357.01950</td>\n", "      <td>383.604942</td>\n", "      <td>397.321</td>\n", "      <td>411.037058</td>\n", "      <td>6.904270</td>\n", "      <td>5.416195</td>\n", "      <td>1.562681</td>\n", "      <td>3.853514</td>\n", "      <td>71.814343</td>\n", "      <td>1.110352</td>\n", "      <td>1.101371</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "      <td>70</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              open    high     low   close      volume    SMA_50    SMA_200  \\\n", "date                                                                          \n", "2021-04-05  403.46  406.94  403.38  406.36  91684764.0  388.3778  355.07135   \n", "2021-04-06  405.76  407.24  405.40  406.12  62020953.0  388.8426  355.54305   \n", "2021-04-07  405.94  406.96  405.45  406.59  55836280.0  389.2866  356.03280   \n", "2021-04-08  407.93  408.58  406.93  408.52  57863114.0  389.7812  356.52230   \n", "2021-04-09  408.39  411.67  408.26  411.49  61104559.0  390.5228  357.01950   \n", "\n", "            BBL_20_2.0  BBM_20_2.0  BBU_20_2.0  BBB_20_2.0  MACD_12_26_9  \\\n", "date                                                                       \n", "2021-04-05  382.834363     393.285  403.735637    5.314536      3.496649   \n", "2021-04-06  384.042695     394.505  404.967305    5.304016      4.043917   \n", "2021-04-07  384.334301     395.476  406.617699    5.634577      4.464097   \n", "2021-04-08  384.272821     396.423  408.573179    6.129906      4.896384   \n", "2021-04-09  383.604942     397.321  411.037058    6.904270      5.416195   \n", "\n", "            MACDh_12_26_9  MACDs_12_26_9     RSI_14  CUMLOGRET_1  \\\n", "date                                                               \n", "2021-04-05       1.051942       2.444707  67.670567     1.097807   \n", "2021-04-06       1.279368       2.764549  67.264341     1.097216   \n", "2021-04-07       1.359638       3.104459  67.673599     1.098373   \n", "2021-04-08       1.433541       3.462844  69.367185     1.103108   \n", "2021-04-09       1.562681       3.853514  71.814343     1.110352   \n", "\n", "            SMA_5_CUMLOGRET  0  30  70  \n", "date                                    \n", "2021-04-05         1.078874  0  30  70  \n", "2021-04-06         1.084032  0  30  70  \n", "2021-04-07         1.089953  0  30  70  \n", "2021-04-08         1.096012  0  30  70  \n", "2021-04-09         1.101371  0  30  70  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["spy = watch.load(\"SPY\")\n", "# Apply constants to the DataFrame for indicators\n", "spy.ta.constants(True, [0, 30, 70])\n", "spy.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Additional Strategy Options"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The ```params``` keyword takes a _tuple_ as a shorthand to the parameter arguments in order.\n", "* **Note**: If the indicator arguments change, so will results. Breaking Changes will **always** be posted on the README.\n", "\n", "The ```col_numbers``` keyword takes a _tuple_ specifying which column to return if the result is a DataFrame."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["Strategy(name='EMA, MACD History, Outter BBands, Log Returns', ta=[{'kind': 'ema', 'params': (10,)}, {'kind': 'macd', 'params': (9, 19, 10), 'col_numbers': (1,)}, {'kind': 'bbands', 'col_numbers': (0, 2), 'col_names': ('LB', 'UB')}, {'kind': 'log_return', 'params': (5, False)}], description='EMA, MACD History, BBands(LB, UB), and Log Returns Strategy', created='Saturday April 10, 2021, NYSE: 5:39:01, Local: 9:39:01 PDT, Day 100/365 (27.0%)')"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["params_ta = [\n", "    {\"kind\":\"ema\", \"params\": (10,)},\n", "    # params sets MACD's keyword arguments: fast=9, slow=19, signal=10\n", "    # and returning the 2nd column: histogram\n", "    {\"kind\":\"macd\", \"params\": (9, 19, 10), \"col_numbers\": (1,)},\n", "    # Selects the Lower and Upper Bands and renames them LB and UB, ignoring the MB\n", "    {\"kind\":\"bbands\", \"col_numbers\": (0,2), \"col_names\": (\"LB\", \"UB\")},\n", "    {\"kind\":\"log_return\", \"params\": (5, False)},\n", "]\n", "params_ta_strategy = ta.Strategy(\n", "    \"EMA, MACD History, Outter BBands, Log Returns\", # name\n", "    params_ta, # ta\n", "    \"EMA, MACD History, BBands(LB, UB), and Log Returns Strategy\" # description\n", ")\n", "params_ta_strategy"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["'EMA, MACD History, Outter BBands, Log Returns'"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# Update the Watchlist\n", "watch.strategy = params_ta_strategy\n", "watch.strategy.name"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[i] Loaded SPY[D]: SPY_D.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>EMA_10</th>\n", "      <th>MACDh_9_19_10</th>\n", "      <th>LB</th>\n", "      <th>UB</th>\n", "      <th>LOGRET_5</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-04-05</th>\n", "      <td>403.46</td>\n", "      <td>406.94</td>\n", "      <td>403.38</td>\n", "      <td>406.36</td>\n", "      <td>91684764.0</td>\n", "      <td>396.789148</td>\n", "      <td>1.302704</td>\n", "      <td>390.173232</td>\n", "      <td>407.350768</td>\n", "      <td>0.025876</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-06</th>\n", "      <td>405.76</td>\n", "      <td>407.24</td>\n", "      <td>405.40</td>\n", "      <td>406.12</td>\n", "      <td>62020953.0</td>\n", "      <td>398.485666</td>\n", "      <td>1.529192</td>\n", "      <td>391.193677</td>\n", "      <td>410.466323</td>\n", "      <td>0.025790</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-07</th>\n", "      <td>405.94</td>\n", "      <td>406.96</td>\n", "      <td>405.45</td>\n", "      <td>406.59</td>\n", "      <td>55836280.0</td>\n", "      <td>399.959181</td>\n", "      <td>1.561328</td>\n", "      <td>395.008877</td>\n", "      <td>411.395123</td>\n", "      <td>0.029603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-08</th>\n", "      <td>407.93</td>\n", "      <td>408.58</td>\n", "      <td>406.93</td>\n", "      <td>408.52</td>\n", "      <td>57863114.0</td>\n", "      <td>401.515694</td>\n", "      <td>1.590588</td>\n", "      <td>400.329889</td>\n", "      <td>410.950111</td>\n", "      <td>0.030294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-04-09</th>\n", "      <td>408.39</td>\n", "      <td>411.67</td>\n", "      <td>408.26</td>\n", "      <td>411.49</td>\n", "      <td>61104559.0</td>\n", "      <td>403.329204</td>\n", "      <td>1.695492</td>\n", "      <td>403.766969</td>\n", "      <td>411.865031</td>\n", "      <td>0.026796</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              open    high     low   close      volume      EMA_10  \\\n", "date                                                                 \n", "2021-04-05  403.46  406.94  403.38  406.36  91684764.0  396.789148   \n", "2021-04-06  405.76  407.24  405.40  406.12  62020953.0  398.485666   \n", "2021-04-07  405.94  406.96  405.45  406.59  55836280.0  399.959181   \n", "2021-04-08  407.93  408.58  406.93  408.52  57863114.0  401.515694   \n", "2021-04-09  408.39  411.67  408.26  411.49  61104559.0  403.329204   \n", "\n", "            MACDh_9_19_10          LB          UB  LOGRET_5  \n", "date                                                         \n", "2021-04-05       1.302704  390.173232  407.350768  0.025876  \n", "2021-04-06       1.529192  391.193677  410.466323  0.025790  \n", "2021-04-07       1.561328  395.008877  411.395123  0.029603  \n", "2021-04-08       1.590588  400.329889  410.950111  0.030294  \n", "2021-04-09       1.695492  403.766969  411.865031  0.026796  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["spy = watch.load(\"SPY\")\n", "spy.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Disclaimer\n", "* All investments involve risk, and the past performance of a security, industry, sector, market, financial product, trading strategy, or individual’s trading does not guarantee future results or returns. Investors are fully responsible for any investment decisions they make. Such decisions should be based solely on an evaluation of their financial circumstances, investment objectives, risk tolerance, and liquidity needs.\n", "\n", "* Any opinions, news, research, analyses, prices, or other information offered is provided as general market commentary, and does not constitute investment advice. I will not accept liability for any loss or damage, including without limitation any loss of profit, which may arise directly or indirectly from use of or reliance on such information."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 4}