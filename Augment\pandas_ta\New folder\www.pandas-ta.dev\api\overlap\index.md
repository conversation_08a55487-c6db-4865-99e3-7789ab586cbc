---
author: Pandas TA
description: Pandas TA list of Overlap Indicators
generator: mkdocs-1.6.1, mkdocs-material-9.6.14
lang: en
title: Overlap - Pandas TA
viewport: width=device-width,initial-scale=1
---

::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: {.md-main role="main" md-component="main"}
:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: {.md-main__inner .md-grid}
:::::: {.md-sidebar .md-sidebar--primary md-component="sidebar" md-type="navigation"}
::::: md-sidebar__scrollwrap
:::: md-sidebar__inner
[![](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdib3g9IjAgMCAyNCAyNCI+PHBhdGggZD0ibTE2IDYgMi4yOSAyLjI5LTQuODggNC44OC00LTRMMiAxNi41OSAzLjQxIDE4bDYtNiA0IDQgNi4zLTYuMjlMMjIgMTJWNnoiIC8+PC9zdmc+)](../../index.html "Pandas TA"){.md-nav__button
.md-logo aria-label="Pandas TA" md-component="logo"} Pandas TA

[[ Home ]{.md-ellipsis} []{.md-nav__icon
.md-icon}](../../index.html){.md-nav__link}

[[ Support ]{.md-ellipsis} []{.md-nav__icon
.md-icon}](../../support/index.html){.md-nav__link}

::: {.md-nav__link .md-nav__container}
[[ Documentation ]{.md-ellipsis}](../index.html){.md-nav__link}
[]{.md-nav__icon .md-icon}
:::

[]{.md-nav__icon .md-icon} Documentation

[[ DataFrame Extension
]{.md-ellipsis}](../ta-extension/index.html){.md-nav__link}

[[ Studies ]{.md-ellipsis}](../studies/index.html){.md-nav__link}

[[ Events ]{.md-ellipsis}](../events/index.html){.md-nav__link}

[[ Custom Directory
]{.md-ellipsis}](../custom/index.html){.md-nav__link}

[ Indicators ]{.md-ellipsis} []{.md-nav__icon .md-icon}

[]{.md-nav__icon .md-icon} Indicators

[[ Candle ]{.md-ellipsis}](../candle/index.html){.md-nav__link}

[[ Cycle ]{.md-ellipsis}](../cycle/index.html){.md-nav__link}

[[ Momentum ]{.md-ellipsis}](../momentum/index.html){.md-nav__link}

[ Overlap ]{.md-ellipsis} []{.md-nav__icon .md-icon} [[ Overlap
]{.md-ellipsis}](index.html){.md-nav__link .md-nav__link--active}

[]{.md-nav__icon .md-icon} Table of contents

- [[  alligator
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.alligator.alligator){.md-nav__link}
- [[  alma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.alma.alma){.md-nav__link}
- [[  dema
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.dema.dema){.md-nav__link}
- [[  ema
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ema.ema){.md-nav__link}
- [[  fwma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.fwma.fwma){.md-nav__link}
- [[  hilo
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hilo.hilo){.md-nav__link}
- [[  hl2
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hl2.hl2){.md-nav__link}
- [[  hlc3
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hlc3.hlc3){.md-nav__link}
- [[  hma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hma.hma){.md-nav__link}
- [[  hwma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hwma.hwma){.md-nav__link}
- [[  ichimoku
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ichimoku.ichimoku){.md-nav__link}
- [[  jma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.jma.jma){.md-nav__link}
- [[  kama
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.kama.kama){.md-nav__link}
- [[  linreg
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.linreg.linreg){.md-nav__link}
- [[  mama
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.mama.mama){.md-nav__link}
- [[  mcgd
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.mcgd.mcgd){.md-nav__link}
- [[  midpoint
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.midpoint.midpoint){.md-nav__link}
- [[  midprice
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.midprice.midprice){.md-nav__link}
- [[  ohlc4
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ohlc4.ohlc4){.md-nav__link}
- [[  pwma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.pwma.pwma){.md-nav__link}
- [[  rma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.rma.rma){.md-nav__link}
- [[  sinwma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.sinwma.sinwma){.md-nav__link}
- [[  sma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.sma.sma){.md-nav__link}
- [[  smma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.smma.smma){.md-nav__link}
- [[  ssf
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ssf.ssf){.md-nav__link}
- [[  ssf3
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ssf3.ssf3){.md-nav__link}
- [[  supertrend
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.supertrend.supertrend){.md-nav__link}
- [[  swma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.swma.swma){.md-nav__link}
- [[  t3
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.t3.t3){.md-nav__link}
- [[  tema
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.tema.tema){.md-nav__link}
- [[  trima
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.trima.trima){.md-nav__link}
- [[  vidya
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.vidya.vidya){.md-nav__link}
- [[  wcp
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.wcp.wcp){.md-nav__link}
- [[  wma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.wma.wma){.md-nav__link}
- [[  zlma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.zlma.zlma){.md-nav__link}

[[ Performance
]{.md-ellipsis}](../performance/index.html){.md-nav__link}

[[ Statistics ]{.md-ellipsis}](../statistics/index.html){.md-nav__link}

[[ Trend ]{.md-ellipsis}](../trend/index.html){.md-nav__link}

[[ Volatility ]{.md-ellipsis}](../volatility/index.html){.md-nav__link}

[[ Volume ]{.md-ellipsis}](../volume/index.html){.md-nav__link}

[[ Utilities ]{.md-ellipsis}](../utilities/index.html){.md-nav__link}

[[ Legal ]{.md-ellipsis} []{.md-nav__icon
.md-icon}](../../legal/index.html){.md-nav__link}
::::
:::::
::::::

::::: {.md-sidebar .md-sidebar--secondary md-component="sidebar" md-type="toc"}
:::: md-sidebar__scrollwrap
::: md-sidebar__inner
[]{.md-nav__icon .md-icon} Table of contents

- [[  alligator
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.alligator.alligator){.md-nav__link}
- [[  alma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.alma.alma){.md-nav__link}
- [[  dema
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.dema.dema){.md-nav__link}
- [[  ema
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ema.ema){.md-nav__link}
- [[  fwma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.fwma.fwma){.md-nav__link}
- [[  hilo
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hilo.hilo){.md-nav__link}
- [[  hl2
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hl2.hl2){.md-nav__link}
- [[  hlc3
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hlc3.hlc3){.md-nav__link}
- [[  hma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hma.hma){.md-nav__link}
- [[  hwma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.hwma.hwma){.md-nav__link}
- [[  ichimoku
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ichimoku.ichimoku){.md-nav__link}
- [[  jma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.jma.jma){.md-nav__link}
- [[  kama
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.kama.kama){.md-nav__link}
- [[  linreg
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.linreg.linreg){.md-nav__link}
- [[  mama
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.mama.mama){.md-nav__link}
- [[  mcgd
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.mcgd.mcgd){.md-nav__link}
- [[  midpoint
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.midpoint.midpoint){.md-nav__link}
- [[  midprice
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.midprice.midprice){.md-nav__link}
- [[  ohlc4
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ohlc4.ohlc4){.md-nav__link}
- [[  pwma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.pwma.pwma){.md-nav__link}
- [[  rma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.rma.rma){.md-nav__link}
- [[  sinwma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.sinwma.sinwma){.md-nav__link}
- [[  sma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.sma.sma){.md-nav__link}
- [[  smma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.smma.smma){.md-nav__link}
- [[  ssf
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ssf.ssf){.md-nav__link}
- [[  ssf3
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.ssf3.ssf3){.md-nav__link}
- [[  supertrend
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.supertrend.supertrend){.md-nav__link}
- [[  swma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.swma.swma){.md-nav__link}
- [[  t3
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.t3.t3){.md-nav__link}
- [[  tema
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.tema.tema){.md-nav__link}
- [[  trima
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.trima.trima){.md-nav__link}
- [[  vidya
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.vidya.vidya){.md-nav__link}
- [[  wcp
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.wcp.wcp){.md-nav__link}
- [[  wma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.wma.wma){.md-nav__link}
- [[  zlma
  ]{.md-ellipsis}](index.html#src.pandas_ta.overlap.zlma.zlma){.md-nav__link}
:::
::::
:::::

:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: {.md-content md-component="content"}
# Overlap

------------------------------------------------------------------------

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [alligator]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.alligator.alligator "Permanent link"){.headerlink} {#src.pandas_ta.overlap.alligator.alligator .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    alligator(
        close: Series,
        jaw: Int = None,
        teeth: Int = None,
        lips: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Bill Williams Alligator

This indicator, by Bill Williams, attempts to identify trends.

Sources

- [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=175&Name=Bill_Williams_Alligator)
- [tradingview](https://www.tradingview.com/scripts/alligator/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `jaw`           | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Jaw period. Default:   |                 |
|                 |                                   | `13`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `teeth`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Teeth period. Default: |                 |
|                 |                                   | `8`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `lips`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Lips period. Default:  |                 |
|                 |                                   | `5`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 3 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Tip

To avoid data leaks, offsets are to be done manually.

Note

Williams believed the fx market trends between 15% and 30% of the time.
Otherwise it is range bound. Inspired by fractal geometry, where the
outputs are meant to resemble an alligator opening and closing its
mouth. It It consists of 3 lines: Jaw, Teeth, and Lips which each have
differing lengths.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [alma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.alma.alma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.alma.alma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    alma(
        close: Series,
        length: Int = None,
        sigma: IntFloat = None,
        dist_offset: IntFloat = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Arnaud Legoux Moving Average

This indicator attempts to reduce lag with Gaussian smoothing.

Sources

- [prorealcode](https://www.prorealcode.com/prorealtime-indicators/alma-arnaud-legoux-moving-average/)
- [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=475&Name=Moving_Average_-_Arnaud_Legoux)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `9`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `sigma`         | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Smoothing value.       |                 |
|                 |                                   | Default `6.0`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `dist_offset`   | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Distribution offset,   |                 |
|                 |                                   | range `[0, 1]`.        |                 |
|                 |                                   | Default `0.85`         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [dema]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.dema.dema "Permanent link"){.headerlink} {#src.pandas_ta.overlap.dema.dema .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    dema(
        close: Series,
        length: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Double Exponential Moving Average

This indicator attempts to create a smoother average with less lag than
the EMA.

Sources

- [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/double-exponential-moving-average-dema/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Warning

TA-Lib Correlation: `np.float64(0.9999894518202522)`

Tip

Corrective contributions welcome!
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [ema]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.ema.ema "Permanent link"){.headerlink} {#src.pandas_ta.overlap.ema.ema .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    ema(
        close: Series,
        length: Int = None,
        talib: bool = None,
        presma: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Exponential Moving Average

This Moving Average is more responsive than the Simple Moving Average
(SMA).

Sources

- [investopedia](https://www.investopedia.com/ask/answers/122314/what-exponential-moving-average-ema-formula-and-how-ema-calculated.asp)
- [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:moving_averages)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `presma`        | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Initialize with SMA    |                 |
|                 |                                   | like TA Lib. Default:  |                 |
|                 |                                   | `True`                 |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `adjust`              | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [fwma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.fwma.fwma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.fwma.fwma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    fwma(
        close: Series,
        length: Int = None,
        asc: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Fibonacci\'s Weighted Moving Average

This indicator, by Kevin Johnson, is similar to a Weighted Moving
Average (WMA) where the weights are based on the Fibonacci Sequence.

Sources

- Kevin Johnson

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `asc`           | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Recent values weigh    |                 |
|                 |                                   | more. Default: `True`  |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [hilo]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.hilo.hilo "Permanent link"){.headerlink} {#src.pandas_ta.overlap.hilo.hilo .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    hilo(
        high: Series,
        low: Series,
        close: Series,
        high_length: Int = None,
        low_length: Int = None,
        mamode: str = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Gann HiLo Activator

This indicator, by Robert Krausz, uses two different Moving Averages to
identify trends.

Sources

- Gann HiLo Activator, , Stocks & Commodities Magazine, 1998
- [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=447&Name=Gann_HiLo_Activator)
- [tradingview](https://www.tradingview.com/script/XNQSLIYb-Gann-High-Low/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `high_length`   | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | High period. Default:  |                 |
|                 |                                   | `13`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low_length`    | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Low period. Default:   |                 |
|                 |                                   | `21`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"sma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 3 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Note

Increasing `high_length` and decreasing `low_length` is better for short
trades and vice versa for long trades.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [hl2]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.hl2.hl2 "Permanent link"){.headerlink} {#src.pandas_ta.overlap.hl2.hl2 .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    hl2(
        high: Series,
        low: Series,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
HL2

HL2 is the midpoint/average of high and low.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+-------------------------------+
| Name                  | Type                     | Description                   |
+=======================+==========================+===============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description        |
|                       |                          | `pd.DataFrame.fillna(value)`. |
|                       |                          | Only works when offset.       |
|                       |                          | :::                           |
+-----------------------+--------------------------+-------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [hlc3]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.hlc3.hlc3 "Permanent link"){.headerlink} {#src.pandas_ta.overlap.hlc3.hlc3 .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    hlc3(
        high: Series,
        low: Series,
        close: Series,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
HLC3

HLC3 is the average of high, low and close.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+-------------------------------+
| Name                  | Type                     | Description                   |
+=======================+==========================+===============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description        |
|                       |                          | `pd.DataFrame.fillna(value)`. |
|                       |                          | Only works when offset.       |
|                       |                          | :::                           |
+-----------------------+--------------------------+-------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [hma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.hma.hma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.hma.hma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    hma(
        close: Series,
        length: Int = None,
        mamode: str = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Hull Moving Average

This indicator, by Alan Hull, attempts to reduce lag compared to
classical moving averages.

Sources

- [Alan Hull](https://alanhull.com/hull-moving-average)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | One of: \'ema\',       |                 |
|                 |                                   | \'sma\', or \'wma\'.   |                 |
|                 |                                   | Default: `"wma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [hwma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.hwma.hwma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.hwma.hwma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    hwma(
        close: Series,
        na: IntFloat = None,
        nb: IntFloat = None,
        nc: IntFloat = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Holt-Winter Moving Average

This indicator uses a three parameter Holt-Winter Moving Average for
smoothing.

Sources

- [rengel8](https://github.com/rengel8) based on a publication for
  MetaTrader 5.
- [mql5](https://www.mql5.com/en/code/20856)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `na`            | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Smoothed series        |                 |
|                 |                                   | parameter (from 0 to   |                 |
|                 |                                   | 1). Default: 0.2       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `nb`            | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Trend parameter (from  |                 |
|                 |                                   | 0 to 1). Default: 0.1  |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `nc`            | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Seasonality parameter  |                 |
|                 |                                   | (from 0 to 1).         |                 |
|                 |                                   | Default: 0.1           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------+-----------------------------------+------------------------+
| Name                  | Type                              | Description            |
+=======================+===================================+========================+
| `Series`              | [`Series`]{title="pandas.Series"} | ::: doc-md-description |
|                       |                                   | 1 column               |
|                       |                                   | :::                    |
+-----------------------+-----------------------------------+------------------------+
:::
:::::
::::::
:::::::
::::::::

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [ichimoku]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.ichimoku.ichimoku "Permanent link"){.headerlink} {#src.pandas_ta.overlap.ichimoku.ichimoku .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    ichimoku(
        high: Series,
        low: Series,
        close: Series,
        tenkan: Int = None,
        kijun: Int = None,
        senkou: Int = None,
        include_chikou: bool = True,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Tuple[DataFrame, DataFrame]
:::

::: {.doc .doc-contents}
Ichimoku Kinkō Hyō

A forecasting model used in Japaese financial markets Pre WWII.

Sources

- [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/ichimoku-ich/)

[Parameters:]{.doc-section-title}

+------------------+-----------------------------------+------------------------+-----------------+
| Name             | Type                              | Description            | Default         |
+==================+===================================+========================+=================+
| `high`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                  |                                   | `high` Series          |                 |
|                  |                                   | :::                    |                 |
+------------------+-----------------------------------+------------------------+-----------------+
| `low`            | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                  |                                   | `low` Series           |                 |
|                  |                                   | :::                    |                 |
+------------------+-----------------------------------+------------------------+-----------------+
| `close`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                  |                                   | `close` Series         |                 |
|                  |                                   | :::                    |                 |
+------------------+-----------------------------------+------------------------+-----------------+
| `tenkan`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                  |                                   | Tenkan period.         |                 |
|                  |                                   | Default: `9`           |                 |
|                  |                                   | :::                    |                 |
+------------------+-----------------------------------+------------------------+-----------------+
| `kijun`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                  |                                   | Kijun period. Default: |                 |
|                  |                                   | `26`                   |                 |
|                  |                                   | :::                    |                 |
+------------------+-----------------------------------+------------------------+-----------------+
| `senkou`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                  |                                   | Senkou period.         |                 |
|                  |                                   | Default: `52`          |                 |
|                  |                                   | :::                    |                 |
+------------------+-----------------------------------+------------------------+-----------------+
| `include_chikou` | [`bool`]{title="bool"}            | ::: doc-md-description | `True`          |
|                  |                                   | Whether to include     |                 |
|                  |                                   | chikou component.      |                 |
|                  |                                   | Default: `True`        |                 |
|                  |                                   | :::                    |                 |
+------------------+-----------------------------------+------------------------+-----------------+
| `offset`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                  |                                   | Post shift. Default:   |                 |
|                  |                                   | `0`                    |                 |
|                  |                                   | :::                    |                 |
+------------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `lookahead`           | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | To avoid data leakage set to |
|                       |                          | `False`.                     |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+----------------------------------------------------------------------------------------------------------------------------------------------------------------------+-----------------------------------+
| Type                                                                                                                                                                 | Description                       |
+======================================================================================================================================================================+===================================+
| [`Tuple`]{title="pandas_ta._typing.Tuple"}`[`[`pd`]{title="pd"}`.`[`DataFrame`]{title="pd.DataFrame"}`, `[`pd`]{title="pd"}`.`[`DataFrame`]{title="pd.DataFrame"}`]` | ::: doc-md-description            |
|                                                                                                                                                                      | - Historical DataFrame, 5 columns |
|                                                                                                                                                                      | - Forward Looking DataFrame, 2    |
|                                                                                                                                                                      |   columns                         |
|                                                                                                                                                                      | :::                               |
+----------------------------------------------------------------------------------------------------------------------------------------------------------------------+-----------------------------------+

Possible Data Leak

Set `lookahead=False` to avoid data leakage. Issue
[#60](https://github.com/twopirllc/pandas-ta/issues/60#).
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [jma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.jma.jma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.jma.jma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    jma(
        close: Series,
        length: IntFloat = None,
        phase: IntFloat = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Jurik Moving Average Average

This indicator, by Mark Jurik, attempts to eliminate noise. It claims to
have extremely low lag, is very smooth and is responsive to gaps.

Sources

- [mql5](https://c.mql5.com/forextsd/forum/164/jurik_1.pdf)
- [prorealcode](https://www.prorealcode.com/prorealtime-indicators/jurik-volatility-bands/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `7`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `phase`         | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Phase value between    |                 |
|                 |                                   | \[-100, 100\].         |                 |
|                 |                                   | Default: `0`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [kama]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.kama.kama "Permanent link"){.headerlink} {#src.pandas_ta.overlap.kama.kama .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    kama(
        close: Series,
        length: Int = None,
        fast: Int = None,
        slow: Int = None,
        mamode: str = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Kaufman\'s Adaptive Moving Average

This indicator, by Perry Kaufman, attempts to find the overall trend by
adapting to volatility.

Sources

- [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:kaufman_s_adaptive_moving_average)
- [tradingview](https://www.tradingview.com/script/wZGOIz9r-REPOST-Indicators-3-Different-Adaptive-Moving-Averages/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Fast MA period.        |                 |
|                 |                                   | Default: `2`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Slow MA period.        |                 |
|                 |                                   | Default: `30`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"sma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [linreg]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.linreg.linreg "Permanent link"){.headerlink} {#src.pandas_ta.overlap.linreg.linreg .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    linreg(
        close: Series,
        length: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Linear Regression Moving Average

This indicator is a simplified version of Standard Linear Regression. It
is one variable rolling regression whereas a Standard Linear Regression
is between two or more variables.

Sources

- [TA Lib](https://ta-lib.org)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `angle`               | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | Returns the slope angle in   |
|                       |                          | radians. Default: `False`    |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `degrees`             | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | Return the slope angle in    |
|                       |                          | degrees. Default: `False`    |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `intercept`           | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | Return the intercept.        |
|                       |                          | Default: `False`             |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `r`                   | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | Return the \'r\'             |
|                       |                          | correlation. Default:        |
|                       |                          | `False`                      |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `slope`               | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | Return the slope. Default:   |
|                       |                          | `False`                      |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `tsf`                 | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | Return the Time Series       |
|                       |                          | Forecast value. Default:     |
|                       |                          | `False`                      |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Warning

TA-Lib Correlation: `np.float64(0.9985638477660118)`

Tip

Corrective contributions welcome!
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [mama]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.mama.mama "Permanent link"){.headerlink} {#src.pandas_ta.overlap.mama.mama .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    mama(
        close: Series,
        fastlimit: IntFloat = None,
        slowlimit: IntFloat = None,
        prenan: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
MESA Adaptive Moving Average

This indicator, aka the Mother of All Moving Averages by John Ehlers,
attempts to adapt to volatility by using a Hilbert Transform
Discriminator

Sources

- [Ehlers\'s Mother of Adaptive Moving
  Averages](http://traders.com/documentation/feedbk_docs/2014/01/traderstips.html)
- [tradingview](https://www.tradingview.com/script/foQxLbU3-Ehlers-MESA-Adaptive-Moving-Average-LazyBear/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fastlimit`     | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Fast limit. Default:   |                 |
|                 |                                   | `0.5`                  |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slowlimit`     | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Slow limit. Default:   |                 |
|                 |                                   | `0.05`                 |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `prenan`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Prenans to apply.      |                 |
|                 |                                   | TV-LB `3`, Ehler\'s    |                 |
|                 |                                   | `6`, TA Lib `32`.      |                 |
|                 |                                   | Default: `3`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Tip

**FAMA** also included
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [mcgd]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.mcgd.mcgd "Permanent link"){.headerlink} {#src.pandas_ta.overlap.mcgd.mcgd .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    mcgd(
        close: Series,
        length: Int = None,
        c: IntFloat = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
McGinley Dynamic Indicator

This indicator, by John R. McGinley, is not a moving average but a
differential smoothing technique.

Sources

- John R. McGinley, a Certified Market Technician (CMT) and former
  editor of the Market Technicians Association\'s Journal of Technical
  Analysis.
- [investopedia](https://www.investopedia.com/articles/forex/09/mcginley-dynamic-indicator.asp)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `c`             | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Denominator            |                 |
|                 |                                   | multiplier. Default:   |                 |
|                 |                                   | `1`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

Sometimes `c` is set to `0.6`.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [midpoint]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.midpoint.midpoint "Permanent link"){.headerlink} {#src.pandas_ta.overlap.midpoint.midpoint .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    midpoint(
        close: Series,
        length: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Midpoint

The Midpoint is the average of the rolling high and low of period
length.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `2`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [midprice]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.midprice.midprice "Permanent link"){.headerlink} {#src.pandas_ta.overlap.midprice.midprice .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    midprice(
        high: Series,
        low: Series,
        length: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Midprice

The Midprice is the average of the rolling high and low of period
length.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `2`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [ohlc4]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.ohlc4.ohlc4 "Permanent link"){.headerlink} {#src.pandas_ta.overlap.ohlc4.ohlc4 .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    ohlc4(
        open_: Series,
        high: Series,
        low: Series,
        close: Series,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
OHLC4

OHLC4 is the average of open, high, low and close.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `open_`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `open` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+-------------------------------+
| Name                  | Type                     | Description                   |
+=======================+==========================+===============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description        |
|                       |                          | `pd.DataFrame.fillna(value)`. |
|                       |                          | Only works when offset.       |
|                       |                          | :::                           |
+-----------------------+--------------------------+-------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [pwma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.pwma.pwma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.pwma.pwma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    pwma(
        close: Series,
        length: Int = None,
        asc: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Pascal\'s Weighted Moving Average

This indicator, by Kevin Johnson, creates a weighted moving average
using Pascal\'s Triangle.

Sources

- Kevin Johnson

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `asc`           | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Ascending. Default:    |                 |
|                 |                                   | `True`                 |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`DataFrame`]{title="DataFrame"}  | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [rma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.rma.rma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.rma.rma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    rma(
        close: Series,
        length: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
wildeR\'s Moving Average

This indicator, by Wilder, is simply an EMA where *alpha* is the
recipical of its *length*.

Sources

- [incrediblecharts](https://www.incrediblecharts.com/indicators/wilder_moving_average.php)
- [thinkorswim](https://tlc.thinkorswim.com/center/reference/Tech-Indicators/studies-library/V-Z/WildersSmoothing)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [sinwma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.sinwma.sinwma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.sinwma.sinwma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    sinwma(
        close: Series,
        length: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Sine Weighted Moving Average

This indicator is a weighted average using sine cycles where the central
values have greater weight.

Source

- [Everget](https://www.tradingview.com/u/everget/)
- [tradingview](https://www.tradingview.com/script/6MWFvnPO-Sine-Weighted-Moving-Average/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [sma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.sma.sma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.sma.sma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    sma(
        close: Series,
        length: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Simple Moving Average

This indicator is the the textbook moving average, a rolling sum of
values divided by the window period (or length).

Sources

- [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/simple-moving-average-sma/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `adjust`              | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | Adjust the values. Default:  |
|                       |                          | `True`                       |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `presma`              | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | If True, uses SMA for        |
|                       |                          | initial value.               |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [smma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.smma.smma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.smma.smma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    smma(
        close: Series,
        length: Int = None,
        mamode: str = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
SMoothed Moving Average

This indicator attempts to confirm trends and identify support and
resistance areas. It tries to reduce noise in contrast to reducing lag.

Sources

- [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=173&Name=Moving_Average_-_Smoothed)
- [tradingview](https://www.tradingview.com/scripts/smma/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"sma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

A core component of Bill Williams Alligator indicator.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [ssf]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.ssf.ssf "Permanent link"){.headerlink} {#src.pandas_ta.overlap.ssf.ssf .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    ssf(
        close: Series,
        length: Int = None,
        everget: bool = None,
        pi: IntFloat = None,
        sqrt2: IntFloat = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Ehlers\'s Super Smoother Filter

This indicator, by John F. Ehlers\'s © 2013, is a (Recursive) Digital
Filter that attempts to reduce lag and remove aliases. This version has
two poles.

Sources

- [mql5](https://www.mql5.com/en/code/588)
- [traders.com](http://traders.com/documentation/feedbk_docs/2014/01/traderstips.html)
- [tradingview](https://www.tradingview.com/script/VdJy0yBJ-Ehlers-Super-Smoother-Filter/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `20`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `everget`       | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Everget\'s             |                 |
|                 |                                   | implementation of ssf  |                 |
|                 |                                   | that uses pi instead   |                 |
|                 |                                   | of 180 for the b       |                 |
|                 |                                   | factor of ssf.         |                 |
|                 |                                   | Default: `False`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `pi`            | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | The default is         |                 |
|                 |                                   | Ehlers\'s truncated    |                 |
|                 |                                   | value: `3.14159`.      |                 |
|                 |                                   | Default: `3.14159`     |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `sqrt2`         | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | The default is         |                 |
|                 |                                   | Ehlers\'s truncated    |                 |
|                 |                                   | value: `1.414`.        |                 |
|                 |                                   | Default: `1.414`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

- Everget\'s calculation on TradingView: `pi=np.pi`, `sqrt2=np.sqrt(2)`

Danger

Possible Data Leak
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [ssf3]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.ssf3.ssf3 "Permanent link"){.headerlink} {#src.pandas_ta.overlap.ssf3.ssf3 .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    ssf3(
        close: Series,
        length: Int = None,
        pi: IntFloat = None,
        sqrt3: IntFloat = None,
        offset: Int = None,
        **kwargs: DictLike,
    )
:::

::: {.doc .doc-contents}
Ehlers\'s 3 Pole Super Smoother Filter

This indicator, by John F. Ehlers\'s © 2013, is a (Recursive) Digital
Filter that attempts to reduce lag and remove aliases. This version has
two poles.

Sources

- [mql5](https://www.mql5.com/en/code/589)
- [tradingview](https://www.tradingview.com/script/VdJy0yBJ-Ehlers-Super-Smoother-Filter/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `20`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `pi`            | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | The value of `PI`. The |                 |
|                 |                                   | default is Ehler\'s    |                 |
|                 |                                   | truncated value:       |                 |
|                 |                                   | `3.14159`. Default:    |                 |
|                 |                                   | `3.14159`              |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `sqrt3`         | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | The value of `sqrt(3)` |                 |
|                 |                                   | to use. The default is |                 |
|                 |                                   | Ehler\'s truncated     |                 |
|                 |                                   | value: `1.732`.        |                 |
|                 |                                   | Default: `1.732`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

- Everget\'s calculation on TradingView: `pi=np.pi`, `sqrt2=np.sqrt(2)`
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [supertrend]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.supertrend.supertrend "Permanent link"){.headerlink} {#src.pandas_ta.overlap.supertrend.supertrend .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    supertrend(
        high: Series,
        low: Series,
        close: Series,
        length: Int = None,
        atr_length: Int = None,
        multiplier: IntFloat = None,
        atr_mamode: str = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Supertrend

This indicator attempts to identify trend direction as well as support
and resistance levels.

Sources

- [freebsensetips](http://www.freebsensetips.com/blog/detail/7/What-is-supertrend-indicator-its-calculation)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `7`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `atr_length`    | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | ATR period. Default:   |                 |
|                 |                                   | `length`               |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `multiplier`    | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Coefficient for upper  |                 |
|                 |                                   | and lower band         |                 |
|                 |                                   | distance to midrange.  |                 |
|                 |                                   | Default: `3.0`         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `atr_mamode`    | `str) `                           | ::: doc-md-description | `None`          |
|                 |                                   | MA type to be used for |                 |
|                 |                                   | ATR calculation. See   |                 |
|                 |                                   | `help(ta.ma)`.         |                 |
|                 |                                   | Default: `"rma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 4 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [swma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.swma.swma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.swma.swma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    swma(
        close: Series,
        length: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Symmetric Weighted Moving Average

This indicator is based on a Symmetric Weighted Moving Average where
weights are based on a symmetric triangle.

Source

- [tradingview](https://www.tradingview.com/study-script-reference/#fun_swma)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

- `n=3` -\> `[1, 2, 1]`
- `n=4` -\> `[1, 2, 2, 1]`
- etc\...
:::
:::::
::::::
:::::::
::::::::

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [t3]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.t3.t3 "Permanent link"){.headerlink} {#src.pandas_ta.overlap.t3.t3 .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    t3(
        close: Series,
        length: Int = None,
        a: IntFloat = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
T3

This indicator, by Tim Tillson, attempts to be smoother and more
responsive relative to other moving averages.

Sources

- [binarytribune](http://www.binarytribune.com/forex-trading-indicators/t3-moving-average-indicator/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `a`             | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | The a factor, 0 \< a   |                 |
|                 |                                   | \< 1. Default: `0.7`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `adjust`              | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `presma`              | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | If True, uses SMA for        |
|                       |                          | initial value.               |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Warning

TA-Lib Correlation: `np.float64(0.9999994265973177)`

Tip

Corrective contributions welcome!
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [tema]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.tema.tema "Permanent link"){.headerlink} {#src.pandas_ta.overlap.tema.tema .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    tema(
        close: Series,
        length: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Triple Exponential Moving Average

This indicator attempts to be less laggy than the EMA.

Sources

- [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/triple-exponential-moving-average-tema/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `adjust`              | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `presma`              | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | If True, uses SMA for        |
|                       |                          | initial value.               |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Warning

TA-Lib Correlation: `np.float64(0.9999355450605516)`

Tip

Corrective contributions welcome!
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [trima]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.trima.trima "Permanent link"){.headerlink} {#src.pandas_ta.overlap.trima.trima .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    trima(
        close: Series,
        length: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Triangular Moving Average

This indicator is a weighted moving average where the shape of the
weights are triangular with the greatest weight is in the middle of the
period.

Sources

- [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/triangular-moving-average-trima/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `adjust`              | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

tma = sma(sma(src, ceil(length / 2)), floor(length / 2) + 1) \#
Tradingview trima = sma(sma(x, n), n) \# Tradingview

Warning

TA-Lib Correlation: `np.float64(0.9991752493891967)`

Tip

Corrective contributions welcome!
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [vidya]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.vidya.vidya "Permanent link"){.headerlink} {#src.pandas_ta.overlap.vidya.vidya .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    vidya(
        close: Series,
        length: Int = None,
        talib: bool = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Variable Index Dynamic Average

This indicator, by Tushar Chande, is similar to an EMA but it has a
dynamically adjusted lookback period dependent based on CMO.

Sources

- [perfecttrendsystem](https://www.perfecttrendsystem.com/blog_mt4_2/en/vidya-indicator-for-mt4)
- [tradingview](https://www.tradingview.com/script/hdrf0fXV-Variable-Index-Dynamic-Average-VIDYA/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

Sometimes used as a moving average or a trend identifier.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [wcp]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.wcp.wcp "Permanent link"){.headerlink} {#src.pandas_ta.overlap.wcp.wcp .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    wcp(
        high: Series,
        low: Series,
        close: Series,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Weighted Closing Price

This indicator is a weighted value of: high, low and twice the close.

Sources

- [fmlabs](https://www.fmlabs.com/reference/default.htm?url=WeightedCloses.htm)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [wma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.wma.wma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.wma.wma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    wma(
        close: Series,
        length: Int = None,
        asc: bool = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Weighted Moving Average

This indicator is a Moving Average where the weights are linearly
increasing and the most recent data has the heaviest weight.

Sources

- [wikipedia](https://en.wikipedia.org/wiki/Moving_average#Weighted_moving_average)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `asc`           | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Recent values weigh    |                 |
|                 |                                   | more. Default: `True`  |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [zlma]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.overlap.zlma.zlma "Permanent link"){.headerlink} {#src.pandas_ta.overlap.zlma.zlma .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    zlma(
        close: Series,
        length: Int = None,
        mamode: str = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Zero Lag Moving Average

This indicator, by John Ehlers and Ric Way, attempts to eliminate the
lag often introduced in other moving averages.

Sources

- [wikipedia](https://en.wikipedia.org/wiki/Zero_lag_exponential_moving_average)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | One of: \"dema\",      |                 |
|                 |                                   | \"ema\", \"fwma\",     |                 |
|                 |                                   | \"hma\", \"linreg\",   |                 |
|                 |                                   | \"midpoint\",          |                 |
|                 |                                   | \"pwma\", \"rma\",     |                 |
|                 |                                   | \"sinwma\", \"ssf\",   |                 |
|                 |                                   | \"swma\", \"t3\",      |                 |
|                 |                                   | \"tema\", \"trima\",   |                 |
|                 |                                   | \"vidya\", or \"wma\". |                 |
|                 |                                   | Default: `"ema"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

::: {.admonition .danger}
IMPORTANT

**Thanks** to all those that have sponsored and dontated to the library
in the past! Your support has been greatly appreciated!
![🙏](../../assets/external/cdn.jsdelivr.net/gh/jdecked/twemoji@15.1.0/assets/svg/1f64f.svg ":pray:"){.twemoji}

Only **Installation Bugs/Issues** will addressed for releases between
versions *0.4.25b* and *0.4.66b*. Releases beyond version *0.4.66b* will
**only** be released after **significant** donations, sponsorships or
yearly subscriptions have been received via [Buy Me a
Coffee](https://www.buymeacoffee.com/twopirllc){style="color: #ff9200;"}.

Support Tiers coming soon!
:::

\

\

[![\"Buy Me A
Coffee\"](../../assets/external/www.buymeacoffee.com/assets/img/custom_images/orange_img.png){align="left"}](https://www.buymeacoffee.com/twopirllc)\
\

[![ko-fi](../../assets/external/ko-fi.com/img/githubbutton_sm.svg){align="left"}](https://ko-fi.com/K3K4ZRH9D)
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::

![](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdib3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEzIDIwaC0yVjhsLTUuNSA1LjUtMS40Mi0xLjQyTDEyIDQuMTZsNy45MiA3LjkyLTEuNDIgMS40MkwxMyA4eiIgLz48L3N2Zz4=)
Back to top
:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
