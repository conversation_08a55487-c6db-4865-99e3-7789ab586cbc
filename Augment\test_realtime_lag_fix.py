"""
Test Real-time Lag Fix

This script tests the real-time detection fix to ensure signals are detected
immediately when band touch occurs, without the 1-minute lag.
"""

import sys
import os
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_lag_fix():
    """Test the lag fix across critical time periods"""
    
    # Import functions
    from shared_api_manager import get_api
    from enhanced_nadarya_watson_signal import check_vander_enhanced
    from shared_sideways_signal_helper import check_sideways
    
    # Test parameters
    ticker = 'BATAINDIA'
    date = '24-06-2025'
    exchange = 'NSE'
    
    # Get token ID
    api = get_api()
    search_result = api.searchscrip(exchange='NSE', searchtext=ticker + '-EQ')
    if search_result and 'values' in search_result and search_result['values']:
        tokenid = search_result['values'][0]['token']
        logger.info(f"📊 Found token ID: {tokenid} for {ticker}")
    else:
        logger.error(f"❌ Could not find token ID for {ticker}")
        return
    
    # Test periods around critical moments
    test_periods = [
        # Around 12:49 upper band touch
        {'start': '09:15', 'end': '12:48', 'description': '12:48 - Before band touch'},
        {'start': '09:15', 'end': '12:49', 'description': '12:49 - Exact band touch moment'},
        {'start': '09:15', 'end': '12:50', 'description': '12:50 - After band touch'},
        {'start': '09:15', 'end': '12:51', 'description': '12:51 - Continuation'},
        
        # Around 12:29-12:31 lower band touches
        {'start': '09:15', 'end': '12:29', 'description': '12:29 - Lower band touch'},
        {'start': '09:15', 'end': '12:30', 'description': '12:30 - Lower band continuation'},
        {'start': '09:15', 'end': '12:31', 'description': '12:31 - Lower band continuation'},
    ]
    
    print("\n" + "="*140)
    print("📊 REAL-TIME LAG FIX TEST RESULTS")
    print("="*140)
    
    print(f"\n{'Period':<15} | {'Sideways':<8} | {'Original':<8} | {'Real-time':<9} | {'Enhanced':<8} | {'Lag Fixed':<10} | {'Momentum':<9} | {'Description':<25}")
    print("-" * 140)
    
    results = []
    
    for period in test_periods:
        try:
            start_time = period['start']
            end_time = period['end']
            description = period['description']
            
            # Test sideways
            is_sideways, sideways_text = check_sideways(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date,
                starttime_input=start_time,
                endtime_input=end_time
            )
            
            # Test original (1-minute lag, no momentum)
            is_vander_orig, vander_text_orig = check_vander_enhanced(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date,
                starttime_input=start_time,
                endtime_input=end_time,
                enable_momentum_validation=False,
                enable_realtime_detection=False
            )
            
            # Test real-time only (no lag, no momentum)
            is_vander_rt, vander_text_rt = check_vander_enhanced(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date,
                starttime_input=start_time,
                endtime_input=end_time,
                enable_momentum_validation=False,
                enable_realtime_detection=True
            )
            
            # Test enhanced (no lag, with momentum)
            is_vander_enh, vander_text_enh = check_vander_enhanced(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date,
                starttime_input=start_time,
                endtime_input=end_time,
                enable_momentum_validation=True,
                enable_realtime_detection=True
            )
            
            # Determine effects
            lag_fixed = is_vander_orig != is_vander_rt
            momentum_effect = is_vander_rt != is_vander_enh
            
            # Final signals (Ver4 logic: both sideways and nadarya must be true)
            final_orig = is_sideways and is_vander_orig
            final_rt = is_sideways and is_vander_rt
            final_enh = is_sideways and is_vander_enh
            
            result = {
                'period': end_time,
                'sideways': is_sideways,
                'original': is_vander_orig,
                'realtime': is_vander_rt,
                'enhanced': is_vander_enh,
                'lag_fixed': lag_fixed,
                'momentum_effect': momentum_effect,
                'description': description,
                'final_orig': final_orig,
                'final_rt': final_rt,
                'final_enh': final_enh,
                'vander_text_orig': vander_text_orig,
                'vander_text_rt': vander_text_rt,
                'vander_text_enh': vander_text_enh
            }
            
            results.append(result)
            
            # Print summary row
            lag_icon = "✅" if lag_fixed else "⚪"
            momentum_icon = "✅" if momentum_effect else "⚪"
            
            print(f"{result['period']:<15} | {str(result['sideways']):<8} | {str(result['original']):<8} | {str(result['realtime']):<9} | {str(result['enhanced']):<8} | {lag_icon:<10} | {momentum_icon:<9} | {result['description']:<25}")
            
        except Exception as e:
            logger.error(f"❌ Error testing period {period}: {str(e)}")
    
    # Print detailed analysis
    print("\n" + "="*140)
    print("📋 DETAILED ANALYSIS")
    print("="*140)
    
    for i, result in enumerate(results, 1):
        print(f"\n{i}. {result['description']}")
        print(f"   Sideways: {result['sideways']}")
        print(f"   Original (lag): {result['original']} - {result['vander_text_orig']}")
        print(f"   Real-time: {result['realtime']} - {result['vander_text_rt']}")
        print(f"   Enhanced: {result['enhanced']} - {result['vander_text_enh']}")
        
        if result['lag_fixed']:
            print(f"   🎯 LAG FIXED: Real-time detection found signal that original missed!")
        
        if result['momentum_effect']:
            effect = "filtered false positive" if result['realtime'] and not result['enhanced'] else "enhanced detection"
            print(f"   🎯 MOMENTUM EFFECT: {effect}")
        
        print(f"   Final Signals - Original: {result['final_orig']}, Real-time: {result['final_rt']}, Enhanced: {result['final_enh']}")
    
    # Summary statistics
    total_tests = len(results)
    lag_fixes = sum(1 for r in results if r['lag_fixed'])
    momentum_effects = sum(1 for r in results if r['momentum_effect'])
    
    print(f"\n" + "="*140)
    print("📊 SUMMARY STATISTICS")
    print("="*140)
    print(f"Total test periods: {total_tests}")
    print(f"Lag fixes detected: {lag_fixes}")
    print(f"Momentum effects detected: {momentum_effects}")
    
    if lag_fixes > 0:
        print(f"\n🎯 SUCCESS: Real-time detection fixed {lag_fixes} lagged signals!")
    else:
        print(f"\n📊 INFO: No lag issues detected in this test set")
    
    if momentum_effects > 0:
        print(f"🎯 SUCCESS: Momentum validation affected {momentum_effects} signals!")
    
    return results

def test_specific_band_touch_moments():
    """Test specific moments when band touch should occur"""
    
    print("\n" + "="*80)
    print("🧪 TESTING SPECIFIC BAND TOUCH MOMENTS")
    print("="*80)
    
    from enhanced_nadarya_watson_signal import check_vander_enhanced
    from shared_api_manager import get_api
    
    # Get token ID
    api = get_api()
    search_result = api.searchscrip(exchange='NSE', searchtext='BATAINDIA-EQ')
    tokenid = search_result['values'][0]['token']
    
    # Test exact moments
    test_moments = [
        {'end': '12:49', 'expected': 'Upper band touch at 12:49'},
        {'end': '12:29', 'expected': 'Lower band touch at 12:29'},
        {'end': '12:30', 'expected': 'Lower band touch at 12:30'},
        {'end': '12:31', 'expected': 'Lower band touch at 12:31'},
    ]
    
    for moment in test_moments:
        print(f"\n🔍 Testing {moment['end']} - {moment['expected']}")
        
        # Test with real-time detection
        is_signal, signal_text = check_vander_enhanced(
            tokenid=tokenid,
            exchange='NSE',
            date_input='24-06-2025',
            starttime_input='09:15',
            endtime_input=moment['end'],
            enable_momentum_validation=False,
            enable_realtime_detection=True
        )
        
        print(f"   Real-time result: {is_signal}")
        print(f"   Signal text: {signal_text}")
        
        if is_signal:
            print(f"   ✅ SUCCESS: Band touch detected at {moment['end']}")
        else:
            print(f"   ❌ ISSUE: No band touch detected at {moment['end']}")

def main():
    """Main execution function"""
    logger.info("🚀 Starting real-time lag fix tests...")
    
    # Test lag fix across periods
    test_lag_fix()
    
    # Test specific band touch moments
    test_specific_band_touch_moments()
    
    logger.info("🎉 Real-time lag fix tests completed!")

if __name__ == "__main__":
    main()
