"""
Search MCX Tickers

This script helps find the correct MCX ticker names for commodities.
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def search_mcx_tickers():
    """Search for available MCX tickers"""
    print("🔍 Searching for MCX Tickers...")
    
    try:
        from shared_api_manager import get_api
        
        api = get_api()
        
        # Common MCX commodities to search
        commodities = [
            'SILVER',
            'SILVERMIC',
            'SILVERMINI',
            'GOLD',
            'GOLDPETAL',
            'GOLDGUINEA',
            'NATURALGAS',
            'CRUDEOIL',
            'COPPER',
            'ZINC',
            'ALUMINIUM',
            'LEAD',
            'NICKEL'
        ]
        
        print(f"📊 Searching for {len(commodities)} commodities on MCX...")
        print("="*70)
        
        found_tickers = []
        
        for commodity in commodities:
            try:
                print(f"\n🔍 Searching for: {commodity}")
                ret = api.searchscrip(exchange='MCX', searchtext=commodity)
                
                if ret and ret.get('stat') == 'Ok' and ret.get('values'):
                    print(f"✅ Found {len(ret['values'])} results:")
                    
                    for item in ret['values']:
                        token = item.get('token', 'N/A')
                        tsym = item.get('tsym', 'N/A')
                        instname = item.get('instname', 'N/A')
                        
                        print(f"   📈 {tsym} (Token: {token}, Type: {instname})")
                        found_tickers.append({
                            'commodity': commodity,
                            'tsym': tsym,
                            'token': token,
                            'instname': instname
                        })
                else:
                    print(f"❌ No results for {commodity}")
                    
            except Exception as e:
                print(f"❌ Error searching {commodity}: {str(e)}")
        
        # Summary
        print(f"\n" + "="*70)
        print("📊 SUMMARY OF AVAILABLE MCX TICKERS")
        print("="*70)
        
        if found_tickers:
            print(f"✅ Found {len(found_tickers)} MCX instruments:")
            
            # Group by commodity
            by_commodity = {}
            for ticker in found_tickers:
                commodity = ticker['commodity']
                if commodity not in by_commodity:
                    by_commodity[commodity] = []
                by_commodity[commodity].append(ticker)
            
            for commodity, tickers in by_commodity.items():
                print(f"\n🏷️ {commodity}:")
                for ticker in tickers:
                    print(f"   {ticker['tsym']} (Token: {ticker['token']})")
            
            # Suggest current month tickers
            print(f"\n💡 SUGGESTED CURRENT MONTH TICKERS:")
            current_month_keywords = ['25JUN', '26JUN', '29AUG', '30AUG', '25JUL']
            
            for keyword in current_month_keywords:
                matching = [t for t in found_tickers if keyword in t['tsym']]
                if matching:
                    print(f"\n📅 {keyword} expiry:")
                    for ticker in matching:
                        print(f"   {ticker['tsym']}")
        else:
            print("❌ No MCX tickers found")
        
        return found_tickers
        
    except Exception as e:
        print(f"❌ MCX ticker search failed: {str(e)}")
        return []

def test_specific_tickers():
    """Test specific ticker variations"""
    print(f"\n🧪 Testing Specific Ticker Variations...")
    
    try:
        from shared_api_manager import get_api
        
        api = get_api()
        
        # Test variations of SILVER
        silver_variations = [
            'SILVERMIC29AUG25',
            'SILVERMIC30AUG25',
            'SILVERMIC25JUN25',
            'SILVERMIC26JUN25',
            'SILVER29AUG25',
            'SILVER30AUG25',
            'SILVERMINI29AUG25'
        ]
        
        print("🥈 Testing SILVER variations:")
        for ticker in silver_variations:
            try:
                ret = api.searchscrip(exchange='MCX', searchtext=ticker)
                if ret and ret.get('stat') == 'Ok' and ret.get('values'):
                    print(f"✅ {ticker} - FOUND")
                    for item in ret['values']:
                        print(f"   {item.get('tsym')} (Token: {item.get('token')})")
                else:
                    print(f"❌ {ticker} - NOT FOUND")
            except Exception as e:
                print(f"❌ {ticker} - ERROR: {str(e)}")
        
    except Exception as e:
        print(f"❌ Specific ticker test failed: {str(e)}")

def main():
    """Main function"""
    print("🚀 MCX Ticker Search Tool")
    print("="*50)
    
    # Search for available tickers
    found_tickers = search_mcx_tickers()
    
    # Test specific variations
    test_specific_tickers()
    
    print(f"\n🎯 RECOMMENDATIONS:")
    print("1. Use the exact ticker names found above")
    print("2. Check expiry dates (25JUN, 26JUN, 29AUG, etc.)")
    print("3. SILVERMIC is likely the correct format, not SLVERMIC")
    print("4. Try current month expiries for active trading")

if __name__ == "__main__":
    main()
