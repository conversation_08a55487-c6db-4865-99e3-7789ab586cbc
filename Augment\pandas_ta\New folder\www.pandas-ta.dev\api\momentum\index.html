<!doctype html><html lang=en class=no-js> <head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=description content="Pandas TA list of Momentum Indicators"><meta name=author content="Pandas TA"><link href="index.html" rel="canonical"><link href="../cycle/index.html" rel=prev><link href="../overlap/index.html" rel=next><link rel=icon href="../../assets/images/favicon.ico"><meta name=generator content="mkdocs-1.6.1, mkdocs-material-9.6.14"><title>Momentum - Pandas TA</title><link rel=stylesheet href="../../assets/stylesheets/main.342714a4.min.css"><link rel=stylesheet href="../../assets/stylesheets/palette.06af60db.min.css"><style>:root{--md-admonition-icon--note:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M14%2010h5.5L14%204.5zM5%203h10l6%206v10a2%202%200%200%201-2%202H5a2%202%200%200%201-2-2V5c0-1.11.89-2%202-2m0%209v2h14v-2zm0%204v2h9v-2z%22/%3E%3C/svg%3E');--md-admonition-icon--abstract:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M18%2022a2%202%200%200%200%202-2V4a2%202%200%200%200-2-2h-6v7L9.5%207.5%207%209V2H6a2%202%200%200%200-2%202v16a2%202%200%200%200%202%202z%22/%3E%3C/svg%3E');--md-admonition-icon--info:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M11%209h2V7h-2m1%2013c-4.41%200-8-3.59-8-8s3.59-8%208-8%208%203.59%208%208-3.59%208-8%208m0-18A10%2010%200%200%200%202%2012a10%2010%200%200%200%2010%2010%2010%2010%200%200%200%2010-10A10%2010%200%200%200%2012%202m-1%2015h2v-6h-2z%22/%3E%3C/svg%3E');--md-admonition-icon--tip:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M19%202.8v6.4c0%20.3-.3.6-.6.5%200%200-3.9-2.6-8.4-2.6v2.4h.2c.5%200%201%20.2%201.2.6l1.3%201.8c.*******.3.7v8c0%20.7-.7%201.4-1.5%201.4h-5c-.8%200-1.5-.6-1.5-1.4v-8c0-.3.1-.5.3-.7l1.3-1.8q.45-.6%201.2-.6H8V8c-.4.5-1%20.8-1.6.8C5.1%208.8%204%207.5%204%206s1.1-2.8%202.4-2.8c.6.1%201.2.4%201.6.8V2.6h2V5c4.5%200%208.4-2.6%208.4-2.6.3-.*******.4%22/%3E%3C/svg%3E');--md-admonition-icon--success:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M21%207%209%2019l-5.5-5.5%201.41-1.41L9%2016.17%2019.59%205.59z%22/%3E%3C/svg%3E');--md-admonition-icon--question:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M11%2018h2v-2h-2zm1-12a4%204%200%200%200-4%204h2a2%202%200%200%201%202-2%202%202%200%200%201%202%202c0%202-3%201.75-3%205h2c0-2.25%203-2.5%203-5a4%204%200%200%200-4-4M5%203h14a2%202%200%200%201%202%202v14a2%202%200%200%201-2%202H5a2%202%200%200%201-2-2V5a2%202%200%200%201%202-2%22/%3E%3C/svg%3E');--md-admonition-icon--warning:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M13%2014h-2V9h2m0%209h-2v-2h2M1%2021h22L12%202z%22/%3E%3C/svg%3E');--md-admonition-icon--failure:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M11.25%206a3.25%203.25%200%200%201%203.25-3.25A3.25%203.25%200%200%201%2017.75%206c0%20.42.33.75.75.75s.75-.33.75-.75v-.75h1.5V6a2.25%202.25%200%200%201-2.25%202.25A2.25%202.25%200%200%201%2016.25%206a1.75%201.75%200%200%200-1.75-1.75A1.75%201.75%200%200%200%2012.75%206H14v1.29c2.89.86%205%203.54%205%206.71a7%207%200%200%201-7%207%207%207%200%200%201-7-7c0-3.17%202.11-5.85%205-6.71V6zM22%206h2v1h-2zm-3-2V2h1v2zm1.91.38%201.42-1.42.71.71-1.42%201.42z%22/%3E%3C/svg%3E');--md-admonition-icon--danger:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M13%2013h-2V7h2m-2%208h2v2h-2m4.73-14H8.27L3%208.27v7.46L8.27%2021h7.46L21%2015.73V8.27z%22/%3E%3C/svg%3E');--md-admonition-icon--bug:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M14%2012h-4v-2h4m0%206h-4v-2h4m6-6h-2.81a6%206%200%200%200-1.82-1.96L17%204.41%2015.59%203l-2.17%202.17a6%206%200%200%200-2.83%200L8.41%203%207%204.41l1.62%201.63C7.88%206.55%207.26%207.22%206.81%208H4v2h2.09c-.05.33-.09.66-.09%201v1H4v2h2v1c0%20.34.04.67.09%201H4v2h2.81c1.04%201.79%202.97%203%205.19%203s4.15-1.21%205.19-3H20v-2h-2.09c.05-.33.09-.66.09-1v-1h2v-2h-2v-1c0-.34-.04-.67-.09-1H20z%22/%3E%3C/svg%3E');--md-admonition-icon--example:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M7%202v2h1v14a4%204%200%200%200%204%204%204%204%200%200%200%204-4V4h1V2zm4%2014c-.6%200-1-.4-1-1s.4-1%201-1%201%20.4%201%201-.4%201-1%201m2-4c-.6%200-1-.4-1-1s.4-1%201-1%201%20.4%201%201-.4%201-1%201m1-5h-4V4h4z%22/%3E%3C/svg%3E');--md-admonition-icon--quote:url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22m10%207-2%204h3v6H5v-6l2-4zm8%200-2%204h3v6h-6v-6l2-4z%22/%3E%3C/svg%3E');}</style><link rel="stylesheet" href="../../assets/external/fonts.googleapis.com/css.90ef4c13.css"><style>:root{--md-text-font:"Lato";--md-code-font:"Roboto Mono"}</style><link rel=stylesheet href="../../assets/_mkdocstrings.css"><link rel=stylesheet href="../../assets/css/pta.css"><script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script><script id=__analytics>function __md_analytics(){function e(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],e("js",new Date),e("config","G-C7C3376JJK"),document.addEventListener("DOMContentLoaded",(function(){document.forms.search&&document.forms.search.query.addEventListener("blur",(function(){this.value&&e("event","search",{search_term:this.value})}));document$.subscribe((function(){var t=document.forms.feedback;if(void 0!==t)for(var a of t.querySelectorAll("[type=submit]"))a.addEventListener("click",(function(a){a.preventDefault();var n=document.location.pathname,d=this.getAttribute("data-md-value");e("event","feedback",{page:n,data:d}),t.firstElementChild.disabled=!0;var r=t.querySelector(".md-feedback__note [data-md-value='"+d+"']");r&&(r.hidden=!1)})),t.hidden=!1})),location$.subscribe((function(t){e("config","G-C7C3376JJK",{page_path:t.pathname})}))}));var t=document.createElement("script");t.async=!0,t.src="https://www.googletagmanager.com/gtag/js?id=G-C7C3376JJK",document.getElementById("__analytics").insertAdjacentElement("afterEnd",t)}</script><script>"undefined"!=typeof __md_analytics&&__md_analytics()</script></head> <body dir=ltr data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=indigo> <input class=md-toggle data-md-toggle=drawer type=checkbox id=__drawer autocomplete=off> <input class=md-toggle data-md-toggle=search type=checkbox id=__search autocomplete=off> <label class=md-overlay for=__drawer></label> <div data-md-component=skip> <a href="index.html#src.pandas_ta.momentum.ao.ao" class=md-skip> Skip to content </a> </div> <div data-md-component=announce> </div> <header class=md-header data-md-component=header> <nav class="md-header__inner md-grid" aria-label=Header> <a href="../../index.html" title="Pandas TA" class="md-header__button md-logo" aria-label="Pandas TA" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/></svg> </a> <label class="md-header__button md-icon" for=__drawer> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg> </label> <div class=md-header__title data-md-component=header-title> <div class=md-header__ellipsis> <div class=md-header__topic> <span class=md-ellipsis> Pandas TA </span> </div> <div class=md-header__topic data-md-component=header-topic> <span class=md-ellipsis> Momentum </span> </div> </div> </div> <form class=md-header__option data-md-component=palette> <input class=md-option data-md-color-media=(prefers-color-scheme) data-md-color-scheme=default data-md-color-primary=indigo data-md-color-accent=indigo aria-label="Change Mode" type=radio name=__palette id=__palette_0> <label class="md-header__button md-icon" title="Change Mode" for=__palette_1 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="m14.3 16-.7-2h-3.2l-.7 2H7.8L11 7h2l3.2 9zM20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zm-9.15 3.96h2.3L12 9z"/></svg> </label> <input class=md-option data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme=default data-md-color-primary=red data-md-color-accent=red aria-label="Dark Mode" type=radio name=__palette id=__palette_1> <label class="md-header__button md-icon" title="Dark Mode" for=__palette_2 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="m17.75 4.09-2.53 1.94.91 3.06-2.63-1.81-2.63 1.81.91-3.06-2.53-1.94L12.44 4l1.06-3 1.06 3zm3.5 6.91-1.64 1.25.59 1.98-1.7-1.17-1.7 1.17.59-1.98L15.75 11l2.06-.05L18.5 9l.69 1.95zm-2.28 4.95c.83-.08 1.72 1.1 1.19 1.85-.32.45-.66.87-1.08 1.27C15.17 23 8.84 23 4.94 19.07c-3.91-3.9-3.91-10.24 0-14.14.4-.4.82-.76 1.27-1.08.75-.53 1.93.36 1.85 1.19-.27 2.86.69 5.83 2.89 8.02a9.96 9.96 0 0 0 8.02 2.89m-1.64 2.02a12.08 12.08 0 0 1-7.8-3.47c-2.17-2.19-3.33-5-3.49-7.82-2.81 3.14-2.7 7.96.31 10.98 3.02 3.01 7.84 3.12 10.98.31"/></svg> </label> <input class=md-option data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme=slate data-md-color-primary=red data-md-color-accent=amber aria-label="Light Mode" type=radio name=__palette id=__palette_2> <label class="md-header__button md-icon" title="Light Mode" for=__palette_0 hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M12 7a5 5 0 0 1 5 5 5 5 0 0 1-5 5 5 5 0 0 1-5-5 5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3m0-7 2.39 3.42C13.65 5.15 12.84 5 12 5s-1.65.15-2.39.42zM3.34 7l4.16-.35A7.2 7.2 0 0 0 5.94 8.5c-.44.74-.69 1.5-.83 2.29zm.02 10 1.76-3.77a7.131 7.131 0 0 0 2.38 4.14zM20.65 7l-1.77 3.79a7.02 7.02 0 0 0-2.38-4.15zm-.01 10-4.14.36c.59-.51 1.12-1.14 1.54-1.86.42-.73.69-1.5.83-2.29zM12 22l-2.41-3.44c.74.27 1.55.44 ********** 0 1.63-.17 2.37-.44z"/></svg> </label> </form> <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script> <label class="md-header__button md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> </label> <div class=md-search data-md-component=search role=dialog> <label class=md-search__overlay for=__search></label> <div class=md-search__inner role=search> <form class=md-search__form name=search> <input type=text class=md-search__input name=query aria-label=Search placeholder=Search autocapitalize=off autocorrect=off autocomplete=off spellcheck=false data-md-component=search-query required> <label class="md-search__icon md-icon" for=__search> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg> </label> <nav class=md-search__options aria-label=Search> <button type=reset class="md-search__icon md-icon" title=Clear aria-label=Clear tabindex=-1> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg> </button> </nav> <div class=md-search__suggest data-md-component=search-suggest></div> </form> <div class=md-search__output> <div class=md-search__scrollwrap tabindex=0 data-md-scrollfix> <div class=md-search-result data-md-component=search-result> <div class=md-search-result__meta> Initializing search </div> <ol class=md-search-result__list role=presentation></ol> </div> </div> </div> </div> </div> </nav> </header> <div class=md-container data-md-component=container> <nav class=md-tabs aria-label=Tabs data-md-component=tabs> <div class=md-grid> <ul class=md-tabs__list> <li class=md-tabs__item> <a href="../../index.html" class=md-tabs__link> Home </a> </li> <li class=md-tabs__item> <a href="../../support/index.html" class=md-tabs__link> Support </a> </li> <li class="md-tabs__item md-tabs__item--active"> <a href="../index.html" class=md-tabs__link> Documentation </a> </li> <li class=md-tabs__item> <a href="../../legal/index.html" class=md-tabs__link> Legal </a> </li> </ul> </div> </nav> <main class=md-main data-md-component=main> <div class="md-main__inner md-grid"> <div class="md-sidebar md-sidebar--primary" data-md-component=sidebar data-md-type=navigation> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--primary md-nav--lifted" aria-label=Navigation data-md-level=0> <label class=md-nav__title for=__drawer> <a href="../../index.html" title="Pandas TA" class="md-nav__button md-logo" aria-label="Pandas TA" data-md-component=logo> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/></svg> </a> Pandas TA </label> <ul class=md-nav__list data-md-scrollfix> <li class="md-nav__item md-nav__item--pruned md-nav__item--nested"> <a href="../../index.html" class=md-nav__link> <span class=md-ellipsis> Home </span> <span class="md-nav__icon md-icon"></span> </a> </li> <li class="md-nav__item md-nav__item--pruned md-nav__item--nested"> <a href="../../support/index.html" class=md-nav__link> <span class=md-ellipsis> Support </span> <span class="md-nav__icon md-icon"></span> </a> </li> <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested"> <input class="md-nav__toggle md-toggle " type=checkbox id=__nav_3 checked> <div class="md-nav__link md-nav__container"> <a href="../index.html" class="md-nav__link "> <span class=md-ellipsis> Documentation </span> </a> <label class="md-nav__link " for=__nav_3 id=__nav_3_label tabindex> <span class="md-nav__icon md-icon"></span> </label> </div> <nav class=md-nav data-md-level=1 aria-labelledby=__nav_3_label aria-expanded=true> <label class=md-nav__title for=__nav_3> <span class="md-nav__icon md-icon"></span> Documentation </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href="../ta-extension/index.html" class=md-nav__link> <span class=md-ellipsis> DataFrame Extension </span> </a> </li> <li class=md-nav__item> <a href="../studies/index.html" class=md-nav__link> <span class=md-ellipsis> Studies </span> </a> </li> <li class=md-nav__item> <a href="../events/index.html" class=md-nav__link> <span class=md-ellipsis> Events </span> </a> </li> <li class=md-nav__item> <a href="../custom/index.html" class=md-nav__link> <span class=md-ellipsis> Custom Directory </span> </a> </li> <li class="md-nav__item md-nav__item--active md-nav__item--nested"> <input class="md-nav__toggle md-toggle " type=checkbox id=__nav_3_6 checked> <label class=md-nav__link for=__nav_3_6 id=__nav_3_6_label tabindex=0> <span class=md-ellipsis> Indicators </span> <span class="md-nav__icon md-icon"></span> </label> <nav class=md-nav data-md-level=2 aria-labelledby=__nav_3_6_label aria-expanded=true> <label class=md-nav__title for=__nav_3_6> <span class="md-nav__icon md-icon"></span> Indicators </label> <ul class=md-nav__list data-md-scrollfix> <li class=md-nav__item> <a href="../candle/index.html" class=md-nav__link> <span class=md-ellipsis> Candle </span> </a> </li> <li class=md-nav__item> <a href="../cycle/index.html" class=md-nav__link> <span class=md-ellipsis> Cycle </span> </a> </li> <li class="md-nav__item md-nav__item--active"> <input class="md-nav__toggle md-toggle" type=checkbox id=__toc> <label class="md-nav__link md-nav__link--active" for=__toc> <span class=md-ellipsis> Momentum </span> <span class="md-nav__icon md-icon"></span> </label> <a href="index.html" class="md-nav__link md-nav__link--active"> <span class=md-ellipsis> Momentum </span> </a> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.ao.ao" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;ao </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.apo.apo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;apo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.bias.bias" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;bias </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.bop.bop" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;bop </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.brar.brar" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;brar </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cci.cci" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cci </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cfo.cfo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cfo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cg.cg" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cg </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cmo.cmo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cmo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.coppock.coppock" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;coppock </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.crsi.crsi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;crsi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cti.cti" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cti </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.dm.dm" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;dm </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.er.er" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;er </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.eri.eri" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;eri </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.exhc.exhc" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;exhc </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.fisher.fisher" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;fisher </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.inertia.inertia" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;inertia </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.kdj.kdj" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;kdj </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.kst.kst" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;kst </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.macd.macd" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;macd </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.mom.mom" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;mom </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.pgo.pgo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;pgo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.ppo.ppo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;ppo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.psl.psl" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;psl </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.qqe.qqe" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;qqe </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.roc.roc" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;roc </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.rsi.rsi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;rsi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.rsx.rsx" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;rsx </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.rvgi.rvgi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;rvgi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.slope.slope" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;slope </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.smc.smc" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;smc </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.smi.smi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;smi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.squeeze.squeeze" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;squeeze </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.squeeze_pro.squeeze_pro" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;squeeze_pro </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.stc.stc" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;stc </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.stoch.stoch" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;stoch </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.stochf.stochf" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;stochf </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.stochrsi.stochrsi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;stochrsi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.tmo.tmo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;tmo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.trix.trix" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;trix </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.tsi.tsi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;tsi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.uo.uo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;uo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.willr.willr" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;willr </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href="../overlap/index.html" class=md-nav__link> <span class=md-ellipsis> Overlap </span> </a> </li> <li class=md-nav__item> <a href="../performance/index.html" class=md-nav__link> <span class=md-ellipsis> Performance </span> </a> </li> <li class=md-nav__item> <a href="../statistics/index.html" class=md-nav__link> <span class=md-ellipsis> Statistics </span> </a> </li> <li class=md-nav__item> <a href="../trend/index.html" class=md-nav__link> <span class=md-ellipsis> Trend </span> </a> </li> <li class=md-nav__item> <a href="../volatility/index.html" class=md-nav__link> <span class=md-ellipsis> Volatility </span> </a> </li> <li class=md-nav__item> <a href="../volume/index.html" class=md-nav__link> <span class=md-ellipsis> Volume </span> </a> </li> </ul> </nav> </li> <li class=md-nav__item> <a href="../utilities/index.html" class=md-nav__link> <span class=md-ellipsis> Utilities </span> </a> </li> </ul> </nav> </li> <li class="md-nav__item md-nav__item--pruned md-nav__item--nested"> <a href="../../legal/index.html" class=md-nav__link> <span class=md-ellipsis> Legal </span> <span class="md-nav__icon md-icon"></span> </a> </li> </ul> </nav> </div> </div> </div> <div class="md-sidebar md-sidebar--secondary" data-md-component=sidebar data-md-type=toc> <div class=md-sidebar__scrollwrap> <div class=md-sidebar__inner> <nav class="md-nav md-nav--secondary" aria-label="Table of contents"> <label class=md-nav__title for=__toc> <span class="md-nav__icon md-icon"></span> Table of contents </label> <ul class=md-nav__list data-md-component=toc data-md-scrollfix> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.ao.ao" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;ao </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.apo.apo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;apo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.bias.bias" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;bias </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.bop.bop" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;bop </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.brar.brar" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;brar </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cci.cci" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cci </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cfo.cfo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cfo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cg.cg" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cg </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cmo.cmo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cmo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.coppock.coppock" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;coppock </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.crsi.crsi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;crsi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.cti.cti" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;cti </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.dm.dm" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;dm </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.er.er" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;er </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.eri.eri" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;eri </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.exhc.exhc" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;exhc </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.fisher.fisher" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;fisher </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.inertia.inertia" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;inertia </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.kdj.kdj" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;kdj </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.kst.kst" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;kst </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.macd.macd" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;macd </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.mom.mom" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;mom </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.pgo.pgo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;pgo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.ppo.ppo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;ppo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.psl.psl" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;psl </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.qqe.qqe" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;qqe </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.roc.roc" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;roc </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.rsi.rsi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;rsi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.rsx.rsx" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;rsx </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.rvgi.rvgi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;rvgi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.slope.slope" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;slope </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.smc.smc" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;smc </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.smi.smi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;smi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.squeeze.squeeze" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;squeeze </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.squeeze_pro.squeeze_pro" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;squeeze_pro </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.stc.stc" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;stc </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.stoch.stoch" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;stoch </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.stochf.stochf" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;stochf </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.stochrsi.stochrsi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;stochrsi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.tmo.tmo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;tmo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.trix.trix" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;trix </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.tsi.tsi" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;tsi </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.uo.uo" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;uo </span> </a> </li> <li class=md-nav__item> <a href="index.html#src.pandas_ta.momentum.willr.willr" class=md-nav__link> <span class=md-ellipsis> <code class="doc-symbol doc-symbol-toc doc-symbol-function"></code>&nbsp;willr </span> </a> </li> </ul> </nav> </div> </div> </div> <div class=md-content data-md-component=content> <article class="md-content__inner md-typeset"> <h1>Momentum</h1> <hr> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.ao.ao> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">ao</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.ao.ao" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>ao</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>fast</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>slow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Awesome Oscillator</p> <p>This indicator attempts to identify momentum with the intention to affirm trends or anticipate possible reversals.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.ifcm.co.uk/ntx-indicators/awesome-oscillator">ifcm</a></li> <li><a href="https://www.tradingview.com/wiki/Awesome_Oscillator_(AO)">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>fast</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Fast period. Default: <code>5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Slow period. Default: <code>34</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.apo.apo> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">apo</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.apo.apo" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>apo</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>fast</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>slow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Absolute Price Oscillator</p> <p>This indicator attempts to quantify momentum.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingtechnologies.com/xtrader-help/x-study/technical-indicator-definitions/absolute-price-oscillator-apo/">tradingtechnologies</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>fast</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Fast period. Default: <code>12</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Slow period. Default: <code>26</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"sma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <ul> <li>Simply the difference of two different EMAs.</li> <li>APO and MACD lines are equivalent.</li> </ul> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.bias.bias> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">bias</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.bias.bias" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>bias</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Bias</p> <p>This indicator computes the Rate of Change between the source and a moving average.</p> <details class=sources open> <summary>Sources</summary> <ul> <li>Few internet resources on definitive definition.</li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>26</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"sma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.bop.bop> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">bop</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.bop.bop" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>bop</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>open_</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Balance of Power</p> <p>This indicator attempts to quantify the market strength of buyers versus sellers.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="http://www.worden.com/TeleChartHelp/Content/Indicators/Balance_of_Power.htm">worden</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>open_</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>open</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><script async crossorigin="anonymous" src="../../assets/external/pagead2.googlesyndication.com/pagead/js/adsbygoogle.c5a89bc8.js"></script> <!-- disp-sq --> <p><ins class=adsbygoogle data-ad-client=ca-pub-1083754683080475 data-ad-format=auto data-ad-slot=9545572080 data-full-width-responsive=true style=display:block></ins></p> <script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script> <p><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.brar.brar> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">brar</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.brar.brar" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>brar</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>open_</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>BRAR</p> <p>BR and AR</p> <details class=sources open> <summary>Sources</summary> <ul> <li>No internet resources on definitive definition.</li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>open_</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>open</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>26</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>2 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.cci.cci> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">cci</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.cci.cci" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>cci</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>c</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Commodity Channel Index</p> <p>This indicator attempts to identify "overbought" and "oversold" levels relative to a mean.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingview.com/wiki/Commodity_Channel_Index_(CCI)">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>c</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scaling Constant. Default: <code>0.015</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.cfo.cfo> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">cfo</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.cfo.cfo" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>cfo</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Chande Forcast Oscillator</p> <p>This indicator attempts to calculate the percentage difference between the actual price and the Time Series Forecast (the endpoint of a linear regression line).</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.fmlabs.com/reference/default.htm?url=ForecastOscillator.htm">fmlabs</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>9</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.cg.cg> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">cg</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.cg.cg" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>cg</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Center of Gravity</p> <p>This indicator, by John Ehlers, attempts to identify turning points with minimal to zero lag and smoothing.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="http://www.mesasoftware.com/papers/TheCGOscillator.pdf">MESA Software</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>10</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.cmo.cmo> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">cmo</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.cmo.cmo" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>cmo</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Chande Momentum Oscillator</p> <p>This indicator attempts to capture momentum.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/chande-momentum-oscillator-cmo/">tradingtechnologies</a></li> <li><a href="https://www.tradingview.com/script/hdrf0fXV-Variable-Index-Dynamic-Average-VIDYA/">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Uses EMA if <code>False</code>. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <ul> <li>Overbought around 50</li> <li>Oversold around -50.</li> </ul> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.coppock.coppock> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">coppock</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.coppock.coppock" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>coppock</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>fast</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>slow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Coppock Curve</p> <p>This indicator, by Edwin Coppock 1962, was originally called the "Trendex Model", attempts to identify major upturns and downturns.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://en.wikipedia.org/wiki/Coppock_curve">wikipedia</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>WMA period. Default: <code>10</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>fast</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Fast ROC period. Default: <code>11</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Slow ROC period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <p>Although designed for monthly use, a daily calculation over the same period length can be made, converting the periods to 294-day and 231-day rate of changes, and a 210-day WMA.</p> </details> </div> </div> </div> </div> </div><script async crossorigin="anonymous" src="../../assets/external/pagead2.googlesyndication.com/pagead/js/adsbygoogle.c5a89bc8.js"></script> <!-- disp-sq --> <p><ins class=adsbygoogle data-ad-client=ca-pub-1083754683080475 data-ad-format=auto data-ad-slot=9545572080 data-full-width-responsive=true style=display:block></ins></p> <script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script> <p><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.crsi.crsi> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">crsi</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.crsi.crsi" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>crsi</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>rsi_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>streak_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>rank_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Connors Relative Strength Index</p> <p>This indicator attempts to identify momentum and potential reversals at "overbought" or "oversold" conditions.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://alvarezquanttrading.com/blog/connorsrsi-analysis/">alvarezquanttrading</a></li> <li><a href="https://www.tradingview.com/support/solutions/43000502017-connors-rsi-crsi/">tradingview</a></li> <li>An Introduction to ConnorsRSI. Connors Research Trading Strategy Series. Connors, L., Alvarez, C., &amp; Radtke, M. (2012). ISBN 978-0-9853072-9-5.</li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>rsi_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The RSI period. Default: <code>3</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>streak_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Streak RSI period. Default: <code>2</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>rank_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Percent Rank length. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.cti.cti> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">cti</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.cti.cti" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>cti</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Correlation Trend Indicator</p> <p>This oscillator, by John Ehlers' in 2020, attempts to identify the magnitude and direction of a trend using linear regession.</p> <details class=note open> <summary>Note</summary> <p>This is a wrapper for <code>ta.linreg(close, r=True)</code>.</p> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>12</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.dm.dm> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">dm</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.dm.dm" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>dm</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Directional Movement</p> <p>This indicator, by J. Welles Wilder in 1978, attempts to determine direction.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&amp;ID=24&amp;Name=Directional_Movement_Index">sierrachart</a></li> <li><a href="https://www.tradingview.com/pine-script-reference/#fun_dmi">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"rma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>2 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.er.er> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">er</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.er.er" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>er</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Efficiency Ratio</p> <p>This indicator, by Perry J. Kaufman, attempts to identify market noise or volatility.</p> <details class=sources open> <summary>Sources</summary> <ul> <li>"New Trading Systems and Methods", Perry J. Kaufman</li> <li><a href="https://help.tc2000.com/m/69404/l/749623-kaufman-efficiency-ratio">tc2000</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <p>It is calculated by dividing the net change in price movement over <code>n</code> periods by the sum of the absolute net changes over the same <code>n</code> periods.</p> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.eri.eri> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">eri</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.eri.eri" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>eri</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Elder Ray Index</p> <p>This indicator, by Dr Alexander Elder, attempts to identify market strength.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://admiralmarkets.com/education/articles/forex-indicators/bears-and-bulls-power-indicator">admiralmarkets</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>2 columns</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <ul> <li>Possible entry signals when used in combination with a trend,</li> <li>Bear Power attempts to quantify lower value appeal.</li> <li>Bull Power attempts the to quantify higher value appeal.</li> </ul> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.exhc.exhc> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">exhc</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.exhc.exhc" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>exhc</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>cap</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>asint</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>show_all</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>nozeros</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Exhaustion Count</p> <p>This indicator attempts to identify rising/falling exhaustion.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://demark.com">demark</a></li> <li><a href="http://practicaltechnicalanalysis.blogspot.com/2013/01/tom-demark-sequential.html">practicaltechnicalanalysis</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>Series of close's</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>4</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>cap</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Count cap. For no cap, set to <code>0</code>. Default: <code>13</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>show_all</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Counts 1 - 13. For 6 - 9, set to <code>False</code>. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>asint</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Returns as <code>Int</code>. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>nozeros</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Replace zeros with <code>np.nan</code>. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>2 columns</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <p>Similar to TD Sequential</p> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.fisher.fisher> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">fisher</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.fisher.fisher" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>fisher</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>signal</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Fisher Transform</p> <p>This indicator attempts to identify significant reversals through normalization.</p> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>9</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>signal</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Signal period. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>2 columns</p> </div> </td> </tr> </tbody> </table> <details class=tip open> <summary>Reversal Signal</summary> <p>When the two lines cross.</p> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.inertia.inertia> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">inertia</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.inertia.inertia" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>inertia</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>rvi_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>refined</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>thirds</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-12" id=__codelineno-0-12 name=__codelineno-0-12></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-13" id=__codelineno-0-13 name=__codelineno-0-13></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-14" id=__codelineno-0-14 name=__codelineno-0-14></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Inertia</p> <p>This indicator, by Donald Dorsey, is the <em>rvi</em> smoothed by the Least Squares MA.</p> <details class=sources open> <summary>Sources</summary> <ul> <li>Donald Dorsey, some article in September, 1995.</li> <li><a href="https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&amp;ID=285&amp;Name=Inertia">sierrachart</a></li> <li><a href="https://www.tradingview.com/script/mLZJqxKn-Relative-Volatility-Index/">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>20</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>rvi_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>RVI period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>refined</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Use 'refined' calculation. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>thirds</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Use 'thirds' calculation. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"ema"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <ul> <li>Negative Inertia when less than 50.</li> <li>Positive Inertia when greater than 50.</li> </ul> </details> </div> </div> </div> </div> </div><script async crossorigin="anonymous" src="../../assets/external/pagead2.googlesyndication.com/pagead/js/adsbygoogle.c5a89bc8.js"></script> <!-- disp-sq --> <p><ins class=adsbygoogle data-ad-client=ca-pub-1083754683080475 data-ad-format=auto data-ad-slot=9545572080 data-full-width-responsive=true style=display:block></ins></p> <script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script> <p><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.kdj.kdj> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">kdj</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.kdj.kdj" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>kdj</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>signal</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>KDJ</p> <p>This indicator, derived from the Slow Stochastic, includes an extra signal named the J line. The J line represents the divergence of the %D value from the %K.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://docs.anychart.com/Stock_Charts/Technical_Indicators/Mathematical_Description#kdj">anychart</a></li> <li><a href="https://www.prorealcode.com/prorealtime-indicators/kdj/">prorealcode</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>9</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>signal</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Signal period. Default: <code>3</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>3 columns</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <p>The J can go beyond <code>[0, 100]</code> for %K and %D lines when charted.</p> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.kst.kst> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">kst</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.kst.kst" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>kst</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>signal</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>roc1</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>roc2</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>roc3</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>roc4</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>sma1</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>sma2</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=n>sma3</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a>    <span class=n>sma4</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-12" id=__codelineno-0-12 name=__codelineno-0-12></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-13" id=__codelineno-0-13 name=__codelineno-0-13></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-14" id=__codelineno-0-14 name=__codelineno-0-14></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-15" id=__codelineno-0-15 name=__codelineno-0-15></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>'Know Sure Thing'</p> <p>This indicator, by Martin Pring, attempts to capture trends using a smoothed indicator of four different smoothed ROCs.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.incrediblecharts.com/indicators/kst.php">incrediblecharts</a></li> <li><a href="https://www.tradingview.com/wiki/Know_Sure_Thing_(KST)">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>roc1</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>ROC 1 period. Default: <code>10</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>roc2</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>ROC 2 period. Default: <code>15</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>roc3</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>ROC 3 period. Default: <code>20</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>roc4</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>ROC 4 period. Default: <code>30</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>sma1</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>SMA 1 period. Default: <code>10</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>sma2</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>SMA 2 period. Default: <code>10</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>sma3</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>SMA 3 period. Default: <code>10</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>sma4</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>SMA 4 period. Default: <code>15</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>signal</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Signal period. Default: <code>9</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>2 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.macd.macd> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">macd</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.macd.macd" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>macd</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>fast</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>slow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>signal</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Moving Average Convergence Divergence</p> <p>This indicator attempts to identify trends.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingview.com/wiki/MACD_(Moving_Average_Convergence/Divergence)">tradingview</a></li> <li><a href="https://tr.tradingview.com/script/YFlKXHnP/">tradingview (AS Mode)</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>fast</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Fast MA period. Default: <code>12</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Slow MA period. Default: <code>26</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>signal</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Signal period. Default: <code>9</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>asmode</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p>Enable AS version of MACD. Default: <code>False</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>3 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.mom.mom> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">mom</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.mom.mom" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>mom</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Momentum</p> <p>This indicator attempts to quantify speed by using the differences over a bar length.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="http://www.onlinetradingconcepts.com/TechnicalAnalysis/Momentum.html">onlinetradingconcepts</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.pgo.pgo> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">pgo</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.pgo.pgo" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>pgo</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Pretty Good Oscillator</p> <p>This indicator, by Mark Johnson, attempts to identify breakouts for longer time periods based on the distance of the current bar to its N-day SMA, expressed in terms of an ATR over a similar length.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://library.tradingtechnologies.com/trade/chrt-ti-pretty-good-oscillator.html">tradingtechnologies</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Entry</summary> <ul> <li>Long when greater than 3.</li> <li>Short when less than -3.</li> </ul> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.ppo.ppo> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">ppo</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.ppo.ppo" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>ppo</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>fast</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>slow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>signal</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Percentage Price Oscillator</p> <p>Similar to MACD.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.investopedia.com/terms/p/ppo.asp">investopedia</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>fast</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Fast MA period. Default: <code>12</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Slow MA period. Default: <code>26</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>signal</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Signal period. Default: <code>9</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"sma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>3 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.psl.psl> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">psl</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.psl.psl" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>psl</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>open_</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Psychological Line</p> <p>This indicator compares the number of the rising bars to the total number of bars. In other words, it is the percentage of bars that are above the previous bar over a given length.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.quantshare.com/item-851-psychological-line">quantshare</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>open_</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>open</code> Series</p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>12</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.qqe.qqe> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">qqe</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.qqe.qqe" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>qqe</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>smooth</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>factor</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Quantitative Qualitative Estimation</p> <p>This indicator is similar to SuperTrend but uses a Smoothed <code>rsi</code> with upper and lower bands.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.prorealcode.com/prorealtime-indicators/qqe-quantitative-qualitative-estimation/">prorealcode</a></li> <li><a href="https://www.tradingpedia.com/forex-trading-indicators/quantitative-qualitative-estimation">tradingpedia</a></li> <li><a href="https://www.tradingview.com/script/IYfA9R2k-QQE-MT4/">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>RSI period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>smooth</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>RSI smoothing period. Default: <code>5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>factor</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>QQE Factor. Default: <code>4.236</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"ema"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>4 columns</p> </div> </td> </tr> </tbody> </table> <details class=tip open> <summary>Trend</summary> <ul> <li>Long: When the Smoothed RSI crosses the previous upperband.</li> <li>Short: When the Smoothed RSI crosses the previous lowerband.</li> </ul> </details> <details class=note open> <summary>See also</summary> <ul> <li>QQE.mq5 by EarnForex Copyright © 2010</li> <li>Tim Hyder (2008) version</li> <li>Roman Ignatov (2006) version</li> </ul> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.roc.roc> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">roc</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.roc.roc" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>roc</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Rate of Change</p> <p>This indicator, also (confusingly) known as Momentum, is a pure oscillator that quantifies the percent change.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingview.com/wiki/Rate_of_Change_(ROC)">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>10</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.rsi.rsi> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">rsi</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.rsi.rsi" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>rsi</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Relative Strength Index</p> <p>This oscillator used to attempts to quantify "velocity" and "magnitude".</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingview.com/wiki/Relative_Strength_Index_(RSI)">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"rma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> <details class=warning open> <summary>Warning</summary> <p>TA-Lib Correlation: <code>np.float64(0.9289853267851295)</code></p> </details> <details class=tip open> <summary>Tip</summary> <p>Corrective contributions welcome!</p> </details> </div> </div> </div> </div> </div><script async crossorigin="anonymous" src="../../assets/external/pagead2.googlesyndication.com/pagead/js/adsbygoogle.c5a89bc8.js"></script> <!-- disp-sq --> <p><ins class=adsbygoogle data-ad-client=ca-pub-1083754683080475 data-ad-format=auto data-ad-slot=9545572080 data-full-width-responsive=true style=display:block></ins></p> <script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script> <p><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.rsx.rsx> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">rsx</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.rsx.rsx" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>rsx</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Relative Strength Xtra</p> <p>This indicator, by Jurik Research, is an enhanced version of the RSI which attemps to reduce noise and provide a clearer, though slightly delayed, signal.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="http://www.jurikres.com/catalog1/ms_rsx.htm">jurikres</a></li> <li><a href="https://www.prorealcode.com/prorealtime-indicators/jurik-rsx/">prorealcode</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.rvgi.rvgi> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">rvgi</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.rvgi.rvgi" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>rvgi</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>open_</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>swma_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Relative Vigor Index</p> <p>This indicator attempts to quantify the strength of a trend relative to its trading range.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.investopedia.com/terms/r/relative_vigor_index.asp">investopedia</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>open_</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>open</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>swma_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>SWMA period. Default: <code>4</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.slope.slope> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">slope</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.slope.slope" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>slope</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>as_angle</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>to_degrees</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Slope</p> <p>Calculates a rolling slope.</p> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>as_angle</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Converts slope to an angle in radians per <code>np.arctan()</code>. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>to_degrees</code> </td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p>If <code>as_angle=True</code>, converts radians to degrees. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.smc.smc> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">smc</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.smc.smc" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>smc</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>open_</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>abr_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>close_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>vol_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>percent</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=n>vol_ratio</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a>    <span class=n>asint</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-12" id=__codelineno-0-12 name=__codelineno-0-12></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-13" id=__codelineno-0-13 name=__codelineno-0-13></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-14" id=__codelineno-0-14 name=__codelineno-0-14></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-15" id=__codelineno-0-15 name=__codelineno-0-15></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-16" id=__codelineno-0-16 name=__codelineno-0-16></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Smart Money Concept</p> <p>This indicator combines several techniques in an attempt to identify significant movements that might indicate "smart money" actions. It uses candlestick patterns, moving averages, and imbalance calculations.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingview.com/script/CnB3fSph-Smart-Money-Concepts-LuxAlgo/">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>abr_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>ABR length. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>close_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The <code>close</code> MA period. Default: <code>50</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>vol_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Volatility period. Default: <code>20</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>percent</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Percent of wick that exceeds the body. Default: <code>5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>vol_ratio</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Volatility ratio (high) limit. Default: <code>1.5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>asint</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Returns as <code>Int</code>. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"sma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>7 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.smi.smi> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">smi</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.smi.smi" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>smi</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>fast</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>slow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>signal</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>SMI Ergodic Indicator</p> <p>This indicator, by William Blau, is the same as the TSI except the SMI includes a signal line. A trend is considered bullish when crossing above zero and bearish when crossing below zero. This implementation includes both the SMI Ergodic Indicator and SMI Ergodic Oscillator.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.motivewave.com/studies/smi_ergodic_indicator.htm">motivewave</a></li> <li><a href="https://www.tradingview.com/script/Xh5Q0une-SMI-Ergodic-Oscillator/">tradingview A</a></li> <li><a href="https://www.tradingview.com/script/cwrgy4fw-SMIIO/">tradingview B</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>fast</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The short period. Default: <code>5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The long period. Default: <code>20</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>signal</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Signal period. Default: <code>5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>3 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.squeeze.squeeze> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">squeeze</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.squeeze.squeeze" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>squeeze</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>bb_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>bb_std</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>kc_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>kc_scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>mom_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=n>mom_smooth</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a>    <span class=n>use_tr</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-12" id=__codelineno-0-12 name=__codelineno-0-12></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-13" id=__codelineno-0-13 name=__codelineno-0-13></a>    <span class=n>prenan</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-14" id=__codelineno-0-14 name=__codelineno-0-14></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-15" id=__codelineno-0-15 name=__codelineno-0-15></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-16" id=__codelineno-0-16 name=__codelineno-0-16></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Squeeze</p> <p>This indicator, based on John Carter's "TTM Squeeze" indicator, attempts identify momentum using volatility.</p> <details class=sources open> <summary>Sources</summary> <ul> <li>"Mastering the Trade" (chapter 11), John Carter</li> <li><a href="https://tlc.thinkorswim.com/center/reference/Tech-Indicators/studies-library/T-U/TTM-Squeeze">thinkorswim</a></li> <li><a href="https://tradestation.tradingappstore.com/products/TTMSqueeze">tradestation</a></li> <li><a href="https://www.tradingview.com/scripts/lazybear/">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>bb_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>BB period. Default: <code>20</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>bb_std</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>BB Std. Dev. Default: <code>2</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>kc_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>KC period. Default: <code>20</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>kc_scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>KC scalar. Default: <code>1.5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mom_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Momentum Period. Default: <code>12</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mom_smooth</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Momentum Smoothing period. Default: <code>6</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>One of: "ema" or "sma". Default: <code>"sma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>prenan</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Apply prenans. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>tr</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p>Use True Range for Keltner Channels. Default: <code>True</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>asint</code></td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Returns as <code>Int</code>. Default: <code>True</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>lazybear</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p>LazyBear's TradingView. Default: <code>False</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>detailed</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p>Extra detailed. Default: <code>False</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <ul> <li>Default: 4 columns</li> <li>Detailed: 10 columns</li> </ul> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Volatility</summary> <ul> <li>Increasing: <code>kc</code> and <code>bbands</code> difference increases</li> <li>Decreasing: <code>kc</code> and <code>bbands</code> difference decreases</li> </ul> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.squeeze_pro.squeeze_pro> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">squeeze_pro</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.squeeze_pro.squeeze_pro" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>squeeze_pro</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>bb_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>bb_std</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>kc_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>kc_scalar_narrow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>kc_scalar_normal</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=n>kc_scalar_wide</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a>    <span class=n>mom_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-12" id=__codelineno-0-12 name=__codelineno-0-12></a>    <span class=n>mom_smooth</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-13" id=__codelineno-0-13 name=__codelineno-0-13></a>    <span class=n>use_tr</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-14" id=__codelineno-0-14 name=__codelineno-0-14></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-15" id=__codelineno-0-15 name=__codelineno-0-15></a>    <span class=n>prenan</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-16" id=__codelineno-0-16 name=__codelineno-0-16></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-17" id=__codelineno-0-17 name=__codelineno-0-17></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-18" id=__codelineno-0-18 name=__codelineno-0-18></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Squeeze Pro</p> <p>This indicator, based on John Carter's "TTM Squeeze" indicator, attempts identify momentum using volatility with additional details.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://usethinkscript.com/threads/john-carters-squeeze-pro-indicator-for-thinkorswim-free.4021/">usethinkscript</a></li> <li><a href="https://www.tradingview.com/script/TAAt6eRX-Squeeze-PRO-Indicator-Makit0/">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>bb_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>BB period. Default: <code>20</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>bb_std</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>BB Std. Dev. Default: <code>2</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>kc_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>KC period. Default: <code>20</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>kc_scalar_normal</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Keltner Channel scalar for normal channel. Default: <code>1.5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>kc_scalar_narrow</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Narrow channel KC scalar. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>kc_scalar_wide</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Wide channel KC scalar. Default: <code>2</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mom_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Momentum Period. Default: <code>12</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mom_smooth</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Momentum Smoothing period. Default: <code>6</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>One of: "ema" or "sma". Default: <code>"sma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>prenan</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Apply prenans. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>tr</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p>Use True Range for Keltner Channels. Default: <code>True</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>asint</code></td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Returns as <code>Int</code>. Default: <code>True</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>mamode</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p>Which MA to use. Default: <code>"sma"</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>detailed</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p>Extra detailed. Default: <code>False</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>6 columns (<em>default</em>) or 12 columns if <code>detailed=True</code></p> </div> </td> </tr> </tbody> </table> <details class=warning open> <summary>Warning</summary> <p>May be depreciated in the future and combined with <code>squeeze</code>.</p> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.stc.stc> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">stc</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.stc.stc" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>stc</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>tc_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>fast</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>slow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>factor</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Schaff Trend Cycle</p> <p>This indicator is an evolved MACD with additional smoothing.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://github.com/rengel8">rengel8</a></li> <li><a href="https://www.prorealcode.com/prorealtime-indicators/schaff-trend-cycle2/">prorealcode</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>tc_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>TC period. (Adjust to the half of cycle) Default: <code>10</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>fast</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Fast MA period. Default: <code>12</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Slow MA period. Default: <code>26</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>factor</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Smoothing factor for last stoch. calculation. Default: <code>0.5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>How many bars to shift the results. Default: <code>`0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>ma1</code></td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>User chosen MA. Default: <code>False</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>ma2</code></td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>User chosen MA. Default: <code>False</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>osc</code></td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>User chosen oscillator. Default: <code>False</code></p> </div> </td> </tr> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>3 columns</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <p>Can also seed STC with two MAs, <code>ma1</code> and <code>ma2</code>, or an oscillator <code>osc</code>.</p> <ul> <li><code>ma1</code> and <code>ma2</code> are <strong>both</strong> required if this option is used.</li> </ul> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.stoch.stoch> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">stoch</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.stoch.stoch" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>stoch</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>k</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>d</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>smooth_k</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-12" id=__codelineno-0-12 name=__codelineno-0-12></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Stochastic</p> <p>This indicator, by George Lane in the 1950's, attempts to identify and quantify momentum; it assumes that momentum precedes value change.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&amp;ID=332&amp;Name=KD_-_Slow">sierrachart</a></li> <li><a href="https://www.tradingview.com/wiki/Stochastic_(STOCH)">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>k</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Fast %K period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>d</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Slow %D period. Default: <code>3</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>smooth_k</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Slow %K period. Default: <code>3</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"sma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>3 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.stochf.stochf> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">stochf</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.stochf.stochf" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>stochf</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>k</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>d</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Fast Stochastic</p> <p>This indicator, by George Lane in the 1950's, attempts to identify and quantify momentum like STOCH, but is more volatile.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://corporatefinanceinstitute.com/resources/knowledge/trading-investing/fast-stochastic-indicator/">corporatefinanceinstitute</a></li> <li><a href="https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&amp;ID=333&amp;Name=KD_-_Fast">sierrachart</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>k</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Fast %K period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>d</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Slow %D period. Default: <code>3</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"sma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>2 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.stochrsi.stochrsi> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">stochrsi</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.stochrsi.stochrsi" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>stochrsi</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>rsi_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>k</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>d</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Stochastic RSI</p> <p>This indicator attempts to quantify RSI relative to its High-Low range.</p> <details class=sources open> <summary>Sources</summary> <ul> <li>"Stochastic RSI and Dynamic Momentum Index", Tushar Chande and Stanley Kroll, Stock &amp; Commodities V.11:5 (189-199)</li> <li><a href="https://www.tradingview.com/wiki/Stochastic_(STOCH)">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>rsi_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>RSI period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>k</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Fast %K period. Default: <code>3</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>d</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Slow %K period. Default: <code>3</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"sma"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>2 columns</p> </div> </td> </tr> </tbody> </table> <details class=note open> <summary>Note</summary> <p>May be more sensitive to RSI and thus identify potential "overbought" or "oversold" signals.</p> </details> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.tmo.tmo> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">tmo</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.tmo.tmo" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>tmo</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>open_</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>tmo_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>calc_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>smooth_length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>momentum</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>normalize</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>exclusive</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-12" id=__codelineno-0-12 name=__codelineno-0-12></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-13" id=__codelineno-0-13 name=__codelineno-0-13></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>True Momentum Oscillator</p> <p>This indicator attempts to quantify momentum.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingview.com/script/VRwDppqd-True-Momentum-Oscillator/">tradingview A</a></li> <li><a href="https://www.tradingview.com/script/65vpO7T5-True-Momentum-Oscillator-Universal-Edition/">tradingview B</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>open_</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>open</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>tmo_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>TMO period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>calc_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Initial MA period. Default: <code>5</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>smooth_length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Main and smooth signal MA period. Default: <code>3</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>See <code>help(ta.ma)</code>. Default: <code>"ema"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>momentum</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Compute main and smooth momentum. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>normalize</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Normalize. Default: <code>False</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>exclusive</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>Exclusive period over <code>n</code> bars, or inclusively over <code>n-1</code> bars. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p>DataFrame.fillna(value)</p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>4 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><script async crossorigin="anonymous" src="../../assets/external/pagead2.googlesyndication.com/pagead/js/adsbygoogle.c5a89bc8.js"></script> <!-- disp-sq --> <p><ins class=adsbygoogle data-ad-client=ca-pub-1083754683080475 data-ad-format=auto data-ad-slot=9545572080 data-full-width-responsive=true style=display:block></ins></p> <script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script> <p><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.trix.trix> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">trix</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.trix.trix" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>trix</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>signal</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Trix</p> <p>This indicator attempts to identify divergences as an oscillator.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingview.com/wiki/TRIX">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>18</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>signal</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Signal period. Default: <code>9</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.tsi.tsi> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">tsi</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.tsi.tsi" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>tsi</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>fast</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>slow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>signal</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>scalar</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>mamode</span><span class=p>:</span> <span class=n><span title=str>str</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.DataFrame>DataFrame</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>True Strength Index</p> <p>This indicator attempts to identify short-term swings in trend direction as well as identifying possible "overbought" and "oversold" signals.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.investopedia.com/terms/t/tsi.asp">investopedia</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>fast</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Fast MA period. Default: <code>13</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Slow MA period. Default: <code>25</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>signal</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Signal period. Default: <code>13</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>scalar</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>Scalar. Default: <code>100</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>mamode</code> </td> <td> <code><span title=str>str</span></code> </td> <td> <div class=doc-md-description> <p>Signal MA. See <code>help(ta.ma)</code>. Default: <code>"ema"</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.DataFrame>DataFrame</span></code> </td> <td> <div class=doc-md-description> <p>2 columns</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.uo.uo> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">uo</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.uo.uo" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>uo</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>fast</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>medium</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>slow</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=n>fast_w</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a>    <span class=n>medium_w</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-10" id=__codelineno-0-10 name=__codelineno-0-10></a>    <span class=n>slow_w</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.IntFloat>IntFloat</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-11" id=__codelineno-0-11 name=__codelineno-0-11></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-12" id=__codelineno-0-12 name=__codelineno-0-12></a>    <span class=n>drift</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-13" id=__codelineno-0-13 name=__codelineno-0-13></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-14" id=__codelineno-0-14 name=__codelineno-0-14></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-15" id=__codelineno-0-15 name=__codelineno-0-15></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>Ultimate Oscillator</p> <p>This indicator, by Larry Williams, attempts to identify momentum.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingview.com/wiki/Ultimate_Oscillator_(UO)">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>fast</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Fast %K period. Default: <code>7</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>medium</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Slow %K period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The Slow %D period. Default: <code>28</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>fast_w</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>The Fast %K period. Default: <code>4.0</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>medium_w</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>The Slow %K period. Default: <code>2.0</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>slow_w</code> </td> <td> <code><span title=float>float</span></code> </td> <td> <div class=doc-md-description> <p>The Slow %D period. Default: <code>1.0</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>drift</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Difference amount. Default: <code>1</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br><hr><br></p> <div class="doc doc-object doc-module"> <div class="doc doc-contents first"> <div class="doc doc-children"> <div class="doc doc-object doc-function"> <h2 class="doc doc-heading" id=src.pandas_ta.momentum.willr.willr> <code class="doc-symbol doc-symbol-heading doc-symbol-function"></code> <span class="doc doc-object-name doc-function-name">willr</span> <a class=headerlink href="index.html#src.pandas_ta.momentum.willr.willr" title="Permanent link">#</a></h2> <div class="language-python doc-signature highlight"><pre><span></span><code><a href="index.html#__codelineno-0-1" id=__codelineno-0-1 name=__codelineno-0-1></a><span class=nf>willr</span><span class=p>(</span>
<a href="index.html#__codelineno-0-2" id=__codelineno-0-2 name=__codelineno-0-2></a>    <span class=n>high</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-3" id=__codelineno-0-3 name=__codelineno-0-3></a>    <span class=n>low</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-4" id=__codelineno-0-4 name=__codelineno-0-4></a>    <span class=n>close</span><span class=p>:</span> <span class=n><span title=pandas.Series>Series</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-5" id=__codelineno-0-5 name=__codelineno-0-5></a>    <span class=n>length</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-6" id=__codelineno-0-6 name=__codelineno-0-6></a>    <span class=n>talib</span><span class=p>:</span> <span class=n><span title=bool>bool</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-7" id=__codelineno-0-7 name=__codelineno-0-7></a>    <span class=n>offset</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.Int>Int</span></span> <span class=o>=</span> <span class=kc>None</span><span class=p>,</span>
<a href="index.html#__codelineno-0-8" id=__codelineno-0-8 name=__codelineno-0-8></a>    <span class=o>**</span><span class=n>kwargs</span><span class=p>:</span> <span class=n><span title=pandas_ta._typing.DictLike>DictLike</span></span><span class=p>,</span>
<a href="index.html#__codelineno-0-9" id=__codelineno-0-9 name=__codelineno-0-9></a><span class=p>)</span> <span class=o>-&gt;</span> <span class=n><span title=pandas.Series>Series</span></span>
</code></pre></div> <div class="doc doc-contents"> <p>William's Percent R</p> <p>This indicator attempts to identify "overbought" and "oversold" conditions similar to the RSI.</p> <details class=sources open> <summary>Sources</summary> <ul> <li><a href="https://www.tradingview.com/wiki/Williams_%25R_(%25R)">tradingview</a></li> </ul> </details> <p><span class=doc-section-title>Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> <th>Default</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code>high</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>high</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>low</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>low</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>close</code> </td> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p><code>close</code> Series</p> </div> </td> <td> <em>required</em> </td> </tr> <tr class=doc-section-item> <td> <code>length</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>The period. Default: <code>14</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>talib</code> </td> <td> <code><span title=bool>bool</span></code> </td> <td> <div class=doc-md-description> <p>If installed, use TA Lib. Default: <code>True</code></p> </div> </td> <td> <code>None</code> </td> </tr> <tr class=doc-section-item> <td> <code>offset</code> </td> <td> <code><span title=int>int</span></code> </td> <td> <div class=doc-md-description> <p>Post shift. Default: <code>0</code></p> </div> </td> <td> <code>None</code> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Other Parameters:</span></p> <table> <thead> <tr> <th>Name</th> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td><code>fillna</code></td> <td> <code><span title=value>value</span></code> </td> <td> <div class=doc-md-description> <p><code>pd.DataFrame.fillna(value)</code></p> </div> </td> </tr> </tbody> </table> <p><span class=doc-section-title>Returns:</span></p> <table> <thead> <tr> <th>Type</th> <th>Description</th> </tr> </thead> <tbody> <tr class=doc-section-item> <td> <code><span title=pandas.Series>Series</span></code> </td> <td> <div class=doc-md-description> <p>1 column</p> </div> </td> </tr> </tbody> </table> </div> </div> </div> </div> </div><p><br></p> <!-- BEGIN INCLUDE notice.md '&lt;!--start--&gt;' '&lt;!--end--&gt;' --> <div class="admonition danger"> <p class=admonition-title>IMPORTANT</p> <p><strong>Thanks</strong> to all those that have sponsored and dontated to the library in the past! Your support has been greatly appreciated! <img alt="🙏" class="twemoji" src="../../assets/external/cdn.jsdelivr.net/gh/jdecked/twemoji@15.1.0/assets/svg/1f64f.svg" title=":pray:"></p> <p>Only <strong>Installation Bugs/Issues</strong> will addressed for releases between versions <em>0.4.25b</em> and <em>0.4.66b</em>. Releases beyond version <em>0.4.66b</em> will <strong>only</strong> be released after <strong>significant</strong> donations, sponsorships or yearly subscriptions have been received via <a href="https://www.buymeacoffee.com/twopirllc" style="color: #ff9200;">Buy Me a Coffee</a>.</p> <p>Support Tiers coming soon!</p> <!--
<div class="two-col-grid" style="margin-top: 1rem;">
    <div class="">Help keep this library and application the **best** in it's class!</div>
    <div class="">
        <progress class="" max="100" value="0" aria-label="Release Status" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0" style="--value: 0; --max: 100;"></progress>
    </div>
</div>
--> </div> <!-- END INCLUDE --> <p><br></p> <!-- BEGIN INCLUDE support/donations.md '&lt;!--start--&gt;' '&lt;!--end--&gt;' --> <p><br></p> <p><a href="https://www.buymeacoffee.com/twopirllc"><img align="left" alt="&quot;Buy Me A Coffee&quot;" src="../../assets/external/www.buymeacoffee.com/assets/img/custom_images/orange_img.png"></a> <br><br></p> <p><a href="https://ko-fi.com/K3K4ZRH9D"><img align="left" alt="ko-fi" src="../../assets/external/ko-fi.com/img/githubbutton_sm.svg"></a></p> <!-- END INCLUDE --> </article> </div> <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var labels=set.querySelector(".tabbed-labels");for(var tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script> <script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script> </div> <button type=button class="md-top md-icon" data-md-component=top hidden> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg> Back to top </button> </main> <footer class=md-footer> <div class="md-footer-meta md-typeset"> <div class="md-footer-meta__inner md-grid"> <div class=md-copyright> <div class=md-copyright__highlight> Copyright &copy; 2025 Pandas TA </div> Made with <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener"> Material for MkDocs </a> </div> <div class=md-social> <a href=mailto:<EMAIL> target=_blank rel=noopener title class=md-social__link> <svg xmlns=http://www.w3.org/2000/svg viewbox="0 0 24 24"><path d="M17 4H7a5 5 0 0 0-5 5v11h18a2 2 0 0 0 2-2V9a5 5 0 0 0-5-5m-7 14H4V9a3 3 0 0 1 3-3 3 3 0 0 1 3 3zm9-3h-2v-2h-4v-2h6zM9 11H5V9h4z"/></svg> </a> </div> </div> </div> </footer> </div> <div class=md-dialog data-md-component=dialog> <div class="md-dialog__inner md-typeset"></div> </div> <div class=md-progress data-md-component=progress role=progressbar></div> <script id=__config type=application/json>{"base": "../..", "features": ["announce.dismiss", "code.action.edit", "code.action.view", "content.code.annotate", "content.code.copy", "content.tabs.link", "content.tooltips", "header.autohide", "navigation.indexes", "navigation.instant", "navigation.instant.prefetch", "navigation.instant.progress", "navigation.path", "navigation.prune", "navigation.tabs", "navigation.top", "navigation.tracking", "search.highlight", "search.suggest", "toc.follow"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script> <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script> <script src="../../assets/js/extra.js"></script> <script src="../../assets/js/mathjax.js"></script> <script src="../../assets/js/tex-mml-chtml.js"></script> </body> </html>