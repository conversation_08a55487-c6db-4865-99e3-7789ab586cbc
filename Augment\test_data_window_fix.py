"""
Test Data Window Fix

This script tests if the data window fix is working correctly by comparing
the data used in vectorized vs standalone versions.
"""

import sys
import os
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_window():
    """Test if the data window is correctly implemented"""
    
    from shared_api_manager import get_api
    from intelligent_vectorized_backtester import IntelligentVectorizedBacktester
    from enhanced_nadarya_watson_signal import get_start_end_timestamps, live_data
    
    # Test parameters
    ticker = 'BATAINDIA'
    date = '24-06-2025'
    start_time = '09:15'
    test_time = '12:31'  # Test with 12:31 which should have lower band signal
    exchange = 'NSE'
    tokenid = '371'
    
    print("\n" + "="*80)
    print("🔍 TESTING DATA WINDOW FIX")
    print("="*80)
    
    # Get API
    api = get_api()
    
    # Test 1: Get data for standalone (09:15 to 12:31)
    print(f"\n1️⃣ STANDALONE DATA WINDOW (09:15 to {test_time})")
    print("-" * 50)
    
    start_timestamp, end_timestamp = get_start_end_timestamps(date, start_time, test_time)
    
    standalone_data = api.get_time_price_series(
        exchange='NSE', 
        token=tokenid, 
        starttime=start_timestamp, 
        endtime=end_timestamp, 
        interval=1
    )
    
    standalone_df = live_data(standalone_data)
    standalone_df = standalone_df.sort_values(by='time')
    
    print(f"📊 Standalone data shape: {standalone_df.shape}")
    print(f"📊 Standalone range: {standalone_df.index[0]} to {standalone_df.index[-1]}")
    print(f"📊 Standalone last close: {standalone_df['Close'].iloc[-1]:.2f}")
    
    # Test 2: Get full data for vectorized (09:15 to 13:00)
    print(f"\n2️⃣ VECTORIZED FULL DATA WINDOW (09:15 to 13:00)")
    print("-" * 50)
    
    full_end_time = '13:00'
    start_timestamp_full, end_timestamp_full = get_start_end_timestamps(date, start_time, full_end_time)
    
    full_data = api.get_time_price_series(
        exchange='NSE', 
        token=tokenid, 
        starttime=start_timestamp_full, 
        endtime=end_timestamp_full, 
        interval=1
    )
    
    full_df = live_data(full_data)
    full_df = full_df.sort_values(by='time')
    
    print(f"📊 Full data shape: {full_df.shape}")
    print(f"📊 Full range: {full_df.index[0]} to {full_df.index[-1]}")
    
    # Test 3: Extract window from full data (like vectorized should do)
    print(f"\n3️⃣ EXTRACTED WINDOW FROM FULL DATA")
    print("-" * 50)
    
    # Convert test_time to datetime for comparison
    test_datetime = datetime.strptime(f"{date} {test_time}", '%d-%m-%Y %H:%M')
    market_start = datetime.strptime(f"{date} {start_time}", '%d-%m-%Y %H:%M')
    
    # Extract window like vectorized version should
    extracted_df = full_df[
        (full_df.index >= market_start) & 
        (full_df.index <= test_datetime)
    ].copy()
    
    print(f"📊 Extracted data shape: {extracted_df.shape}")
    print(f"📊 Extracted range: {extracted_df.index[0]} to {extracted_df.index[-1]}")
    print(f"📊 Extracted last close: {extracted_df['Close'].iloc[-1]:.2f}")
    
    # Test 4: Compare data
    print(f"\n4️⃣ DATA COMPARISON")
    print("-" * 50)
    
    shapes_match = standalone_df.shape == extracted_df.shape
    ranges_match = (standalone_df.index[0] == extracted_df.index[0] and 
                   standalone_df.index[-1] == extracted_df.index[-1])
    closes_match = abs(standalone_df['Close'].iloc[-1] - extracted_df['Close'].iloc[-1]) < 0.01
    
    print(f"📊 Shapes match: {shapes_match} (Standalone: {standalone_df.shape}, Extracted: {extracted_df.shape})")
    print(f"📊 Ranges match: {ranges_match}")
    print(f"📊 Last closes match: {closes_match} (Standalone: {standalone_df['Close'].iloc[-1]:.2f}, Extracted: {extracted_df['Close'].iloc[-1]:.2f})")
    
    if shapes_match and ranges_match and closes_match:
        print(f"✅ SUCCESS: Data windows match perfectly!")
        
        # Test 5: Test Nadarya Watson on extracted data
        print(f"\n5️⃣ TESTING NADARYA WATSON ON EXTRACTED DATA")
        print("-" * 50)
        
        # Create a temporary backtester to test the calculation
        backtester = IntelligentVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start=start_time,
            end=full_end_time,
            date=date,
            tokenid=tokenid,
            enable_momentum_validation=False,  # Disable for pure test
            enable_realtime_detection=True
        )
        
        # Test the calculation
        signal = backtester._calculate_nadarya_watson_signal_vectorized(
            extracted_df, 
            1.75, 
            enable_momentum_validation=False,
            enable_realtime_detection=True,
            debug_time="12:31"
        )
        
        print(f"📊 Nadarya Watson result on extracted data: {signal}")
        
        if signal != 0:
            print(f"✅ SUCCESS: Nadarya Watson working on extracted data!")
        else:
            print(f"❌ ISSUE: Nadarya Watson still not working on extracted data")
            
    else:
        print(f"❌ ISSUE: Data windows don't match!")
        print(f"   This means the vectorized version is not extracting the correct data window")
    
    return {
        'shapes_match': shapes_match,
        'ranges_match': ranges_match,
        'closes_match': closes_match,
        'data_windows_match': shapes_match and ranges_match and closes_match
    }

def main():
    """Main execution function"""
    logger.info("🚀 Starting data window fix test...")
    
    result = test_data_window()
    
    if result['data_windows_match']:
        logger.info("✅ Data window fix is working correctly!")
    else:
        logger.info("❌ Data window fix needs more work")
    
    logger.info("🎉 Data window test completed!")

if __name__ == "__main__":
    main()
