"""
Test Enhanced Vectorized Backtester

This script tests the enhanced vectorized backtester to ensure it generates
the same signals as the standalone version while maintaining massive performance gains.
"""

import sys
import os
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_vectorized_vs_standalone():
    """Test enhanced vectorized backtester vs standalone for signal accuracy"""
    
    from shared_api_manager import get_api
    from intelligent_vectorized_backtester import IntelligentVectorizedBacktester
    from enhanced_nadarya_watson_signal import check_vander_enhanced
    from shared_sideways_signal_helper import check_sideways
    
    # Test parameters
    ticker = 'BATAINDIA'
    date = '24-06-2025'
    start_time = '09:15'
    end_time = '13:00'
    exchange = 'NSE'
    
    # Get token ID
    api = get_api()
    search_result = api.searchscrip(exchange='NSE', searchtext=ticker + '-EQ')
    if search_result and 'values' in search_result and search_result['values']:
        tokenid = search_result['values'][0]['token']
        logger.info(f"📊 Found token ID: {tokenid} for {ticker}")
    else:
        logger.error(f"❌ Could not find token ID for {ticker}")
        return
    
    print("\n" + "="*120)
    print("📊 ENHANCED VECTORIZED BACKTESTER vs STANDALONE COMPARISON")
    print("="*120)
    
    # Test 1: Enhanced Vectorized Backtester
    logger.info("🚀 Testing Enhanced Vectorized Backtester...")
    start_time_test = datetime.now()
    
    vectorized_backtester = IntelligentVectorizedBacktester(
        ticker=ticker,
        exchange=exchange,
        start=start_time,
        end=end_time,
        date=date,
        tokenid=tokenid,
        enable_momentum_validation=True,
        enable_realtime_detection=True
    )
    
    vectorized_results = vectorized_backtester.single_vectorized_signal_generation()
    vectorized_time = (datetime.now() - start_time_test).total_seconds()
    
    logger.info(f"✅ Vectorized backtester completed in {vectorized_time:.2f} seconds")
    logger.info(f"📊 Vectorized signals: {len(vectorized_backtester.signals)}")
    
    # Test 2: Standalone Signal Detection for Critical Moments
    logger.info("🔍 Testing Standalone Signal Detection for critical moments...")
    
    critical_moments = [
        {'end': '12:29', 'description': 'Lower band signal (12:29)'},
        {'end': '12:30', 'description': 'Lower band signal (12:30)'},
        {'end': '12:31', 'description': 'Lower band signal (12:31)'},
        {'end': '12:50', 'description': 'Upper band signal (12:49) - weak'},
        {'end': '12:51', 'description': 'Upper band signal (12:49) - weak continuation'},
        {'end': '12:54', 'description': 'Upper band signal - should be strong'},
        {'end': '12:55', 'description': 'Upper band signal - should be strong'},
    ]
    
    standalone_signals = []
    start_time_standalone = datetime.now()
    
    for moment in critical_moments:
        # Test sideways
        is_sideways, sideways_text = check_sideways(
            tokenid=tokenid,
            exchange=exchange,
            date_input=date,
            starttime_input=start_time,
            endtime_input=moment['end']
        )
        
        # Test enhanced Nadarya Watson
        is_vander, vander_text = check_vander_enhanced(
            tokenid=tokenid,
            exchange=exchange,
            date_input=date,
            starttime_input=start_time,
            endtime_input=moment['end'],
            enable_momentum_validation=True,
            enable_realtime_detection=True
        )
        
        # Final signal (Ver4 logic: both sideways and nadarya must be true)
        final_signal = is_sideways and is_vander
        
        standalone_signals.append({
            'time': moment['end'],
            'description': moment['description'],
            'sideways': is_sideways,
            'nadarya': is_vander,
            'final_signal': final_signal,
            'sideways_text': sideways_text,
            'vander_text': vander_text
        })
    
    standalone_time = (datetime.now() - start_time_standalone).total_seconds()
    
    logger.info(f"✅ Standalone testing completed in {standalone_time:.2f} seconds")
    
    # Compare Results
    print(f"\n📊 PERFORMANCE COMPARISON:")
    print(f"   Vectorized Time: {vectorized_time:.2f} seconds")
    print(f"   Standalone Time: {standalone_time:.2f} seconds")
    print(f"   Performance Improvement: {standalone_time/vectorized_time:.1f}x faster")
    
    print(f"\n📊 SIGNAL COMPARISON:")
    print(f"{'Time':<8} | {'Standalone':<10} | {'Vectorized':<10} | {'Match':<8} | {'Description':<30}")
    print("-" * 80)
    
    # Extract vectorized signals for comparison
    vectorized_signal_times = [signal['time'] for signal in vectorized_backtester.signals]
    
    matches = 0
    total_tests = len(standalone_signals)
    
    for signal in standalone_signals:
        time_str = signal['time']
        standalone_result = signal['final_signal']
        vectorized_result = time_str in vectorized_signal_times
        
        match = "✅" if standalone_result == vectorized_result else "❌"
        if standalone_result == vectorized_result:
            matches += 1
        
        print(f"{time_str:<8} | {str(standalone_result):<10} | {str(vectorized_result):<10} | {match:<8} | {signal['description']:<30}")
    
    accuracy = (matches / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n📊 ACCURACY ANALYSIS:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Matches: {matches}")
    print(f"   Accuracy: {accuracy:.1f}%")
    
    if accuracy >= 90:
        print(f"   🎯 EXCELLENT: High accuracy achieved!")
    elif accuracy >= 70:
        print(f"   ⚠️ GOOD: Acceptable accuracy, minor differences")
    else:
        print(f"   ❌ ISSUE: Low accuracy, significant differences detected")
    
    # Detailed Analysis
    print(f"\n📋 DETAILED STANDALONE SIGNAL ANALYSIS:")
    for signal in standalone_signals:
        print(f"\n{signal['time']} - {signal['description']}")
        print(f"   Sideways: {signal['sideways']} - {signal['sideways_text']}")
        print(f"   Nadarya: {signal['nadarya']} - {signal['vander_text']}")
        print(f"   Final Signal: {signal['final_signal']}")
    
    print(f"\n📋 VECTORIZED SIGNALS DETECTED:")
    for signal in vectorized_backtester.signals:
        print(f"   {signal['time']}: {signal['signal_type']} - {signal['reason']}")
    
    return {
        'vectorized_time': vectorized_time,
        'standalone_time': standalone_time,
        'performance_improvement': standalone_time/vectorized_time if vectorized_time > 0 else 0,
        'accuracy': accuracy,
        'vectorized_signals': len(vectorized_backtester.signals),
        'standalone_signals': sum(1 for s in standalone_signals if s['final_signal']),
        'matches': matches,
        'total_tests': total_tests
    }

def test_different_configurations():
    """Test different enhancement configurations"""
    
    print("\n" + "="*80)
    print("🧪 TESTING DIFFERENT ENHANCEMENT CONFIGURATIONS")
    print("="*80)
    
    from intelligent_vectorized_backtester import IntelligentVectorizedBacktester
    
    configurations = [
        {
            'name': 'Original (No Enhancements)',
            'momentum': False,
            'realtime': False
        },
        {
            'name': 'Real-time Only',
            'momentum': False,
            'realtime': True
        },
        {
            'name': 'Momentum Only',
            'momentum': True,
            'realtime': False
        },
        {
            'name': 'Enhanced (Both)',
            'momentum': True,
            'realtime': True
        }
    ]
    
    results = {}
    
    for config in configurations:
        logger.info(f"🔍 Testing {config['name']}...")
        
        backtester = IntelligentVectorizedBacktester(
            ticker='BATAINDIA',
            exchange='NSE',
            start='09:15',
            end='13:00',
            date='24-06-2025',
            tokenid='371',
            enable_momentum_validation=config['momentum'],
            enable_realtime_detection=config['realtime']
        )
        
        start_time = datetime.now()
        result = backtester.single_vectorized_signal_generation()
        execution_time = (datetime.now() - start_time).total_seconds()
        
        results[config['name']] = {
            'signals': len(backtester.signals),
            'time': execution_time,
            'success': result['success']
        }
        
        logger.info(f"✅ {config['name']}: {len(backtester.signals)} signals in {execution_time:.2f}s")
    
    print(f"\n📊 CONFIGURATION COMPARISON:")
    print(f"{'Configuration':<20} | {'Signals':<8} | {'Time':<8} | {'Status':<8}")
    print("-" * 50)
    
    for name, result in results.items():
        status = "✅" if result['success'] else "❌"
        print(f"{name:<20} | {result['signals']:<8} | {result['time']:<8.2f} | {status:<8}")
    
    return results

def main():
    """Main execution function"""
    logger.info("🚀 Starting Enhanced Vectorized Backtester tests...")
    
    # Test enhanced vectorized vs standalone
    comparison_results = test_enhanced_vectorized_vs_standalone()
    
    # Test different configurations
    config_results = test_different_configurations()
    
    logger.info("🎉 Enhanced Vectorized Backtester tests completed!")
    
    return {
        'comparison': comparison_results,
        'configurations': config_results
    }

if __name__ == "__main__":
    main()
