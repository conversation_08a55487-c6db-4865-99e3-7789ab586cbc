Title: Statistics - Pandas TA

URL Source: https://www.pandas-ta.dev/api/statistics/

Markdown Content:
* * *

entropy[#](https://www.pandas-ta.dev/api/statistics/#src.pandas_ta.statistics.entropy.entropy "Permanent link")
---------------------------------------------------------------------------------------------------------------

```
entropy(
    close: Series,
    length: Int = None,
    base: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Entropy

This indicator attempts to quantify the unpredictability of the data, or equivalently, its average information. It is a rolling entropy calculation.

Sources
*   [wikipedia](https://en.wikipedia.org/wiki/Entropy_(information_theory))

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `base` | `float` | Logarithmic Base. Default: `2` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

kurtosis[#](https://www.pandas-ta.dev/api/statistics/#src.pandas_ta.statistics.kurtosis.kurtosis "Permanent link")
------------------------------------------------------------------------------------------------------------------

```
kurtosis(
    close: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Rolling Kurtosis

Calculates a rolling Kurtosis.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `30` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Danger
Possible Data Leak

* * *

mad[#](https://www.pandas-ta.dev/api/statistics/#src.pandas_ta.statistics.mad.mad "Permanent link")
---------------------------------------------------------------------------------------------------

```
mad(
    close: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Rolling Mean Absolute Deviation

Calculates a rolling Mean Absolute Deviation (MAD).

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `30` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

```
median(
    close: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Rolling Median

Calculates a rolling Median.

Sources
*   [incrediblecharts](https://www.incrediblecharts.com/indicators/median_price.php)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `30` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

quantile[#](https://www.pandas-ta.dev/api/statistics/#src.pandas_ta.statistics.quantile.quantile "Permanent link")
------------------------------------------------------------------------------------------------------------------

```
quantile(
    close: Series,
    length: Int = None,
    q: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Rolling Quantile

Calculates a rolling Quantile.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `30` | `None` |
| `q` | `float` | The quantile. Default: `0.5` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

skew[#](https://www.pandas-ta.dev/api/statistics/#src.pandas_ta.statistics.skew.skew "Permanent link")
------------------------------------------------------------------------------------------------------

```
skew(
    close: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Rolling Skew

Calculates a rolling Skew.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `30` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Danger
Possible Data Leak

* * *

stdev[#](https://www.pandas-ta.dev/api/statistics/#src.pandas_ta.statistics.stdev.stdev "Permanent link")
---------------------------------------------------------------------------------------------------------

```
stdev(
    close: Series,
    length: Int = None,
    ddof: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Rolling Standard Deviation

Calculates a rolling Standard Deviation.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `30` | `None` |
| `ddof` | `int` | Delta Degrees of Freedom. Default: `1` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
*   TA Lib does not have a `ddof` parameter.
*   The divisor used in calculations is: `N - ddof`, where `N` is the number of elements. To use `ddof`, set `talib=False`.

* * *

tos_stdevall[#](https://www.pandas-ta.dev/api/statistics/#src.pandas_ta.statistics.tos_stdevall.tos_stdevall "Permanent link")
------------------------------------------------------------------------------------------------------------------------------

```
tos_stdevall(
    close: Series,
    length: Int = None,
    stds: List = None,
    ddof: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

TD Ameritrade's Think or Swim Standard Deviation All

This indicator returns the standard deviation(s) over all the bars or the last `n` (length) bars.

Sources
*   [thinkorswim](https://tlc.thinkorswim.com/center/reference/thinkScript/Functions/Statistical/StDevAll)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | Bars since current/last bar, Series[-1]. Default: `None` | `None` |
| `stds` | `list` | List of standard deviations in increasing order from the central Linear Regression line. Default: `[1,2,3]` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 7+ columns |

Note
*   TA Lib does not have a `ddof` parameter.
*   The divisor used in calculations is: `N - ddof`, where `N` is the number of elements. To use `ddof`, set `talib=False`.

Danger
Possible Data Leak

* * *

variance[#](https://www.pandas-ta.dev/api/statistics/#src.pandas_ta.statistics.variance.variance "Permanent link")
------------------------------------------------------------------------------------------------------------------

```
variance(
    close: Series,
    length: Int = None,
    ddof: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Rolling Variance

Calculates a rolling Variance.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `30` | `None` |
| `ddof` | `int` | Delta Degrees of Freedom. Default: `1` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
*   TA Lib does not have a `ddof` parameter.
*   The divisor used in calculations is: `N - ddof`, where `N` is the number of elements. To use `ddof`, set `talib=False`.

* * *

zscore[#](https://www.pandas-ta.dev/api/statistics/#src.pandas_ta.statistics.zscore.zscore "Permanent link")
------------------------------------------------------------------------------------------------------------

```
zscore(
    close: Series,
    length: Int = None,
    std: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Rolling Z Score

Calculates a rolling Z Score.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `30` | `None` |
| `std` | `float` | Number of deviation standards. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |
