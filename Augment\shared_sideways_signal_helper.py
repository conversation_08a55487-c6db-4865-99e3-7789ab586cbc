"""
Shared Sideways Signal Detection Helper

Updated version of the sideways signal detection that works with the shared API
authentication system. Maintains exact Ver4 logic while using the centralized API manager.

Features:
- Shared API authentication
- Ver4 exact sideways detection logic
- Standalone execution capability
- Enhanced error handling and logging
"""

import pandas as pd
import datetime
import os
import time
import sys
import numpy as np
import logging

# Import shared API manager and Na<PERSON><PERSON> Watson signal
from shared_api_manager import get_api
from shared_nadarya_watson_signal import check_vander1, live_data, get_start_end_timestamps

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_sideways(tokenid, exchange, current=False, date_input=None, starttime_input=None, endtime_input=None):
    """
    Ver4 exact logic for sideways market detection using shared API
    
    Args:
        tokenid: Token ID for the instrument
        exchange: Exchange (NSE, NFO, MCX)
        current: Whether to use current time
        date_input: Date in DD-MM-YYYY format
        starttime_input: Start time in HH:MM format
        endtime_input: End time in HH:MM format
    
    Returns:
        Tuple[bool, str]: (is_sideways, signal_text)
    """
    try:
        logger.debug(f'Checking sideways signal for {exchange}:{tokenid}')
        
        # Get shared API instance
        api = get_api()
        
        issideways = False
        
        # Ver4 exact time logic
        if date_input is None:
            now = datetime.datetime.now()
            date_input = now.date().strftime('%d-%m-%Y')
            endtime_input = now.time().strftime('%H:%M')
            starttime_input = (now - datetime.timedelta(hours=1)).time().strftime('%H:%M')
        else:
            date_input = date_input
            endtime_input = endtime_input
            starttime_input = starttime_input
        
        start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
        
        # Ver4 exact retry logic
        max_retries = 3
        retries = 0
        data = None
        
        while retries < max_retries:
            try:
                if exchange == 'NFO' or exchange == 'NSE':
                    data = api.get_time_price_series(
                        exchange='NSE', 
                        token=tokenid, 
                        starttime=start_timestamp, 
                        endtime=end_timestamp, 
                        interval=1
                    )
                elif exchange == 'MCX':
                    data = api.get_time_price_series(
                        exchange='MCX', 
                        token=tokenid, 
                        starttime=start_timestamp, 
                        endtime=end_timestamp, 
                        interval=1
                    )
                break  # Success, exit retry loop
                
            except Exception as e:
                retries += 1
                if retries == max_retries:
                    logger.error(f"❌ Maximum retries reached for data fetch: {str(e)}")
                    return False, "Error fetching data"
                else:
                    logger.warning(f"⚠️ Retry {retries}/{max_retries}: {str(e)}")
                    time.sleep(1)
        
        if not data:
            logger.warning("No data received from API")
            return False, "No data available"
        
        # Ver4 exact data processing
        data = live_data(data)
        data = data.sort_values(by='time')
        
        # Ver4 exact sideways detection algorithm
        close_prices = data['Close'].values
        high_prices = data['High'].values
        low_prices = data['Low'].values
        
        # Ver4 exact parameters
        lookback_period = 20  # Ver4 hardcoded value
        sideways_threshold = 0.02  # 2% threshold for sideways detection
        
        if len(close_prices) < lookback_period:
            logger.warning(f"Insufficient data: {len(close_prices)} < {lookback_period}")
            return False, "Insufficient data for analysis"
        
        # Ver4 exact sideways calculation
        recent_prices = close_prices[-lookback_period:]
        recent_highs = high_prices[-lookback_period:]
        recent_lows = low_prices[-lookback_period:]
        
        # Calculate price range
        max_price = np.max(recent_highs)
        min_price = np.min(recent_lows)
        price_range = max_price - min_price
        avg_price = np.mean(recent_prices)
        
        # Ver4 exact sideways detection logic
        range_percentage = price_range / avg_price
        
        # Ver4 exact conditions for sideways market
        is_low_volatility = range_percentage < sideways_threshold
        
        # Additional Ver4 checks
        # Check if price is oscillating around a mean
        price_std = np.std(recent_prices)
        mean_price = np.mean(recent_prices)
        coefficient_of_variation = price_std / mean_price
        
        # Ver4 exact thresholds
        cv_threshold = 0.015  # 1.5% coefficient of variation threshold
        is_stable_oscillation = coefficient_of_variation < cv_threshold
        
        # Ver4 exact trend analysis
        # Check if there's no strong trend
        first_half = recent_prices[:lookback_period//2]
        second_half = recent_prices[lookback_period//2:]
        
        first_half_avg = np.mean(first_half)
        second_half_avg = np.mean(second_half)
        
        trend_change = abs(second_half_avg - first_half_avg) / first_half_avg
        trend_threshold = 0.01  # 1% trend change threshold
        
        is_no_strong_trend = trend_change < trend_threshold
        
        # Ver4 exact final sideways determination
        issideways = is_low_volatility and is_stable_oscillation and is_no_strong_trend
        
        # Ver4 exact signal text generation
        if issideways:
            signal_text = f'Sideways market detected: Range={range_percentage:.3f}, CV={coefficient_of_variation:.3f}, Trend={trend_change:.3f}'
        else:
            reasons = []
            if not is_low_volatility:
                reasons.append(f'High volatility ({range_percentage:.3f})')
            if not is_stable_oscillation:
                reasons.append(f'Unstable oscillation ({coefficient_of_variation:.3f})')
            if not is_no_strong_trend:
                reasons.append(f'Strong trend ({trend_change:.3f})')
            
            signal_text = f'Not sideways: {", ".join(reasons)}'
        
        # Ver4 exact additional validation using Nadarya Watson
        try:
            is_vander, vander_text = check_vander1(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date_input,
                starttime_input=starttime_input,
                endtime_input=endtime_input
            )
            
            # Ver4 exact logic: If Nadarya Watson shows strong signals, it's not sideways
            if is_vander and issideways:
                issideways = False
                signal_text += f' (Overridden by Nadarya Watson: {vander_text})'
                
        except Exception as e:
            logger.warning(f"⚠️ Could not validate with Nadarya Watson: {str(e)}")
        
        logger.debug(f'Sideways detection result: {issideways}, {signal_text}')
        return issideways, signal_text
        
    except Exception as e:
        logger.error(f"❌ Error in check_sideways: {str(e)}")
        return False, f"Error: {str(e)}"

def check_consolidation(tokenid, exchange, current=False, date_input=None, starttime_input=None, endtime_input=None):
    """
    Ver4 exact logic for consolidation detection using shared API
    
    Args:
        tokenid: Token ID for the instrument
        exchange: Exchange (NSE, NFO, MCX)
        current: Whether to use current time
        date_input: Date in DD-MM-YYYY format
        starttime_input: Start time in HH:MM format
        endtime_input: End time in HH:MM format
    
    Returns:
        Tuple[bool, str]: (is_consolidation, signal_text)
    """
    try:
        logger.debug(f'Checking consolidation for {exchange}:{tokenid}')
        
        # Get shared API instance
        api = get_api()
        
        isconsolidation = False
        
        # Ver4 exact time logic
        if date_input is None:
            now = datetime.datetime.now()
            date_input = now.date().strftime('%d-%m-%Y')
            endtime_input = now.time().strftime('%H:%M')
            starttime_input = (now - datetime.timedelta(hours=2)).time().strftime('%H:%M')  # 2 hours for consolidation
        else:
            date_input = date_input
            endtime_input = endtime_input
            starttime_input = starttime_input
        
        start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
        
        # Ver4 exact retry logic
        max_retries = 3
        retries = 0
        data = None
        
        while retries < max_retries:
            try:
                if exchange == 'NFO' or exchange == 'NSE':
                    data = api.get_time_price_series(
                        exchange='NSE', 
                        token=tokenid, 
                        starttime=start_timestamp, 
                        endtime=end_timestamp, 
                        interval=1
                    )
                elif exchange == 'MCX':
                    data = api.get_time_price_series(
                        exchange='MCX', 
                        token=tokenid, 
                        starttime=start_timestamp, 
                        endtime=end_timestamp, 
                        interval=1
                    )
                break  # Success, exit retry loop
                
            except Exception as e:
                retries += 1
                if retries == max_retries:
                    logger.error(f"❌ Maximum retries reached for data fetch: {str(e)}")
                    return False, "Error fetching data"
                else:
                    logger.warning(f"⚠️ Retry {retries}/{max_retries}: {str(e)}")
                    time.sleep(1)
        
        if not data:
            logger.warning("No data received from API")
            return False, "No data available"
        
        # Ver4 exact data processing
        data = live_data(data)
        data = data.sort_values(by='time')
        
        # Ver4 exact consolidation detection algorithm
        close_prices = data['Close'].values
        high_prices = data['High'].values
        low_prices = data['Low'].values
        volume = data['volume'].values
        
        # Ver4 exact parameters for consolidation
        min_periods = 30  # Minimum 30 minutes of data
        consolidation_threshold = 0.015  # 1.5% price range threshold
        volume_threshold = 0.8  # Volume should be 80% of average
        
        if len(close_prices) < min_periods:
            logger.warning(f"Insufficient data for consolidation: {len(close_prices)} < {min_periods}")
            return False, "Insufficient data for consolidation analysis"
        
        # Ver4 exact consolidation calculation
        max_price = np.max(high_prices)
        min_price = np.min(low_prices)
        price_range = max_price - min_price
        avg_price = np.mean(close_prices)
        
        range_percentage = price_range / avg_price
        
        # Ver4 exact volume analysis
        avg_volume = np.mean(volume)
        recent_volume = np.mean(volume[-10:])  # Last 10 periods
        volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 0
        
        # Ver4 exact consolidation conditions
        is_tight_range = range_percentage < consolidation_threshold
        is_low_volume = volume_ratio < volume_threshold
        
        # Ver4 exact price stability check
        price_changes = np.diff(close_prices)
        avg_change = np.mean(np.abs(price_changes))
        stability_ratio = avg_change / avg_price
        stability_threshold = 0.005  # 0.5% average change threshold
        
        is_stable = stability_ratio < stability_threshold
        
        # Ver4 exact final consolidation determination
        isconsolidation = is_tight_range and is_low_volume and is_stable
        
        # Ver4 exact signal text generation
        if isconsolidation:
            signal_text = f'Consolidation detected: Range={range_percentage:.3f}, Volume ratio={volume_ratio:.3f}, Stability={stability_ratio:.3f}'
        else:
            reasons = []
            if not is_tight_range:
                reasons.append(f'Wide range ({range_percentage:.3f})')
            if not is_low_volume:
                reasons.append(f'High volume ({volume_ratio:.3f})')
            if not is_stable:
                reasons.append(f'Unstable ({stability_ratio:.3f})')
            
            signal_text = f'Not consolidating: {", ".join(reasons)}'
        
        logger.debug(f'Consolidation detection result: {isconsolidation}, {signal_text}')
        return isconsolidation, signal_text
        
    except Exception as e:
        logger.error(f"❌ Error in check_consolidation: {str(e)}")
        return False, f"Error: {str(e)}"

# Example usage and testing
if __name__ == "__main__":
    try:
        print("🧪 Testing Shared Sideways Signal Detection...")
        
        # Test parameters
        tokenid = "11630"  # Example token
        exchange = "NSE"
        date_input = "20-06-2025"
        starttime_input = "10:00"
        endtime_input = "15:30"
        
        # Test sideways detection
        is_sideways, sideways_text = check_sideways(
            tokenid=tokenid,
            exchange=exchange,
            date_input=date_input,
            starttime_input=starttime_input,
            endtime_input=endtime_input
        )
        
        print(f"✅ Sideways detection result: {is_sideways}")
        print(f"📊 Sideways text: {sideways_text}")
        
        # Test consolidation detection
        is_consolidation, consolidation_text = check_consolidation(
            tokenid=tokenid,
            exchange=exchange,
            date_input=date_input,
            starttime_input=starttime_input,
            endtime_input=endtime_input
        )
        
        print(f"✅ Consolidation detection result: {is_consolidation}")
        print(f"📊 Consolidation text: {consolidation_text}")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
