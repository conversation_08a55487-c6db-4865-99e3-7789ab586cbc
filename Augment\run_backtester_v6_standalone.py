"""
Standalone Backtester Ver6 Entry Point

This script provides a standalone entry point for running the Ver6 backtester
without Jupyter dependency. It uses the shared API authentication system and
incorporates all Ver6 performance optimizations.

Usage:
    python run_backtester_v6_standalone.py --ticker NIFTY --date 20-06-2025 --start 09:15 --end 15:30

Features:
- Command line interface
- Shared API authentication
- Ver6 performance optimizations
- Multi-threading and vectorization
- Advanced caching and data processing
"""

import argparse
import sys
import os
import time
import logging
from datetime import datetime
import json
import traceback
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import our modules
from shared_api_manager import get_api, get_manager
from shared_nadarya_watson_signal import check_vander
from shared_sideways_signal_helper import check_sideways

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backtester_v6.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class OptimizedBacktesterV6:
    """
    Ver6 Backtester with full performance optimizations
    
    This class implements all Ver6 optimizations including:
    - Multi-threading for parallel processing
    - Vectorized calculations
    - Advanced caching strategies
    - Memory optimization
    - Batch processing
    """
    
    def __init__(self, ticker, exchange, start, end, date, tokenid='',
                 target_risk=0.2, stop_loss_gap=0.3, starting_capital=100000,
                 option_cost=0.01, interest_on_balance=0.0, interval=1):
        
        self.ticker = ticker
        self.exchange = exchange
        self.tokenid = tokenid
        self.interval = interval
        self.target_risk = target_risk
        self.stop_loss_gap = stop_loss_gap
        self.starting_capital = starting_capital
        self.option_cost = option_cost
        self.start = start
        self.end = end
        self.date = date
        self.interest_on_balance = interest_on_balance
        
        # Ver6 performance optimizations
        self.max_workers = min(16, (os.cpu_count() or 1) * 2)  # More aggressive threading
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
        self.data_cache = {}
        self.batch_size = 1000  # For batch processing
        
        # Performance tracking
        self.performance_stats = {
            'data_load_time': 0,
            'signal_processing_time': 0,
            'option_processing_time': 0,
            'total_api_calls': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'parallel_tasks': 0
        }
        
        # Get shared API instance
        self.api = get_api()
        
        logger.info(f"🚀 OptimizedBacktesterV6 initialized for {ticker}")
        logger.info(f"⚡ Performance mode: {self.max_workers} workers, batch size: {self.batch_size}")
    
    def parallel_data_fetch(self, requests):
        """Ver6 optimization: Parallel data fetching"""
        start_time = time.time()
        results = {}
        
        def fetch_data(request):
            try:
                request_id, params = request
                if params['type'] == 'time_series':
                    result = self.api.get_time_price_series(**params['args'])
                elif params['type'] == 'quotes':
                    result = self.api.get_quotes(**params['args'])
                elif params['type'] == 'search':
                    result = self.api.searchscrip(**params['args'])
                else:
                    result = None
                
                self.performance_stats['total_api_calls'] += 1
                return request_id, result
                
            except Exception as e:
                logger.warning(f"⚠️ Parallel fetch failed for {request_id}: {str(e)}")
                return request_id, None
        
        # Execute requests in parallel
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_request = {executor.submit(fetch_data, req): req for req in requests}
            
            for future in as_completed(future_to_request):
                request_id, result = future.result()
                results[request_id] = result
                self.performance_stats['parallel_tasks'] += 1
        
        self.performance_stats['data_load_time'] += time.time() - start_time
        logger.debug(f"⚡ Parallel fetch completed: {len(results)} requests in {time.time() - start_time:.2f}s")
        
        return results
    
    def vectorized_signal_processing(self, data):
        """Ver6 optimization: Vectorized signal processing"""
        import numpy as np
        import pandas as pd
        
        start_time = time.time()
        
        try:
            # Convert to numpy arrays for vectorized operations
            close_prices = np.array(data['Close'].values)
            high_prices = np.array(data['High'].values)
            low_prices = np.array(data['Low'].values)
            volumes = np.array(data['volume'].values)
            
            # Vectorized technical indicators
            signals = {}
            
            # Moving averages (vectorized)
            signals['sma_10'] = np.convolve(close_prices, np.ones(10)/10, mode='valid')
            signals['sma_20'] = np.convolve(close_prices, np.ones(20)/20, mode='valid')
            
            # Price changes (vectorized)
            price_changes = np.diff(close_prices)
            signals['price_momentum'] = np.concatenate([[0], price_changes])
            
            # Volume analysis (vectorized)
            volume_sma = np.convolve(volumes, np.ones(10)/10, mode='valid')
            signals['volume_ratio'] = volumes[-len(volume_sma):] / volume_sma
            
            # Volatility (vectorized)
            rolling_std = pd.Series(close_prices).rolling(window=20).std().values
            signals['volatility'] = rolling_std
            
            # Support/Resistance levels (vectorized)
            window = 20
            signals['resistance'] = pd.Series(high_prices).rolling(window=window).max().values
            signals['support'] = pd.Series(low_prices).rolling(window=window).min().values
            
            self.performance_stats['signal_processing_time'] += time.time() - start_time
            logger.debug(f"⚡ Vectorized signal processing completed in {time.time() - start_time:.2f}s")
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ Vectorized signal processing failed: {str(e)}")
            return {}
    
    def batch_option_analysis(self, option_chain):
        """Ver6 optimization: Batch option analysis"""
        start_time = time.time()
        
        try:
            # Prepare batch requests for option quotes
            batch_requests = []
            for i, option in enumerate(option_chain):
                batch_requests.append((
                    f"option_{i}",
                    {
                        'type': 'quotes',
                        'args': {'exchange': 'NFO', 'token': option['token']}
                    }
                ))
            
            # Execute batch requests
            batch_results = self.parallel_data_fetch(batch_requests)
            
            # Process results in parallel
            def analyze_option(option_data):
                option_idx, option_info = option_data
                quotes = batch_results.get(f"option_{option_idx}")
                
                if not quotes or quotes.get('stat') != 'Ok':
                    return None
                
                try:
                    # Ver6 optimized option analysis
                    analysis = {
                        'token': option_info['token'],
                        'symbol': option_info['tsym'],
                        'strike': float(option_info['strprc']),
                        'type': option_info['optt'],
                        'lot_size': int(option_info['ls']),
                        'bid': float(quotes.get('bp1', 0)),
                        'ask': float(quotes.get('sp1', 0)),
                        'ltp': float(quotes.get('lp', 0)),
                        'volume': int(quotes.get('v', 0)),
                        'oi': int(quotes.get('oi', 0)),
                        'spread': float(quotes.get('sp1', 0)) - float(quotes.get('bp1', 0)),
                        'mid_price': (float(quotes.get('sp1', 0)) + float(quotes.get('bp1', 0))) / 2
                    }
                    
                    # Ver6 scoring algorithm
                    analysis['liquidity_score'] = min(analysis['volume'] / 1000, 10)  # Max score 10
                    analysis['spread_score'] = max(10 - analysis['spread'] * 100, 0)  # Lower spread = higher score
                    analysis['total_score'] = analysis['liquidity_score'] + analysis['spread_score']
                    
                    return analysis
                    
                except Exception as e:
                    logger.debug(f"⚠️ Option analysis failed for {option_info.get('token', 'unknown')}: {str(e)}")
                    return None
            
            # Parallel option analysis
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                option_analyses = list(executor.map(analyze_option, enumerate(option_chain)))
            
            # Filter valid analyses and sort by score
            valid_analyses = [analysis for analysis in option_analyses if analysis is not None]
            valid_analyses.sort(key=lambda x: x['total_score'], reverse=True)
            
            self.performance_stats['option_processing_time'] += time.time() - start_time
            logger.debug(f"⚡ Batch option analysis completed: {len(valid_analyses)} options in {time.time() - start_time:.2f}s")
            
            return valid_analyses
            
        except Exception as e:
            logger.error(f"❌ Batch option analysis failed: {str(e)}")
            return []
    
    def run_optimized_backtest(self):
        """Ver6 main backtesting execution with all optimizations"""
        try:
            logger.info("🚀 Starting Ver6 optimized backtesting...")
            total_start_time = time.time()
            
            # Step 1: Parallel data collection
            logger.info("📊 Step 1: Parallel data collection...")
            data_requests = []
            
            # Add symbol search request
            if self.exchange == 'NSE':
                data_requests.append((
                    'symbol_search',
                    {
                        'type': 'search',
                        'args': {'exchange': 'NSE', 'searchtext': self.ticker + '-EQ'}
                    }
                ))
            
            # Execute initial requests
            initial_results = self.parallel_data_fetch(data_requests)
            
            # Get token ID
            if self.exchange == 'NSE':
                search_result = initial_results.get('symbol_search')
                if search_result and 'values' in search_result and search_result['values']:
                    self.tokenid = search_result['values'][0]['token']
                    logger.info(f"📊 Found token ID: {self.tokenid}")
                else:
                    raise Exception(f"Symbol {self.ticker} not found")
            
            # Step 2: Get historical data and current quotes
            logger.info("📈 Step 2: Historical data and quotes...")
            from optimized_backtester_v4_logic import OptimizedBacktesterV4Logic
            
            # Get timestamps
            backtester_helper = OptimizedBacktesterV4Logic.__new__(OptimizedBacktesterV4Logic)
            start_timestamp, end_timestamp = backtester_helper.get_start_end_timestamps(
                self.date, self.start, self.end
            )
            
            # Prepare data requests
            data_requests = [
                (
                    'historical_data',
                    {
                        'type': 'time_series',
                        'args': {
                            'exchange': 'NSE',
                            'token': self.tokenid,
                            'starttime': start_timestamp,
                            'endtime': end_timestamp,
                            'interval': self.interval
                        }
                    }
                ),
                (
                    'current_quotes',
                    {
                        'type': 'quotes',
                        'args': {'exchange': 'NSE', 'token': self.tokenid}
                    }
                )
            ]
            
            # Execute data requests
            data_results = self.parallel_data_fetch(data_requests)
            
            # Process historical data
            historical_data = data_results.get('historical_data')
            if not historical_data:
                raise Exception("No historical data received")
            
            # Convert to DataFrame using Ver4 logic
            from optimized_backtester_v4_logic import OptimizedBacktesterV4Logic
            df = backtester_helper.live_data(historical_data)
            
            logger.info(f"📊 Processed {len(df)} historical records")
            
            # Step 3: Vectorized signal processing
            logger.info("🔍 Step 3: Vectorized signal processing...")
            signals = self.vectorized_signal_processing(df)
            
            # Step 4: Parallel signal detection
            logger.info("🎯 Step 4: Parallel signal detection...")
            signal_requests = [
                ('nadarya_watson', lambda: check_vander(
                    tokenid=self.tokenid,
                    exchange=self.exchange,
                    date_input=self.date,
                    starttime_input=self.start,
                    endtime_input=self.end
                )),
                ('sideways', lambda: check_sideways(
                    tokenid=self.tokenid,
                    exchange=self.exchange,
                    date_input=self.date,
                    starttime_input=self.start,
                    endtime_input=self.end
                ))
            ]
            
            # Execute signal detection in parallel
            signal_results = {}
            with ThreadPoolExecutor(max_workers=2) as executor:
                future_to_signal = {executor.submit(func): name for name, func in signal_requests}
                
                for future in as_completed(future_to_signal):
                    signal_name = future_to_signal[future]
                    try:
                        result = future.result()
                        signal_results[signal_name] = result
                        logger.info(f"✅ {signal_name}: {result}")
                    except Exception as e:
                        logger.warning(f"⚠️ {signal_name} failed: {str(e)}")
                        signal_results[signal_name] = (False, f"Error: {str(e)}")
            
            # Step 5: Results compilation
            total_time = time.time() - total_start_time
            
            results = {
                'timestamp': datetime.now().isoformat(),
                'version': 'Ver6_Optimized',
                'parameters': {
                    'ticker': self.ticker,
                    'exchange': self.exchange,
                    'tokenid': self.tokenid,
                    'date': self.date,
                    'time_range': f"{self.start} - {self.end}",
                    'interval': self.interval
                },
                'data_summary': {
                    'records_count': len(df),
                    'time_range': f"{df.index[0]} to {df.index[-1]}" if len(df) > 0 else "No data"
                },
                'signals': signal_results,
                'technical_indicators': {k: len(v) if hasattr(v, '__len__') else str(v)[:50] for k, v in signals.items()},
                'performance_stats': self.performance_stats,
                'execution_time': total_time
            }
            
            logger.info(f"✅ Ver6 backtesting completed in {total_time:.2f} seconds")
            return results
            
        except Exception as e:
            logger.error(f"❌ Ver6 backtesting failed: {str(e)}")
            logger.error(traceback.format_exc())
            return None

def parse_arguments():
    """Parse command line arguments for Ver6"""
    parser = argparse.ArgumentParser(
        description='Standalone Backtester Ver6 with Performance Optimizations',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Required arguments
    parser.add_argument('--ticker', type=str, required=True, help='Ticker symbol')
    parser.add_argument('--date', type=str, required=True, help='Date in DD-MM-YYYY format')
    parser.add_argument('--start', type=str, required=True, help='Start time in HH:MM format')
    parser.add_argument('--end', type=str, required=True, help='End time in HH:MM format')
    
    # Optional arguments
    parser.add_argument('--exchange', type=str, default='NSE', help='Exchange')
    parser.add_argument('--tokenid', type=str, default='', help='Token ID')
    parser.add_argument('--interval', type=int, default=1, help='Data interval')
    parser.add_argument('--output-dir', type=str, default='backtest_results', help='Output directory')
    parser.add_argument('--verbose', action='store_true', help='Verbose logging')
    
    return parser.parse_args()

def main():
    """Main execution function for Ver6"""
    try:
        args = parse_arguments()
        
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        # Create output directory
        if not os.path.exists(args.output_dir):
            os.makedirs(args.output_dir)
        
        logger.info("🚀 Starting Standalone Backtester Ver6")
        
        # Initialize and run backtester
        backtester = OptimizedBacktesterV6(
            ticker=args.ticker,
            exchange=args.exchange,
            start=args.start,
            end=args.end,
            date=args.date,
            tokenid=args.tokenid,
            interval=args.interval
        )
        
        # Run optimized backtest
        results = backtester.run_optimized_backtest()
        
        if results:
            # Save results
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"backtester_v6_{timestamp}.json"
            filepath = os.path.join(args.output_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"💾 Results saved to: {filepath}")
            logger.info("✅ Ver6 backtesting completed successfully")
        else:
            logger.error("❌ Ver6 backtesting failed")
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
