"""
Test script for Technical Indicators Analyzer

This script demonstrates the usage of the Technical Indicators Analyzer
with different modes and methods.
"""

import sys
import os
import pandas as pd
import pandas_ta as ta
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from technical_indicators_analyzer import TechnicalIndicatorsAnalyzer

def create_sample_data():
    """Create sample OHLCV data for testing"""
    import numpy as np
    
    # Create sample data with 100 candles
    np.random.seed(42)
    dates = pd.date_range(start='2025-06-24 09:15', periods=100, freq='1min')
    
    # Generate realistic price data
    base_price = 1000
    price_changes = np.random.normal(0, 0.5, 100)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] + change
        prices.append(max(new_price, 1))  # Ensure positive prices
    
    # Create OHLCV data
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        high = price + abs(np.random.normal(0, 0.3))
        low = price - abs(np.random.normal(0, 0.3))
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': close_price,
            'Volume': volume
        })
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_basic_functionality():
    """Test basic functionality with sample data"""
    print("🧪 Testing Basic Functionality")
    print("=" * 50)
    
    # Create sample data
    sample_data = create_sample_data()
    print(f"✅ Created sample data with {len(sample_data)} candles")
    
    # Initialize analyzer
    analyzer = TechnicalIndicatorsAnalyzer()
    
    # Test different methods
    methods = ['standard', 'extension', 'study', 'strategy_common']
    
    for method in methods:
        print(f"\n🔧 Testing {method} method...")
        try:
            result = analyzer._analyze_dataframe(sample_data, method)
            if 'error' in result:
                print(f"❌ Error in {method}: {result['error']}")
            else:
                indicators_count = len(result.get('indicators', {}))
                print(f"✅ {method}: {indicators_count} indicators calculated")
                
                # Show a few sample indicators
                if indicators_count > 0:
                    sample_indicators = list(result['indicators'].keys())[:5]
                    print(f"   Sample indicators: {', '.join(sample_indicators)}")
        except Exception as e:
            print(f"❌ Exception in {method}: {str(e)}")

def test_specific_indicators():
    """Test specific indicators calculation"""
    print("\n🧪 Testing Specific Indicators")
    print("=" * 50)
    
    # Create sample data
    sample_data = create_sample_data()
    
    # Test individual indicators
    close = sample_data['Close']
    high = sample_data['High']
    low = sample_data['Low']
    volume = sample_data['Volume']
    
    indicators_to_test = [
        ('SMA 20', lambda: ta.sma(close, length=20)),
        ('EMA 20', lambda: ta.ema(close, length=20)),
        ('RSI 14', lambda: ta.rsi(close, length=14)),
        ('MACD', lambda: ta.macd(close)),
        ('Bollinger Bands', lambda: ta.bbands(close, length=20)),
        ('ATR 14', lambda: ta.atr(high, low, close, length=14)),
        ('ADX 14', lambda: ta.adx(high, low, close, length=14)),
        ('OBV', lambda: ta.obv(close, volume)),
    ]
    
    for name, calc_func in indicators_to_test:
        try:
            result = calc_func()
            if result is not None:
                if isinstance(result, pd.DataFrame):
                    print(f"✅ {name}: {len(result.columns)} columns, {len(result)} rows")
                else:
                    print(f"✅ {name}: {len(result)} values")
            else:
                print(f"❌ {name}: No result")
        except Exception as e:
            print(f"❌ {name}: Error - {str(e)}")

def test_categories_and_indicators():
    """Test categories and indicators listing"""
    print("\n🧪 Testing Categories and Indicators")
    print("=" * 50)
    
    analyzer = TechnicalIndicatorsAnalyzer()
    
    print(f"📂 Available categories: {len(analyzer.categories)}")
    for category in analyzer.categories:
        print(f"   - {category}")
    
    print(f"\n📊 Total indicators available: {len(analyzer.all_indicators)}")
    print("Sample indicators:")
    for indicator in analyzer.all_indicators[:10]:
        print(f"   - {indicator}")
    
    if len(analyzer.all_indicators) > 10:
        print(f"   ... and {len(analyzer.all_indicators) - 10} more")

def test_study_creation():
    """Test custom study creation or manual indicator application"""
    print("\n🧪 Testing Custom Study Creation")
    print("=" * 50)

    # Create sample data
    sample_data = create_sample_data()

    # Check if Study is available
    if hasattr(ta, 'Study'):
        # Create custom study
        custom_study = ta.Study(
            name="Test Study",
            description="Test study for demonstration",
            cores=0,
            ta=[
                {"kind": "sma", "length": 10},
                {"kind": "sma", "length": 20},
                {"kind": "ema", "length": 12},
                {"kind": "rsi", "length": 14},
                {"kind": "macd"},
                {"kind": "bbands", "length": 20},
                {"kind": "atr", "length": 14}
            ]
        )

        print(f"📋 Study created: {custom_study.name}")
        print(f"📝 Description: {custom_study.description}")
        print(f"🔧 Indicators: {len(custom_study.ta)}")

        # Apply study to data
        try:
            df = sample_data.copy()
            df.ta.study(custom_study, verbose=False)

            # Count new columns
            original_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            new_cols = [col for col in df.columns if col not in original_cols]

            print(f"✅ Study applied successfully")
            print(f"📊 New columns created: {len(new_cols)}")
            print(f"   Sample columns: {', '.join(new_cols[:5])}")

        except Exception as e:
            print(f"❌ Error applying study: {str(e)}")
    else:
        print("⚠️ Study class not available in this pandas-ta version")
        print("📝 Testing manual indicator application instead")

        # Apply indicators manually
        try:
            df = sample_data.copy()

            # Apply indicators using extension method
            df.ta.sma(length=10, append=True)
            df.ta.sma(length=20, append=True)
            df.ta.ema(length=12, append=True)
            df.ta.rsi(length=14, append=True)
            df.ta.macd(append=True)
            df.ta.bbands(length=20, append=True)
            df.ta.atr(length=14, append=True)

            # Count new columns
            original_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            new_cols = [col for col in df.columns if col not in original_cols]

            print(f"✅ Manual indicators applied successfully")
            print(f"📊 New columns created: {len(new_cols)}")
            print(f"   Sample columns: {', '.join(new_cols[:5])}")

        except Exception as e:
            print(f"❌ Error applying manual indicators: {str(e)}")

def main():
    """Main test function"""
    print("🚀 Technical Indicators Analyzer - Test Suite")
    print("=" * 60)
    
    try:
        # Test basic functionality
        test_basic_functionality()
        
        # Test specific indicators
        test_specific_indicators()
        
        # Test categories and indicators
        test_categories_and_indicators()
        
        # Test study creation
        test_study_creation()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed!")
        print("\n💡 Usage Examples:")
        print("   # Analyze signals from backtester")
        print("   python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025")
        print("\n   # Analyze specific candles")
        print("   python technical_indicators_analyzer.py --mode candles --ticker BATAINDIA --exchange BSE --date 24-06-2025 --times '12:23,15:42'")
        print("\n   # Analyze time period")
        print("   python technical_indicators_analyzer.py --mode period --ticker BATAINDIA --exchange BSE --date 24-06-2025 --start-time '10:15' --end-time '15:00'")
        print("\n   # Analyze full market session")
        print("   python technical_indicators_analyzer.py --mode full --ticker BATAINDIA --exchange BSE --date 24-06-2025")
        
    except Exception as e:
        print(f"❌ Test suite failed: {str(e)}")

if __name__ == "__main__":
    main()
