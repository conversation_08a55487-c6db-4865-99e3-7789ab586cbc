"""
Signal Accuracy Comparison Tool

This script compares signals generated by the vectorized approach vs the original
minute-by-minute approach to validate 100% Ver4 logic preservation.

Usage:
    python compare_signal_accuracy.py --ticker BATAINDIA --date 20-06-2025 --start 12:00 --end 12:30

Features:
✅ Side-by-side signal comparison
✅ Performance metrics comparison
✅ Detailed accuracy validation
✅ Position opening logic verification
"""

import argparse
import sys
import os
import logging
from datetime import datetime
import json
import pandas as pd

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import both approaches
from minute_by_minute_signal_generator import MinuteByMinuteSignalGenerator
from vectorized_backtester_v4_logic import VectorizedBacktesterV4Logic
from shared_api_manager import get_api

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SignalAccuracyComparator:
    """
    🔍 SIGNAL ACCURACY COMPARATOR
    
    Compares signals from vectorized vs minute-by-minute approaches to validate
    100% Ver4 logic preservation.
    """
    
    def __init__(self, ticker: str, exchange: str, start: str, end: str, 
                 date: str, tokenid: str = ""):
        self.ticker = ticker
        self.exchange = exchange
        self.start = start
        self.end = end
        self.date = date
        self.tokenid = tokenid
        
        self.minute_by_minute_results = None
        self.vectorized_results = None
        self.comparison_results = None
        
        logger.info(f"🔍 Initialized Signal Accuracy Comparator")
        logger.info(f"📊 Ticker: {ticker}, Period: {start}-{end}, Date: {date}")
        
    def run_comparison(self) -> dict:
        """
        🎯 RUN COMPLETE SIGNAL ACCURACY COMPARISON
        
        Executes both approaches and compares their results.
        """
        logger.info("🔄 Starting Signal Accuracy Comparison...")
        logger.info("=" * 80)
        
        try:
            # Get token if not provided
            if not self.tokenid:
                self.tokenid = self._search_token()
            
            # Step 1: Run minute-by-minute approach (baseline)
            logger.info("🔍 Step 1: Running Minute-by-Minute Approach (Baseline)")
            self.minute_by_minute_results = self._run_minute_by_minute()
            
            # Step 2: Run vectorized approach
            logger.info("🚀 Step 2: Running Vectorized Approach")
            self.vectorized_results = self._run_vectorized()
            
            # Step 3: Compare results
            logger.info("📊 Step 3: Comparing Signal Accuracy")
            self.comparison_results = self._compare_signals()
            
            # Step 4: Generate comprehensive report
            logger.info("📈 Step 4: Generating Accuracy Report")
            report = self._generate_accuracy_report()
            
            logger.info("=" * 80)
            logger.info("🎉 SIGNAL ACCURACY COMPARISON COMPLETED!")
            logger.info("=" * 80)
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Error in signal comparison: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc()
            }
            
    def _search_token(self) -> str:
        """Search token for ticker"""
        try:
            logger.info(f"🔍 Searching token for {self.ticker}...")
            api = get_api()
            
            search_patterns = [self.ticker, f"{self.ticker}-EQ", self.ticker.upper(), f"{self.ticker.upper()}-EQ"]
            
            for pattern in search_patterns:
                search_results = api.searchscrip(exchange=self.exchange, searchtext=pattern)
                
                if isinstance(search_results, dict) and search_results.get('stat') == 'Ok':
                    values = search_results.get('values', [])
                    if values:
                        for result in values:
                            if result.get('tsym', '').upper().startswith(self.ticker.upper()):
                                token = result.get('token', '')
                                symbol = result.get('tsym', '')
                                logger.info(f"✅ Found token: {token} (Symbol: {symbol})")
                                return token
                        
                        # Use first result if no exact match
                        first_result = values[0]
                        token = first_result.get('token', '')
                        logger.warning(f"⚠️ Using first result: {token}")
                        return token
                        
            raise ValueError(f"No valid token found for {self.ticker}")
            
        except Exception as e:
            logger.error(f"❌ Error searching token: {str(e)}")
            raise
            
    def _run_minute_by_minute(self) -> dict:
        """Run minute-by-minute signal generation"""
        try:
            logger.info("⏰ Executing minute-by-minute signal generation...")
            start_time = datetime.now()
            
            generator = MinuteByMinuteSignalGenerator(
                ticker=self.ticker,
                exchange=self.exchange,
                start=self.start,
                end=self.end,
                date=self.date,
                tokenid=self.tokenid
            )
            
            signals = generator.generate_minute_by_minute_signals()
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            results = {
                'approach': 'minute_by_minute',
                'execution_time': execution_time,
                'signals': signals,
                'position_events': generator.position_events,
                'summary': {
                    'total_minutes': len(signals),
                    'signals_generated': sum(1 for s in signals if s['signal_generated'] != 0),
                    'positions_opened': len([e for e in generator.position_events if e['type'] == 'ENTRY']),
                    'estimated_api_calls': len(signals) * 2  # 2 calls per minute
                }
            }
            
            logger.info(f"✅ Minute-by-minute completed: {execution_time:.2f}s, {results['summary']['signals_generated']} signals")
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in minute-by-minute approach: {str(e)}")
            raise
            
    def _run_vectorized(self) -> dict:
        """Run vectorized signal generation"""
        try:
            logger.info("🚀 Executing vectorized signal generation...")
            start_time = datetime.now()
            
            backtester = VectorizedBacktesterV4Logic(
                ticker=self.ticker,
                exchange=self.exchange,
                start=self.start,
                end=self.end,
                date=self.date,
                tokenid=self.tokenid
            )
            
            results = backtester.run_complete_analysis()
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Standardize format for comparison
            vectorized_results = {
                'approach': 'vectorized',
                'execution_time': execution_time,
                'signals': results.get('minute_signals', []),
                'position_events': results.get('position_events', []),
                'summary': {
                    'total_minutes': len(results.get('minute_signals', [])),
                    'signals_generated': results.get('performance_metrics', {}).get('total_signals_generated', 0),
                    'positions_opened': results.get('performance_metrics', {}).get('positions_opened', 0),
                    'api_calls_used': results.get('performance_metrics', {}).get('api_calls_used', 0)
                }
            }
            
            logger.info(f"✅ Vectorized completed: {execution_time:.2f}s, {vectorized_results['summary']['signals_generated']} signals")
            return vectorized_results
            
        except Exception as e:
            logger.error(f"❌ Error in vectorized approach: {str(e)}")
            raise
            
    def _compare_signals(self) -> dict:
        """Compare signals between both approaches"""
        try:
            logger.info("📊 Comparing signal accuracy...")
            
            mb_signals = self.minute_by_minute_results['signals']
            vec_signals = self.vectorized_results['signals']
            
            # Create comparison DataFrame
            comparison_data = []
            
            # Ensure both have same number of minutes
            min_length = min(len(mb_signals), len(vec_signals))
            
            signal_matches = 0
            signal_differences = []
            
            for i in range(min_length):
                mb_signal = mb_signals[i]
                vec_signal = vec_signals[i]
                
                # Compare key signal attributes
                mb_sig_val = mb_signal.get('signal_generated', 0)
                vec_sig_val = vec_signal.get('final_signal', 0)
                
                is_match = mb_sig_val == vec_sig_val
                if is_match:
                    signal_matches += 1
                else:
                    signal_differences.append({
                        'minute': i + 1,
                        'time': mb_signal.get('time', ''),
                        'mb_signal': mb_sig_val,
                        'vec_signal': vec_sig_val,
                        'mb_reason': mb_signal.get('signal_reason', ''),
                        'vec_reason': vec_signal.get('signal_reason', '')
                    })
                
                comparison_data.append({
                    'minute': i + 1,
                    'time': mb_signal.get('time', ''),
                    'mb_signal': mb_sig_val,
                    'vec_signal': vec_sig_val,
                    'match': is_match,
                    'mb_stage1_pass': mb_signal.get('stage1_pass', False),
                    'vec_stage1_pass': vec_signal.get('stage1', {}).get('pass', False),
                    'mb_stage2_pass': mb_signal.get('stage2_pass', False),
                    'vec_stage2_pass': vec_signal.get('stage2', {}).get('pass', False)
                })
            
            # Calculate accuracy metrics
            accuracy_percentage = (signal_matches / min_length) * 100 if min_length > 0 else 0
            
            comparison_results = {
                'total_minutes_compared': min_length,
                'signal_matches': signal_matches,
                'signal_differences_count': len(signal_differences),
                'accuracy_percentage': accuracy_percentage,
                'signal_differences': signal_differences,
                'detailed_comparison': comparison_data
            }
            
            logger.info(f"📊 Signal accuracy: {accuracy_percentage:.2f}% ({signal_matches}/{min_length} matches)")
            
            return comparison_results
            
        except Exception as e:
            logger.error(f"❌ Error comparing signals: {str(e)}")
            raise

    def _generate_accuracy_report(self) -> dict:
        """Generate comprehensive accuracy report"""
        try:
            logger.info("📈 Generating comprehensive accuracy report...")

            # Performance comparison
            mb_time = self.minute_by_minute_results['execution_time']
            vec_time = self.vectorized_results['execution_time']
            performance_improvement = mb_time / vec_time if vec_time > 0 else 0

            # API usage comparison
            mb_api_calls = self.minute_by_minute_results['summary']['estimated_api_calls']
            vec_api_calls = self.vectorized_results['summary']['api_calls_used']
            api_reduction = mb_api_calls / vec_api_calls if vec_api_calls > 0 else 0

            # Signal comparison
            mb_signals_count = self.minute_by_minute_results['summary']['signals_generated']
            vec_signals_count = self.vectorized_results['summary']['signals_generated']

            # Position comparison
            mb_positions = self.minute_by_minute_results['summary']['positions_opened']
            vec_positions = self.vectorized_results['summary']['positions_opened']

            report = {
                'success': True,
                'ticker': self.ticker,
                'analysis_period': f"{self.start}-{self.end}",
                'date': self.date,

                # Performance Metrics
                'performance_comparison': {
                    'minute_by_minute_time': mb_time,
                    'vectorized_time': vec_time,
                    'performance_improvement_factor': performance_improvement,
                    'time_savings_seconds': mb_time - vec_time,
                    'time_savings_percentage': ((mb_time - vec_time) / mb_time) * 100 if mb_time > 0 else 0
                },

                # API Usage Comparison
                'api_usage_comparison': {
                    'minute_by_minute_api_calls': mb_api_calls,
                    'vectorized_api_calls': vec_api_calls,
                    'api_reduction_factor': api_reduction,
                    'api_savings_percentage': ((mb_api_calls - vec_api_calls) / mb_api_calls) * 100 if mb_api_calls > 0 else 0
                },

                # Signal Accuracy
                'signal_accuracy': {
                    'total_minutes_compared': self.comparison_results['total_minutes_compared'],
                    'signal_matches': self.comparison_results['signal_matches'],
                    'signal_differences': self.comparison_results['signal_differences_count'],
                    'accuracy_percentage': self.comparison_results['accuracy_percentage'],
                    'minute_by_minute_signals': mb_signals_count,
                    'vectorized_signals': vec_signals_count,
                    'signal_count_match': mb_signals_count == vec_signals_count
                },

                # Position Accuracy
                'position_accuracy': {
                    'minute_by_minute_positions': mb_positions,
                    'vectorized_positions': vec_positions,
                    'position_count_match': mb_positions == vec_positions
                },

                # Ver4 Logic Validation
                'ver4_logic_validation': {
                    'signal_accuracy_perfect': self.comparison_results['accuracy_percentage'] == 100.0,
                    'signal_count_identical': mb_signals_count == vec_signals_count,
                    'position_count_identical': mb_positions == vec_positions,
                    'overall_logic_preserved': (
                        self.comparison_results['accuracy_percentage'] >= 95.0 and
                        abs(mb_signals_count - vec_signals_count) <= 1 and
                        abs(mb_positions - vec_positions) <= 1
                    )
                },

                # Detailed Results
                'detailed_comparison': self.comparison_results,
                'minute_by_minute_results': self.minute_by_minute_results,
                'vectorized_results': self.vectorized_results
            }

            # Log summary
            logger.info("🎯 ACCURACY REPORT SUMMARY:")
            logger.info(f"⚡ Performance Improvement: {performance_improvement:.1f}x")
            logger.info(f"📡 API Reduction: {api_reduction:.1f}x")
            logger.info(f"🎯 Signal Accuracy: {self.comparison_results['accuracy_percentage']:.2f}%")
            logger.info(f"📊 Signal Count Match: {mb_signals_count} vs {vec_signals_count}")
            logger.info(f"💼 Position Count Match: {mb_positions} vs {vec_positions}")
            logger.info(f"✅ Ver4 Logic Preserved: {report['ver4_logic_validation']['overall_logic_preserved']}")

            return report

        except Exception as e:
            logger.error(f"❌ Error generating accuracy report: {str(e)}")
            raise

    def save_comparison_report(self, report: dict, filename_prefix: str = "signal_accuracy_comparison") -> str:
        """Save the comparison report"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{filename_prefix}_{self.ticker}_{self.date.replace('-', '')}_{timestamp}.json"

            # Convert datetime objects to strings for JSON serialization
            json_results = json.loads(json.dumps(report, default=str))

            with open(filename, 'w') as f:
                json.dump(json_results, f, indent=2)

            logger.info(f"💾 Comparison report saved to: {filename}")
            return filename

        except Exception as e:
            logger.error(f"❌ Error saving comparison report: {str(e)}")
            return ""

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description='🔍 Signal Accuracy Comparison Tool',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Quick comparison (30 minutes)
  python compare_signal_accuracy.py --ticker BATAINDIA --date 20-06-2025 --start 12:00 --end 12:30

  # Extended comparison (1 hour)
  python compare_signal_accuracy.py --ticker BATAINDIA --date 20-06-2025 --start 12:00 --end 13:00

Purpose:
  Compare signals from vectorized vs minute-by-minute approaches to validate
  100% Ver4 logic preservation and measure performance improvements.
        """
    )

    parser.add_argument('--ticker', required=True, help='Stock ticker symbol (e.g., BATAINDIA)')
    parser.add_argument('--date', required=True, help='Date in DD-MM-YYYY format (e.g., 20-06-2025)')
    parser.add_argument('--start', required=True, help='Start time in HH:MM format (e.g., 12:00)')
    parser.add_argument('--end', required=True, help='End time in HH:MM format (e.g., 12:30)')
    parser.add_argument('--exchange', default='NSE', help='Exchange name (default: NSE)')
    parser.add_argument('--tokenid', default='', help='Token ID (will be auto-searched if not provided)')

    args = parser.parse_args()

    try:
        # Validate inputs
        datetime.strptime(args.date, '%d-%m-%Y')
        datetime.strptime(args.start, '%H:%M')
        datetime.strptime(args.end, '%H:%M')

        # Run comparison
        comparator = SignalAccuracyComparator(
            ticker=args.ticker,
            date=args.date,
            start=args.start,
            end=args.end,
            exchange=args.exchange,
            tokenid=args.tokenid
        )

        report = comparator.run_comparison()

        if report['success']:
            # Save report
            filename = comparator.save_comparison_report(report)

            logger.info("🎉 SIGNAL ACCURACY COMPARISON COMPLETED SUCCESSFULLY!")
            logger.info(f"💾 Report saved to: {filename}")

            sys.exit(0)
        else:
            logger.error("❌ SIGNAL ACCURACY COMPARISON FAILED!")
            sys.exit(1)

    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
