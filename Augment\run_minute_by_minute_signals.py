"""
Run Minute-by-Minute Signal Generation

This script executes the original Ver4 minute-by-minute signal detection logic
to generate baseline reference signals for comparison with the vectorized approach.

Usage:
    python run_minute_by_minute_signals.py --ticker BATAINDIA --date 20-06-2025 --start 12:00 --end 15:30

Features:
✅ Exact Ver4 two-stage signal detection
✅ Individual API calls for each minute analysis
✅ Perfect baseline reference for comparison
✅ No plotting issues - pure signal generation
"""

import argparse
import sys
import os
import logging
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import our modules
from minute_by_minute_signal_generator import MinuteByMinuteSignalGenerator
from shared_api_manager import get_api

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('minute_by_minute_signals.log')
    ]
)
logger = logging.getLogger(__name__)

def search_token(ticker: str, exchange: str = 'NSE') -> str:
    """Search token for ticker using shared API"""
    try:
        logger.info(f"🔍 Searching token for {ticker} on {exchange}...")
        api = get_api()
        
        # Try different search patterns
        search_patterns = [
            ticker,
            f"{ticker}-EQ",
            ticker.upper(),
            f"{ticker.upper()}-EQ"
        ]
        
        for pattern in search_patterns:
            logger.debug(f"🔍 Trying search pattern: {pattern}")
            
            search_results = api.searchscrip(exchange=exchange, searchtext=pattern)
            
            if isinstance(search_results, dict) and search_results.get('stat') == 'Ok':
                values = search_results.get('values', [])
                if values:
                    for result in values:
                        if result.get('tsym', '').upper().startswith(ticker.upper()):
                            token = result.get('token', '')
                            symbol = result.get('tsym', '')
                            logger.info(f"✅ Found token for {ticker}: {token} (Symbol: {symbol})")
                            return token
                    
                    # Use first result if no exact match
                    first_result = values[0]
                    token = first_result.get('token', '')
                    symbol = first_result.get('tsym', '')
                    logger.warning(f"⚠️ Using first result: {token} (Symbol: {symbol})")
                    return token
                    
        raise ValueError(f"No valid token found for {ticker} on {exchange}")
        
    except Exception as e:
        logger.error(f"❌ Error searching token for {ticker}: {str(e)}")
        raise

def run_minute_by_minute_analysis(ticker: str, date: str, start: str, end: str, 
                                exchange: str = 'NSE', tokenid: str = "") -> dict:
    """
    🔍 RUN MINUTE-BY-MINUTE SIGNAL ANALYSIS
    
    Executes the original Ver4 minute-by-minute signal detection logic.
    """
    try:
        logger.info("🔍 STARTING MINUTE-BY-MINUTE SIGNAL GENERATION")
        logger.info("=" * 80)
        logger.info(f"📊 Analysis Parameters:")
        logger.info(f"   Ticker: {ticker}")
        logger.info(f"   Exchange: {exchange}")
        logger.info(f"   Date: {date}")
        logger.info(f"   Time Range: {start} - {end}")
        logger.info("=" * 80)
        
        # Get token if not provided
        if not tokenid:
            tokenid = search_token(ticker, exchange)
            
        # Initialize the minute-by-minute signal generator
        logger.info("🔧 Initializing Minute-by-Minute Signal Generator...")
        generator = MinuteByMinuteSignalGenerator(
            ticker=ticker,
            exchange=exchange,
            start=start,
            end=end,
            date=date,
            tokenid=tokenid
        )
        
        # Generate minute-by-minute signals
        logger.info("⏰ Executing Minute-by-Minute Analysis...")
        start_time = datetime.now()
        
        signals = generator.generate_minute_by_minute_signals()
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        # Save results
        filename = generator.save_results("minute_by_minute_reference")
        
        # Calculate summary statistics
        total_minutes = len(signals)
        signal_count = sum(1 for s in signals if s['signal_generated'] != 0)
        position_count = len([e for e in generator.position_events if e['type'] == 'ENTRY'])
        
        # Estimate API calls (2 per minute for two-stage analysis)
        estimated_api_calls = total_minutes * 2
        
        results = {
            'success': True,
            'ticker': ticker,
            'analysis_period': f"{start}-{end}",
            'date': date,
            'execution_time_seconds': execution_time,
            'total_minutes_analyzed': total_minutes,
            'signals_generated': signal_count,
            'positions_opened': position_count,
            'estimated_api_calls': estimated_api_calls,
            'time_per_minute': execution_time / total_minutes if total_minutes > 0 else 0,
            'signal_frequency_percent': (signal_count / total_minutes) * 100 if total_minutes > 0 else 0,
            'minute_signals': signals,
            'position_events': generator.position_events,
            'results_file': filename
        }
        
        # Display summary
        logger.info("🎉 MINUTE-BY-MINUTE ANALYSIS COMPLETED!")
        logger.info("=" * 80)
        logger.info("📈 EXECUTION SUMMARY:")
        logger.info(f"⏰ Execution Time: {execution_time:.2f} seconds ({execution_time/60:.2f} minutes)")
        logger.info(f"📊 Minutes Analyzed: {total_minutes}")
        logger.info(f"📡 Estimated API Calls: {estimated_api_calls}")
        logger.info(f"⚡ Time per Minute: {execution_time/total_minutes:.2f} seconds")
        
        logger.info("=" * 80)
        logger.info("🎯 SIGNAL SUMMARY:")
        logger.info(f"🎯 Signals Generated: {signal_count}")
        logger.info(f"💼 Positions Opened: {position_count}")
        logger.info(f"📈 Signal Frequency: {(signal_count/total_minutes)*100:.2f}%")
        
        if filename:
            logger.info(f"💾 Results saved to: {filename}")
            
        logger.info("=" * 80)
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Error in minute-by-minute analysis: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description='🔍 Minute-by-Minute Signal Generator with Ver4 Exact Logic',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_minute_by_minute_signals.py --ticker BATAINDIA --date 20-06-2025 --start 12:00 --end 15:30
  python run_minute_by_minute_signals.py --ticker NIFTY --date 20-06-2025 --start 09:15 --end 15:30

Purpose:
  Generate baseline reference signals using original Ver4 minute-by-minute logic
  for comparison with vectorized approach to validate 100% accuracy preservation.
        """
    )
    
    parser.add_argument('--ticker', required=True, help='Stock ticker symbol (e.g., BATAINDIA)')
    parser.add_argument('--date', required=True, help='Date in DD-MM-YYYY format (e.g., 20-06-2025)')
    parser.add_argument('--start', required=True, help='Start time in HH:MM format (e.g., 12:00)')
    parser.add_argument('--end', required=True, help='End time in HH:MM format (e.g., 15:30)')
    parser.add_argument('--exchange', default='NSE', help='Exchange name (default: NSE)')
    parser.add_argument('--tokenid', default='', help='Token ID (will be auto-searched if not provided)')
    parser.add_argument('--save-json', action='store_true', help='Save detailed results to JSON file')
    
    args = parser.parse_args()
    
    try:
        # Validate date format
        datetime.strptime(args.date, '%d-%m-%Y')
        
        # Validate time format
        datetime.strptime(args.start, '%H:%M')
        datetime.strptime(args.end, '%H:%M')
        
        # Run the minute-by-minute analysis
        results = run_minute_by_minute_analysis(
            ticker=args.ticker,
            date=args.date,
            start=args.start,
            end=args.end,
            exchange=args.exchange,
            tokenid=args.tokenid
        )
        
        if results['success']:
            logger.info("🎉 MINUTE-BY-MINUTE SIGNAL GENERATION COMPLETED SUCCESSFULLY!")
            
            if args.save_json:
                # Save additional detailed JSON
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                detailed_filename = f"detailed_minute_results_{args.ticker}_{timestamp}.json"
                import json
                with open(detailed_filename, 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                logger.info(f"💾 Detailed results saved to: {detailed_filename}")
                
            sys.exit(0)
        else:
            logger.error("❌ MINUTE-BY-MINUTE SIGNAL GENERATION FAILED!")
            sys.exit(1)
            
    except ValueError as e:
        logger.error(f"❌ Invalid input format: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
