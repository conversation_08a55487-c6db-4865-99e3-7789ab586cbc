#backtester ver4 using nadaraya envelope when the stock is sideways for entry (using check_sideways_and_nadarya), and custom stop loss/book profit using mutiple methods of generting exit signals like technicals, custom stop loss logic, book profit, etc for exit and also calcution and saving of technicals during entry and exit for further study. 
from datetime import datetime
from datetime import timedelta
import numpy as np
import warnings
import io
from docx import Document
from contextlib import redirect_stdout
import warnings
import pandas as pd
import traceback
import linecache
warnings.filterwarnings("ignore", category=RuntimeWarning)
warnings.filterwarnings("ignore")

last_stock_traded=''
last_stock_traded_time=''

def test_sideways_and_nadarya_envelope(tokenid, exchange, hours, date_input, starttime_input, endtime_input, current = False ):
        import random
        trends = ['Latest Trend: Higher High', 'Latest Trend: Higher Low', 
                  'Latest Trend: Lower Low', 'Latest Trend: Lower High']
        return True, 'Uptrend', random.choice(trends), 'Buy', True


def check_sideways_and_nadarya(ticker, tokenid, exchange, start, end, date):
    import datetime
    from datetime import timedelta
    start_time = datetime.datetime.strptime(start, '%H:%M')
    end_time = datetime.datetime.strptime(end, '%H:%M')
    time_diff = (end_time - start_time).total_seconds() / 3600
    if time_diff < 0.7:
        start_time = end_time - timedelta(hours=0.7)
        market_open = datetime.datetime.strptime('09:30', '%H:%M')
        if start_time < market_open:
            start_time = market_open
        start_time = start_time.strftime('%H:%M')
        end_time = end_time.strftime('%H:%M')
    else:
        start_time = start
        end_time = end
    # print(f'start_time1 ', start_time)
    # print(f'end_time1', end_time)
    issideways1, overall_trend1, trend_label1, signal_text1, isvander = test_detect_head_shoulder(tokenid=tokenid,exchange=exchange,hours=0.7,current=False,
                                                                                                           date_input=date,starttime_input=start_time,endtime_input=end_time, plot=False)
    # print(f'issideways1', issideways1)
    # print(f'overall_trend1', overall_trend1)
    # print(f'trend_label1', trend_label1)
    # print(f'signal_text1', signal_text1)
    # print(f'isvander', isvander)
   
    
    if issideways1 and isvander:
        # print(f"stock: {ticker}")
        # print(f'overall trend in 0.7 is {overall_trend1} with latest trend: {trend_label1} and latest vander signal: {signal_text1}')
        start_time = datetime.datetime.strptime(start, '%H:%M')
        end_time = datetime.datetime.strptime(end, '%H:%M')
        time_diff = (end_time - start_time).total_seconds() / 3600  # Convert to hours
        if time_diff < 1.2:
            start_time = end_time - timedelta(hours=0.7)
            market_open = datetime.datetime.strptime('09:30', '%H:%M')
            if start_time < market_open:
                start_time = market_open
            start_time = start_time.strftime('%H:%M')
            end_time = end_time.strftime('%H:%M')
        else:
            start_time = start
            end_time = end
        issideways2, overall_trend2, trend_label2, signal_text2, isvander2 = test_detect_head_shoulder(tokenid=tokenid,exchange=exchange,hours=1.2,current=False,date_input=date,starttime_input=start_time,endtime_input=end_time, plot=False) 
        # print(f'overall trend in 1.2 is {overall_trend2} with latest trend: {trend_label2} and latest vander signal: {signal_text2}')
        if issideways2 and isvander2:
            if  signal_text2 in ['Last 5 mins: Lower band signal present']:
                # print('call buy at start')
                signal = 1  # Signal to buy call
            elif signal_text2 in ['Last 5 mins: Upper band signal present']:
                # print('put buy at start')
                signal = -1  # Signal to buy put
            else:
                # print('no signal at start')
                signal = 0
                
 
            print(f'signal', signal)
            print(f"check {['put', 'nothing', 'call'][signal+1]}")
            return True, signal
    # print(f"no trade: stock: {ticker}")
    return False, 0
warnings.filterwarnings("ignore", category=pd.errors.SettingWithCopyWarning)
class StarterSystem:
  def __init__(self, ticker, exchange, start:str, end:str ,date:str, tokenid, target_risk=0.2, stop_loss_gap=0.3,
                 starting_capital=100000, option_cost=0.01, interest_on_balance=0.0,
                  interval=1):
        self.ticker = ticker
        self.exchange = exchange
        self.tokenid = tokenid
        self.interval = interval
        self.target_risk = target_risk
        self.stop_loss_gap = stop_loss_gap
        self.starting_capital = starting_capital
        self.option_cost = option_cost
        self.start = start
        self.end = end
        self.date = date
        self.interest_on_balance = interest_on_balance
        self.daily_iob = (1 + self.interest_on_balance) ** (1 / 252)
        self.signal_names = []
        self._getData()
        self._calcSignals()
        self.api = api
        global last_stock_traded
        global last_stock_traded_time
  def get_quotes(self, exch, token):
        return api.get_quotes(exchange=exch, token=token)
  def search_scrip(self, exchange, searchtext):
        return api.searchscrip(exchange=exchange, searchtext=searchtext)
  def is_otm(self, option_type, strike_price, current_price):
        if option_type == 'CE':
            x=1
            # print(f'is call otm', strike_price > current_price)
            return strike_price > current_price
        elif option_type == 'PE':
            x=1
            # print(f'is put otm', strike_price < current_price)
            return strike_price < current_price
        return False
  def check_price_difference(self, sp1, bp1):
      try:
        if bp1 == 0:
            x=1
            # print(f"Warning: BP1 is zero. SP1={sp1}, BP1={bp1}")
            return False
        print((sp1 - bp1) / bp1)
        return (sp1 - bp1) / bp1 < 0.03
      except ZeroDivisionError:
        x=1
        # print(f"ZeroDivisionError in check_price_difference: SP1={sp1}, BP1={bp1}")
        return False

  def calculate_technicals_at_times(self, entry_time_str, exit_time_str):
    """Calculates technical indicators at specified entry and exit times.

    Args:
        candle_data: A list of dictionaries representing candle data.
        entry_time_str: String representing the entry time ('YYYY-MM-DD HH:MM:SS').
        exit_time_str: String representing the exit time ('YYYY-MM-DD HH:MM:SS').

    Returns:
        A dictionary with technical indicators or None if an error occurs.
    """
    try:
        import talib

        # Extract time strings (HH:MM)
        print(f'entry_time_str',entry_time_str)
        print(f'exit_time_str', exit_time_str)
        # entry_time_only = datetime.datetime.strptime(entry_time_str, '%d-%m-%Y %H:%M:%S').strftime('%H:%M')
        # exit_time_only = datetime.datetime.strptime(exit_time_str, '%d-%m-%Y %H:%M:%S').strftime('%H:%M')
        entry_time_only = '11:30'
        exit_time_only = '15:29'
        print(f'entry_time_only', entry_time_only )
        print(f'exit_time_only', exit_time_only)
        
        start_timestamp, end_timestamp = get_start_end_timestamps(self.date, entry_time_only, exit_time_only)

        candle_data = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)

        if candle_data is None or not candle_data:
            print("No candle data received from API.")
            return None

        # Reverse the candle data to chronological order
        candle_data.reverse()

        df = pd.DataFrame(candle_data)
        # print(df)

        # Convert columns to correct types, handling errors
        for col in ['into', 'inth', 'intl', 'intc', 'v']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        df['time'] = pd.to_datetime(df['time'], format='%d-%m-%Y %H:%M:%S')
        df = df.set_index('time')

        entry_time = pd.to_datetime(entry_time_str)
        exit_time = pd.to_datetime(exit_time_str)

        # Calculate indicators (using 'v' for volume)
        df['SMA_10'] = talib.SMA(df['intc'], timeperiod=10)
        df['EMA_20'] = talib.EMA(df['intc'], timeperiod=20)
        df['RSI'] = talib.RSI(df['intc'], timeperiod=14)
        df['ADX'] = talib.ADX(df['inth'], df['intl'], df['intc'], timeperiod=14)
        df['MACD'], df['MACD_signal'], df['MACD_hist'] = talib.MACD(df['intc'], fastperiod=12, slowperiod=26, signalperiod=9)
        df['ATR'] = talib.ATR(df['inth'], df['intl'], df['intc'], timeperiod=14)
        df['BB_UPPER'], df['BB_MIDDLE'], df['BB_LOWER'] = talib.BBANDS(df['intc'], timeperiod=20)
        df['OBV'] = talib.OBV(df['intc'], df['v'])

        # Extract indicator values
        entry_tech = df.loc[entry_time]
        exit_tech = df.loc[exit_time]

        results = {
            'entry_time': entry_time,
            'exit_time': exit_time,
            'entry_SMA_10': entry_tech['SMA_10'],
            'entry_EMA_20': entry_tech['EMA_20'],
            'entry_RSI': entry_tech['RSI'],
            'entry_ADX': entry_tech['ADX'],
            'entry_MACD': entry_tech['MACD'],
            'entry_MACD_signal': entry_tech['MACD_signal'],
            'entry_MACD_hist': entry_tech['MACD_hist'],
            'entry_ATR': entry_tech['ATR'],
            'entry_BB_UPPER': entry_tech['BB_UPPER'],
            'entry_BB_MIDDLE': entry_tech['BB_MIDDLE'],
            'entry_BB_LOWER': entry_tech['BB_LOWER'],
            'entry_OBV': entry_tech['OBV'],
            'exit_SMA_10': exit_tech['SMA_10'],
            'exit_EMA_20': exit_tech['EMA_20'],
            'exit_RSI': exit_tech['RSI'],
            'exit_ADX': exit_tech['ADX'],
            'exit_MACD': exit_tech['MACD'],
            'exit_MACD_signal': exit_tech['MACD_signal'],
            'exit_MACD_hist': exit_tech['MACD_hist'],
            'exit_ATR': exit_tech['ATR'],
            'exit_BB_UPPER': exit_tech['BB_UPPER'],
            'exit_BB_MIDDLE': exit_tech['BB_MIDDLE'],
            'exit_BB_LOWER': exit_tech['BB_LOWER'],
            'exit_OBV': exit_tech['OBV'],
        }
        return results

    except KeyError as e:
        print(f"Error: Data not found for specified time: {e}")
        return None
    except Exception as e:
        print(f"An error occurred during technical indicator calculation: {e}")
        return None
  def check_lot_price(self, sp1, lot_size):
        
        total_price = sp1 * lot_size
        # print(f'total_price', total_price)
        return 4000 < total_price < 25000
  def get_start_end_timestamps(self, date_input, starttime_input, endtime_input):
    import datetime as datetime
    date_parts = date_input.split('-')
    year, month, day = int(date_parts[2]), int(date_parts[1]), int(date_parts[0])
    start_time = datetime.datetime.strptime(starttime_input, '%H:%M').time()
    end_time = datetime.datetime.strptime(endtime_input, '%H:%M').time()
    start_datetime = datetime.datetime(year, month, day, start_time.hour, start_time.minute)
    end_datetime = datetime.datetime(year, month, day, end_time.hour, end_time.minute)
    start_timestamp = start_datetime.timestamp()
    end_timestamp = end_datetime.timestamp()
    return start_timestamp, end_timestamp
  def get_option_data(self, signal):
    try:
        # print('inside get option data')
        import datetime
        df = None
        now = datetime.datetime.now()
        hours = 1
        date_input = now.date().strftime('%d-%m-%Y')
        endtime_input = now.time().strftime('%H:%M')
        starttime_input = (now - timedelta(hours=hours)).time().strftime('%H:%M')
        date_input = self.date
        starttime_input = self.start
        endtime_input = self.end      
        start_timestamp, end_timestamp = self.get_start_end_timestamps(date_input, starttime_input, endtime_input)
        # print(f'start_timestamp', starttime_input)
        # print(f'end_timestamp',endtime_input)
        from datetime import datetime
      
            
        strikeprice = round(self.current_price / 100) * 100
        self.data['option_underlying_price']=strikeprice
        symbol_expiry = '26DEC24'  # You might want to make this dynamic
        future_symbol = f"{self.ticker}{symbol_expiry}F"
        count = 10
        ret3 = api.get_option_chain('NFO', future_symbol, strikeprice, count)
        # print(f'strikeprice', strikeprice)
        # print(f'ret3', ret3)
        option_chain = ret3['values']
        self.data['lot_size'] = ret3['values'][0]['ls']
        # print(f'old option chain', option_chain)
        option_type = 'CE' if signal == 1 else 'PE'
        option_chain = [opt for opt in option_chain if opt['optt'] == option_type]
        # print(f'new option chain', option_chain)

        # Sort based on signal
        if signal == 1:  # For calls, find strikes above spot price
            option_chain = [opt for opt in option_chain if float(opt['strprc']) > float(strikeprice)]
            option_chain.sort(key=lambda x: float(x['strprc']))  # Sort by strike price ascending
           
        else:  # For puts, find strikes below spot price
            option_chain = [opt for opt in option_chain if float(opt['strprc']) < float(strikeprice)]
            option_chain.sort(key=lambda x: float(x['strprc']), reverse=True)  # Sort by strike price descending
          

        max_retries=3


        valid_ce_options = []
        valid_pe_options = []
        current_price=''
        current_time=''
        current_volume=''
        for option in option_chain:
                retries = 0
                while retries < max_retries:
                    try:
                        option_quotes = self.get_quotes('NFO', option['token'])
                        # print('option_quotes:')
                        # print(option_quotes)
                        if option_quotes['stat'] != 'Ok':
                            raise Exception("Quote status not Ok")
                        option_type = option['optt']
                        strike_price = float(option['strprc'])
                        sp1 = float(option_quotes.get('sp1', 0))
                        bp1 = float(option_quotes.get('bp1', 0))
                        lot_size = int(option['ls'])
                        tokenid = option['token']
                        df = api.get_time_price_series(exchange='NFO', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                        # print(df)
                        if isinstance(df, list) and len(df) > 0:
                            #print('inside instance')
                            # print(df)
                            latest_candle = df[0]
                            if isinstance(latest_candle, dict) and 'intc' in latest_candle and 'intv' in latest_candle:
                                current_price = float(latest_candle['intc'])
                                current_volume = int(latest_candle['intv'])
                                current_time = latest_candle['time']
                                # print(f"Current price set to: {current_price}")
                                # print(f"Current volume set to: {current_volume}")
                                # print(f"Current time set to: {current_time}")
                            else:
                                raise Exception("Unable to extract 'intc' or 'intv' from the latest candle")
                        else:
                            raise Exception("Invalid or empty response from get_time_price_series")
    
                        # is_otm = self.is_otm(option_type, strike_price, current_price)
                        lot_price_check = self.check_lot_price(current_price, lot_size)
                        # print(f'lot_price_check', lot_price_check)
    
                        if lot_price_check:
                            option_data = {
                                'symbol': option['tsym'],
                                'type': option_type,
                                'strike': strike_price,
                                'token': option['token'],
                                'lot_size': lot_size,
                                'volume': current_volume,
                                'price': current_price,
                                'time': current_time
                            }
                            if option_type == 'CE':
                                valid_ce_options.append(option_data)
                            elif option_type == 'PE':
                                valid_pe_options.append(option_data)
                        break
                    except Exception as e:
                        retries += 1
                        if retries == max_retries:
                            x=1
                            # print(f"Maximum number of retries reached for option {option['token']}. Error: {str(e)}")
                        else:
                            # print(f"Error occurred: {str(e)}. Retrying in 1 second... (Attempt {retries}/{max_retries})")
                            time.sleep(1)

        if signal > 0:  # Call option
                selected_options = valid_ce_options
        elif signal < 0:  # Put option
                selected_options = valid_pe_options
        else:
                x=1
                # print("No trade signal (signal is 0)")
                return None, None  # No trade signal
        if selected_options:
            
            selected_option = max(selected_options, key=lambda x: x['volume'])
         
            
            option_result = {
                'CE' if signal > 0 else 'PE': {
                    'token': selected_option['token'],
                    'symbol': selected_option['symbol']
                }
            }
            # print(f'option_result', option_result)
            # print(f'selected_option', selected_option)
            return option_result, selected_option
        else:
            return None, None  # Return None, None if no valid options found
    except Exception as e:
            print(f"Error in get_option_data: {str(e)}")
            return None, None  # Return None, None in case of any exception
  def live_data(self, data):
        import pandas as pd
        from datetime import datetime
        response = data
        time = []
        open_ = []
        high_ = []
        low_ = []
        close_ = []
        volume = []
        for candle in response:
            time.append(datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S'))
            open_.append(float(candle['into']))
            high_.append(float(candle['inth']))
            low_.append(float(candle['intl']))
            close_.append(float(candle['intc']))
            volume.append(float(candle['intv']))
        if close_:
            self.current_price = close_[-1]  # Assuming 'self' is the class instance
    
        candles = pd.DataFrame({
            "Open": open_,
            "High": high_,
            "Low": low_,
            "Close": close_,
            "volume": volume,
            "time": time
        })
        candles = candles.set_index("time")
        return candles

  def set_current_price(self):
    import datetime
    df = None
    now = datetime.datetime.now()
    hours = 1
    date_input = now.date().strftime('%d-%m-%Y')
    endtime_input = now.time().strftime('%H:%M')
    starttime_input = (now - timedelta(hours=hours)).time().strftime('%H:%M')
    date_input = self.date
    starttime_input = self.start
    endtime_input = self.end
    start_timestamp, end_timestamp = self.get_start_end_timestamps(date_input, starttime_input, endtime_input)
    print(f'start_timestamp', self.start)
    print(f'end_timestamp', self.end)
    first_digit_index = next((i for i, char in enumerate(self.ticker) if char.isdigit()), None)
    if self.exchange is not None:
        if self.exchange == 'NSE':
            ret2 = api.searchscrip(exchange='NSE', searchtext=self.ticker+'-EQ')
        elif exchange == 'MCX':
            if new_string == 'NATURALGAS':
                    new_string = 'NATURALGAS25JUN24'
            elif new_string == 'CRUDEOIL':
                    new_string = 'CRUDEOIL18JUN24'
            ret2 = api.searchscrip(exchange='MCX', searchtext=new_string)
        tokenid = ret2['values'][0]['token']
    max_retries = 3
    retries = 0
    while retries < max_retries:
                try:
                     if self.exchange == 'NSE':
                        df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                        if isinstance(df, list) and len(df) > 0:
                            latest_candle = df[0]  # Get the first item in the list
                            if isinstance(latest_candle, dict) and 'intc' in latest_candle:
                                current_p = float(latest_candle['intc'])
                                self.current_price = float(latest_candle['intc'])
                            else:
                                current_p = None
                                self.current_price = None
                        else:
                            current_p = None
                            self.current_price = None
                     elif exchange == 'MCX':
                        df = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=interval)
                     break 
                except Exception as e:
                    # print(f"Error: {e}")
                    retries += 1
                    if retries == max_retries:
                        x=1
                        # print("Maximum number of retries reached. Unable to fetch data.")
                    else:
                        # print(f"Retrying in 2 seconds... (Attempt {retries}/{max_retries})")
                        time.sleep(1)

    return current_p
  def _getData(self):
    import datetime
    df = None
    now = datetime.datetime.now()
    hours = 1
  
    date_input = self.date
    starttime_input = self.start
    endtime_input = self.end
    start_timestamp, end_timestamp = self.get_start_end_timestamps(date_input, starttime_input, endtime_input)
    first_digit_index = next((i for i, char in enumerate(self.ticker) if char.isdigit()), None)
    if self.exchange is not None:
        if self.exchange == 'NSE':
            ret2 = api.searchscrip(exchange='NSE', searchtext=self.ticker+'-EQ')
        elif exchange == 'MCX':
            if new_string == 'NATURALGAS':
                    new_string = 'NATURALGAS25JUN24'
            elif new_string == 'CRUDEOIL':
                    new_string = 'CRUDEOIL18JUN24'
            ret2 = api.searchscrip(exchange='MCX', searchtext=new_string)
        tokenid = ret2['values'][0]['token']
    max_retries = 3
    retries = 0
    while retries < max_retries:
                try:
                    if self.exchange == 'NSE':
                        df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                        # print(f'df', df)
                        if isinstance(df, list) and len(df) > 0:
                            latest_candle = df[0]  # Get the first item in the list
                            if isinstance(latest_candle, dict) and 'intc' in latest_candle:
                                self.current_price = float(latest_candle['intc'])
                            else:
                                self.current_price = None
                        else:
                            # print("Invalid or empty response")
                            self.current_price = None
                    elif exchange == 'MCX':
                        df = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=interval)
                    break  # If the API call is successful, exit the loop
                except Exception as e:
                    print(f"Error: {e}")
                    retries += 1
                    if retries == max_retries:
                        x=1
                        # print("Maximum number of retries reached. Unable to fetch data.")
                    else:
                        # print(f"Retrying in 2 seconds... (Attempt {retries}/{max_retries})")
                        time.sleep(1)
            
    if retries < max_retries:
                new0=0
    df = self.live_data(df)
    self.data = df
  def _calcSignals(self):
        self._calcSidewaysNadaryaSignals()

  def _calcSidewaysNadaryaSignals(self):
        # print('inside _calcSidewaysNadaryaSignals')
        name = 'SidewaysNadarya'
        import datetime
        start_time = datetime.datetime.strptime(self.start, '%H:%M')
        end_time = datetime.datetime.strptime(self.end, '%H:%M')
        time_diff = (end_time - start_time).total_seconds() / 3600  # Convert to hours
        if time_diff < 0.7:
            start_time = end_time - timedelta(hours=0.7)
            market_open = datetime.datetime.strptime('09:30', '%H:%M')
            if start_time < market_open:
                start_time = market_open
            start_time = start_time.strftime('%H:%M')
            end_time=end_time.strftime('%H:%M')
        else:
            start_time=self.start
            end_time=self.end
        issideways1, overall_trend1, trend_label1, signal_text1, isvander = test_detect_head_shoulder(tokenid = self.tokenid,
                                                                                                               exchange = self.exchange,
                                                                                                               hours=0.7, current = False, 
                                                                                                                    date_input=self.date, 
                                                                                                                    starttime_input=start_time,
                                                                                                                    endtime_input=end_time, plot=False)
        if issideways1 and isvander:
            # print(f"stock: {self.ticker}")
            # print(f'overall trend in 0.7 is {overall_trend1} with latest trend: {trend_label1} and latest vander signal: {signal_text1}')
            start_time = datetime.datetime.strptime(self.start, '%H:%M')
            end_time = datetime.datetime.strptime(self.end, '%H:%M')
            time_diff = (end_time - start_time).total_seconds() / 3600  # Convert to hours
            if time_diff < 1.2:
                start_time = end_time - timedelta(hours=0.7)
                market_open = datetime.datetime.strptime('09:30', '%H:%M')
                if start_time < market_open:
                    start_time = market_open
                start_time = start_time.strftime('%H:%M')
                end_time=end_time.strftime('%H:%M')
            else:
                start_time=self.start
                end_time=self.end
               
            issideways2, overall_trend2, trend_label2, signal_text2, isvander2 = test_detect_head_shoulder(tokenid = self.tokenid,
                                                                                                                    exchange=self.exchange,
                                                                                                                    hours=1.2, current = False, 
                                                                                                                    date_input=self.date, 
                                                                                                                    starttime_input=start_time,
                                                                                                                    endtime_input=end_time,  plot=False)
            # print(f'overall trend in 1.2 is {overall_trend2} with latest trend: {trend_label2} and latest vander signal: {signal_text2}')
            
            True
            if issideways2 and isvander2:
                # if trend_label2 in ['Latest Trend: Higher High,', 'Latest Trend: Higher Low,']:
               
                if signal_text2 in ['Last 5 mins: Lower band signal present']:
                    # print('call buy signal in calc nadarya')
                    self.data[name] = 1  # Signal to buy call
                elif signal_text2 in ['Last 5 mins: Upper band signal present']:
                    # print('sell/putbuy signal in calc nadarya')
                    self.data[name] = -1  # Signal to buy put
                else:
                    # print('no signal in calc nadarya')
                    self.data[name] = 0
                
                # print(f"check {['put', 'nothing', 'call'][self.data[name].iloc[-1]+1]}")
            else:
                self.data[name] = 0
        else:
            self.data[name] = 0
            print(f"no trade: stock: {self.ticker}")
        
        self.signal_names.append(name)
        # print(f'signal_names', self.signal_names)
        # print('done calc nadarya')
  def _getSignal(self):
        return self.data['SidewaysNadarya'].iloc[-1]
  def _calcStopPrice(self, price, std, position, signal):
        if position != 0:
            return price * (1 - std * self.stop_loss_gap * np.sign(position))
        else:
            return price * (1 - std * self.stop_loss_gap * np.sign(signal))
  def _sizePosition(self, capital, price):
    contracts = np.floor(self.target_risk * capital / option_price)
    return contract
  def _calcCash(self, cash_balance):
        return cash_balance * self.daily_iob
 
  def stop_loss_strategy(self, entry_price, option_entry_time, option_type, option_tokenid):
    from datetime import datetime, timedelta,time
    import talib
    current_mid = entry_price
    mid_count = 0
    option_entry_time = datetime.strptime(option_entry_time, '%d-%m-%Y %H:%M:%S')
    # Create exit time for the same day at 15:00:00
    exit_time = datetime.combine(option_entry_time.date(), time(15, 0, 0))
    
    last_checked_time = option_entry_time
    last_checked_time = last_checked_time.timestamp()
    # print(f'option_entry_time: {option_entry_time}')

    # Set start time as entry time and end time as 1 hour after entry time
    start_timestamp = option_entry_time
    # end_timestamp = option_entry_time + timedelta(hours=2)
  
    start_timestamp = start_timestamp.timestamp()
    end_timestamp = exit_time.timestamp()

    # # Initialize profit targets
    # initial_target = entry_price * (1.005 if option_type == 'call' else 0.995)  # 0.5% profit target
    # trailing_target = entry_price * (1.0025 if option_type == 'call' else  0.9999)  # 0.25% trailing target
    # max_profit_seen = entry_price

    # Fetch initial data
    df1 = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)

    def get_market_context(df, lookback=20):
        # print('inside get_market_context')
        # From trailing_mids_v2
        def calculate_atr(df, period):
             # Convert string columns to numeric before calculations
            df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
            df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
            df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
            # print('1')
            high_low = df['inth'] - df['intl']
            # print('2')
            high_close = abs(df['inth'] - df['intc'].shift())
            # print('3')
            low_close = abs(df['intl'] - df['intc'].shift())
            # print('4')
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            # print('5')
            true_range = ranges.max(axis=1)
            return true_range.rolling(period).mean()
        
        atr = calculate_atr(df, lookback)
        # Convert volume to numeric as well
        df['v'] = pd.to_numeric(df['v'], errors='coerce')
        # print(f'df',df)
        vol_profile = df['v'].mean() * 1.5

        # Convert the Series comparison to a single boolean
        mean_close = df['intc'].mean()
        is_volatile = (atr > mean_close * 0.002).any()  # or .all() depending on your needs
        
        # is_volatile = atr > df['intc'].mean() * 0.002
        return is_volatile, vol_profile, atr

    def calculate_vwap(df):
        # print('in calculate_vwap')
        df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        df['v'] = pd.to_numeric(df['v'], errors='coerce')
        # From volume_weighted_trailing_stop
        return (df['v'] * df['intc']).cumsum() / df['v'].cumsum()

    
    def identify_swing_points(df, window=5):
        # print('in identify_swing_points')
        import numpy as np
        
      
        
        # Check if DataFrame is too small
        if len(df) <= window * 2:
            # Return empty series if not enough data
            return pd.Series(False, index=df.index), pd.Series(False, index=df.index)
        
        try:
            # Ensure data is numeric and handle any conversion issues
            highs = df['inth'].astype(float).values
            lows = df['intl'].astype(float).values
            
            # Create boolean arrays
            swing_high = np.zeros(len(df), dtype=bool)
            swing_low = np.zeros(len(df), dtype=bool)
            
            # Process each point
            for i in range(window, len(df) - window):
                try:
                    # Get current values
                    curr_high = highs[i]
                    curr_low = lows[i]
                    
                    # Get window values
                    high_window = highs[i-window:i+window+1]
                    low_window = lows[i-window:i+window+1]
                    
                    # Check for swing points
                    if not np.isnan(curr_high) and curr_high == np.nanmax(high_window):
                        swing_high[i] = True
                    if not np.isnan(curr_low) and curr_low == np.nanmin(low_window):
                        swing_low[i] = True
                        
                except Exception as e:
                    print(f"Error processing point {i}: {str(e)}")
                    continue
            
            # Convert to pandas Series
            swing_high_series = pd.Series(swing_high, index=df.index)
            swing_low_series = pd.Series(swing_low, index=df.index)
            
            print(f"Successfully identified swing points. Highs: {sum(swing_high)}, Lows: {sum(swing_low)}")
            
            return swing_high_series, swing_low_series
            
        except Exception as e:
            print(f"Error in identify_swing_points: {str(e)}")
            # Return empty series in case of error
            return pd.Series(False, index=df.index), pd.Series(False, index=df.index)
    #     in identify_swing_points


    # Additional helper functions for new strategies
    def detect_momentum_shift(df, lookback=5):
        # print('in detect_momentum_shift')
        df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
        df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
        df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        df['into'] = pd.to_numeric(df['into'], errors='coerce')  # Added this line
        recent_range = df.tail(lookback)
        body_sizes = abs(recent_range['intc'] - recent_range['into'])
        wicks = recent_range['inth'] - recent_range['intl']
         # Calculate means and compare
        current_body_mean = body_sizes.mean()
        prev_body_mean = body_sizes.shift(lookback).mean()
        current_wick_mean = wicks.mean()
        prev_wick_mean = wicks.shift(lookback).mean()
        
     
        # Return the momentum shift condition
        return (pd.notna(current_body_mean) and 
                pd.notna(prev_body_mean) and 
                pd.notna(current_wick_mean) and 
                pd.notna(prev_wick_mean) and 
                current_body_mean < prev_body_mean and 
                current_wick_mean > prev_wick_mean)
        # return (body_sizes.mean() < body_sizes.shift(lookback).mean() and 
        #         wicks.mean() > wicks.shift(lookback).mean())

    def find_psych_levels(price, range_percent=1.0):
        # print('in find_psych_levels')
        base = round(price, -1)
        levels = []
        for i in range(-3, 4):
            level = base + (i * 10)
            if abs(level - price) / price <= range_percent/100:
                levels.append(level)
        return levels

    def detect_level_rejection(candle, level, option_type):
        # print('in detect_level_rejection')
        # df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
        # df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
        # df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        x=1
        return (abs(float(candle['inth' if option_type == 'call' else 'intl']) - level) / level <= 0.001 and
                float(candle['intc']) < float(candle['into']) if option_type == 'call' else float(candle['intc']) > float(candle['into']))

    def calculate_volatility(df, window=20):
        print('in calculate_volatility')
        df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
        df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
        # df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        return (df['inth'] - df['intl']).rolling(window).std()

    def identify_swing_pattern(df, window=5):
        # print('in identify_swing_pattern')
        df['inth'] = pd.to_numeric(df['inth'], errors='coerce')
        df['intl'] = pd.to_numeric(df['intl'], errors='coerce')
        df['intc'] = pd.to_numeric(df['intc'], errors='coerce')
        highs = df['inth'].rolling(window*2+1).apply(
            lambda x: x[window] == max(x), raw=True
        )
        lows = df['intl'].rolling(window*2+1).apply(
            lambda x: x[window] == min(x), raw=True
        )
        return highs, lows

    # Initialize tracking variables for all strategies
    current_mid = entry_price
    mid_count = 0
    vwap_crosses = 0
    structure_breaks = 0
    trailing_stop = entry_price * (0.98 if option_type == 'call' else 1.02)
    consec_crosses = 0
    
    # Initialize initial tracking variables from original strategy
    max_profit_seen = entry_price
    initial_target = entry_price * (1.005 if option_type == 'call' else 0.995)
    trailing_target = entry_price * (1.0015 if option_type == 'call' else 0.9985)

      # Initialize additional tracking variables
    momentum_shifts = 0
    level_rejections = 0
    vol_expansions = 0
    swing_failures = 0
    psych_levels = find_psych_levels(entry_price)
    prev_swing = None

    
    while True:
       
        new_candles = [row for row in df1 if datetime.strptime(str(row['time']), '%d-%m-%Y %H:%M:%S').timestamp() > last_checked_time]
        # print(f'new_candles', new_candles)
        new_candles = pd.DataFrame(new_candles)

        # Add this line to sort
        new_candles = new_candles.sort_values('time', ascending=True).reset_index(drop=True)
  
        df_candles = pd.DataFrame(new_candles)
        
        # Convert to numeric, handling errors
        for col in ['into', 'inth', 'intl', 'intc', 'v']:
            df_candles[col] = pd.to_numeric(df_candles[col], errors='coerce')
        
        df_candles['time'] = pd.to_datetime(df_candles['time'], format='%d-%m-%Y %H:%M:%S')
        df_candles = df_candles.set_index('time')
        
        
        if new_candles.empty:
            if datetime.now() >= end_timestamp:
                return None, None, "Strategy time limit reached"
            
            time.sleep(60)
            df = api.get_time_price_series(exchange='NSE', token=self.tokenid, starttime=last_checked_time, endtime=end_timestamp, interval=1)
            continue
        # print('before Get market context and indicators')
        # Get market context and indicators
        is_volatile, volume_threshold, atr = get_market_context(new_candles)
        new_vwap = calculate_vwap(new_candles)
        swing_high, swing_low = identify_swing_points(new_candles)
        # print(f'is_volatile',is_volatile)
        stop_loss_percent = 0.003 if is_volatile else 0.001

        for candle_time, candle in new_candles.iterrows():
            # print('in for loop')
            candle_time = datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S')
            last_checked_time = candle_time
            # print(f'candle_time',candle_time)
            current_price = float(candle['intc'])  # Using closing price for current price
            
           
            # print('checking 0 Initial stop loss hit - Highest priority exit to minimize losses')
            if option_type == 'call':
                # stop_loss_hit = float(candle['intl']) < entry_price * 0.997
                stop_loss_hit = round(float(candle['intl']), 3) < round(entry_price * 0.997, 3)
            else:
                # stop_loss_hit = float(candle['inth']) > entry_price * 1.003
                stop_loss_hit = round(float(candle['inth']), 3) > round(1.003 * entry_price, 3)

            if stop_loss_hit:
                exit_reason = "Initial stop loss hit"
                return candle_time, current_price, "Initial stop loss hit"
                # break

           
            if detect_momentum_shift(new_candles):
                print('sucess 2')
                momentum_condition = (
                    float(candle['intc']) < float(candle['into']) if option_type == 'call'
                    else float(candle['intc']) > float(candle['into'])
                )
                if momentum_condition:
                    momentum_shifts += 1
                    if momentum_shifts >= 2:
                        return candle_time, current_price, "Multiple momentum shifts detected"

           
            if option_type == 'call':
                max_profit_seen = max(max_profit_seen, candle['intc'])
            else:
                max_profit_seen = min(max_profit_seen, candle['intc'])
                
            if option_type == 'call':
                if max_profit_seen >= initial_target and round(current_price,4) <= round(max_profit_seen * 0.9985,4):
                    return candle_time, current_price, "Lock profits on pullback from peak"
            else:
                if max_profit_seen <= initial_target and round(current_price,4) >= round(max_profit_seen * 1.0025,4):
                    return candle_time, current_price, "Lock profits on pullback from trough"

            # print('checking 4 market_structure_stop conditions')
            if option_type == 'call':
                structure_break = (
                    swing_low.get(candle_time, False) and 
                    float(candle['intl']) < current_mid * (1 - stop_loss_percent)
                )
            else:
                structure_break = (
                    swing_high.get(candle_time, False) and 
                    float(candle['inth']) > current_mid * (1 + stop_loss_percent)
                )

            # print('checking Structure breaks (major trend change)')
            if structure_break:
                structure_breaks += 1
                if structure_breaks >= 2:
                    return candle_time, current_price, "Multiple structure breaks - Major trend change"

            
          

            # print('checking 5 Add swing failure checks')
            swing_highs, swing_lows = identify_swing_pattern(new_candles)
            if option_type == 'call' and swing_highs.iloc[-1]:
                if prev_swing and float(candle['inth']) > prev_swing * 1.001 and float(candle['intc']) < prev_swing:
                    swing_failures += 1
                prev_swing = float(candle['inth'])
            elif option_type == 'put' and swing_lows.iloc[-1]:
                if prev_swing and float(candle['intl']) < prev_swing * 0.999 and float(candle['intc']) > prev_swing:
                    swing_failures += 1
                prev_swing = float(candle['intl'])

            if swing_failures >= 2:
                return candle_time, current_price, "Multiple swing failures detected"

            # print('checking 8 volume_weighted_trailing_stop conditions')
            if option_type == 'call':
                vwap_condition = (
                   
                    (current_price > new_vwap).any() and
                    float(candle['v']) > volume_threshold
                )
            else:
                vwap_condition = (
                    (current_price < new_vwap).any() and
                    float(candle['v']) > volume_threshold
                )

            # print('checking VWAP trend confirmation (slower but reliable)')
            if vwap_condition:
                consec_crosses += 1
                if consec_crosses >= 3:
                    return candle_time, current_price, "Strong VWAP trend established"

            

        if 'exit_reason' in locals():
            option_df = api.get_time_price_series(exchange='NFO', token=option_tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
            option_df = pd.DataFrame(option_df)
            option_df['time'] = pd.to_datetime(option_df['time'], format='%d-%m-%Y %H:%M:%S')
            
            filtered_df = option_df[(option_df['time'] >= candle_time) & (option_df['time'] <= candle_time)]
            exit_candle = filtered_df
            exit_price = float(exit_candle['intl' if option_type == 'call' else 'inth'])
            return candle_time, exit_price, exit_reason
        else:
            # Define day_end_time at 15:29 on the same day as option_entry_time
            day_end_time = datetime.combine(option_entry_time.date(), time(14, 59, 0))
            # print(f'start_timestamp', start_timestamp)
            # print(f'day_end_time',day_end_time)
            
            # Fetch all candles from start time to day end time
            option_df = api.get_time_price_series(exchange='NFO', token=option_tokenid, starttime=start_timestamp, endtime=day_end_time.timestamp(), interval=1)
            option_df = pd.DataFrame(option_df)
            # print(option_df)
            # print(option_df.columns)
            option_df['time'] = pd.to_datetime(option_df['time'], format='%d-%m-%Y %H:%M:%S')
            
            # Sort by time and pick the last available candle before or exactly at 15:29
            option_df = option_df.sort_values(by='time')
            last_candle = option_df[option_df['time'] <= day_end_time].iloc[-1]  # Select the last row before or at 15:29
            
            # Extract the exit price based on the option type
            exit_price = float(last_candle['intl' if option_type == 'call' else 'inth'])
            exit_time = last_candle['time']
            exit_reason = "Exited at day end time (14:59) with last available candle"
            
            # Return or use exit time, exit price, and reason as needed
            # print("Exit Time:", exit_time)
            # print("Exit Price:", exit_price)
            # print("Exit Reason:", exit_reason)
            
            return exit_time, exit_price, exit_reason
  def get_option_price_by_time(self,target_datetime_str, tokenid=''):
        import datetime as datetime
        # print('inside get_option_price_by_time')
        current_price=''
        current_volume=''
        current_time=''
        tokenid=tokenid
        # target_datetime_str
        target_datetime = target_datetime_str
        start_datetime = target_datetime - datetime.timedelta(seconds=60)
        end_datetime = target_datetime + datetime.timedelta(seconds=60)
        # print(f'start_datetime', start_datetime)
        # print(f'end_datetime', end_datetime)
        date_input = start_datetime.strftime("%d-%m-%Y")
        starttime_input = start_datetime.strftime("%H:%M")
        endtime_input = end_datetime.strftime("%H:%M")
        start_timestamp, end_timestamp = self.get_start_end_timestamps(date_input, starttime_input, endtime_input)
        df = api.get_time_price_series(exchange='NFO', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
        if isinstance(df, list) and len(df) > 0:
            target_candle = None
            for candle in df:
                if isinstance(candle, dict) and 'time' in candle:
                    candle_time = datetime.datetime.strptime(candle['time'], "%d-%m-%Y %H:%M:%S")
                    # print(f'candle_time', candle_time)
                    # print(f'target_datetime', target_datetime)
                    if candle_time == target_datetime:
                        target_candle = candle
                        break
            if target_candle and isinstance(target_candle, dict) and 'intc' in target_candle and 'intv' in target_candle:
                current_price = float(target_candle['intc'])
                current_volume = int(target_candle['intv'])
                current_time = target_candle['time']
            else:
                x=1
                # print(f"Unable to find data for the specific time: {target_datetime_str}")
        else:
            x=1
            # print("Invalid or empty response from get_time_price_series")
        return current_price, current_time
  def run(self, signal_reversal):
    # print('inside run')
    
    position = np.zeros(self.data.shape[0])
    cash = position.copy() 
    pnl = position.copy()  # Add array to track P&L
    option_type = ['']*len(position)
    option_tokenid = ['']*len(position)
    option_entry_price = ['']*len(position)
    option_entry_time = ['']*len(position)
    global last_stock_traded
        # last_stock_traded=self.ticker
    global last_stock_traded_time
        # last_stock_traded_time=exit_time
    global stop_loss_reason
        # stop_loss_reason=reason
    global reversed_signal
        # reversed_signal = 0
      # Add a flag to track if we're in an opposite position
    is_opposite_position = False
    for i, (ts, row) in enumerate(self.data.iterrows()):

        if is_opposite_position == True:
            print('back from continue')
        
        # print('inside run for loop')
        if i == 0:
                cash[i] = self.starting_capital
                pnl[i] = 0  # Initialize P&L as 0
                continue

        
    
        if reversed_signal is not None and (reversed_signal == 1 or reversed_signal == -1):
            print(f'signal due to reversal: ', reversed_signal)
            signal = reversed_signal
           
        else:
             print('no signal reversal initial')
             signal = self._getSignal()
                
            
        # print(f'signal insiide run', signal)
        self.current_price = self.set_current_price()
        # print(f'row', row)
        # print(f'ts', ts)
        if signal != 0 and option_type[i] == '' and position[i] ==0:
            option_result, actual_option = self.get_option_data(signal)
            print('inside initital buy')
            print(f'actual_option', actual_option)
            if actual_option is not None:
                option_price = actual_option['price']
                option_strike=actual_option['strike']
                position[i] = 1
                # cash[i] -= position[i] * option_price
                cash[i] -= position[i] * option_price  # Deduct option cost from cash
                option_type[i] = 'call' if signal > 0 else 'put'
                if option_type[i] == 'call':
                    option_tokenid[i] = option_result.get('CE')['token']
                elif option_type[i] == 'put':
                    option_tokenid[i] = option_result.get('PE')['token']
                option_entry_price[i] = option_price  # Store the option entry price
                entry_price = self.current_price
                option_entry_price[i] = entry_price
                entry_time = actual_option['time']
                option_entry_time[i] = entry_time
                print(f"Position opened at {entry_time}")
                print(f"opening price: {entry_price}")
                print(f"option opening price: {option_price}")
            else:
                x=1
                break
                # print("No valid options found, skipping trade")
        if position[i] > 0:
            print('inside stop loss')
            if is_opposite_position == True:
                print('check becuase of new open positiion after initial stop loss')
            # print('checking exit price')
            exit_time, exit_price, reason = self.stop_loss_strategy(option_entry_price[i], option_entry_time[i], option_type[i], option_tokenid[i])
            # print(f'exit_time', exit_time)
            # print(f'exit_price', exit_price)
            # print(f'reason', reason)
            # cash[i] += position[i] * exit_price
            option_exit_price, option_exit_time = self.get_option_price_by_time(exit_time, option_tokenid[i])

             # Calculate P&L for this trade
            # trade_pnl = (option_exit_price - option_entry_price[i]) * position[i]
            # pnl[i] += trade_pnl  # Add trade P&L to cumulative P&L
            cash[i] += position[i] * option_exit_price  # Add proceeds from closing position
            lot_size = self.data['lot_size']
            underlying_price = self.data['option_underlying_price']
            # print(f'lot_size',lot_size)
            # Convert numpy arrays/pandas elements to simple values
            if hasattr(lot_size, 'iloc'):
                lot_size = lot_size.iloc[0]
            elif hasattr(lot_size, '__array__'):
                lot_size = lot_size[0]
                
            if hasattr(option_price, 'iloc'):
                option_price = option_price.iloc[0]
            elif hasattr(option_price, '__array__'):
                option_price = option_price[0]
                
            if hasattr(option_exit_price, 'iloc'):
                option_exit_price = option_exit_price.iloc[0]
            elif hasattr(option_exit_price, '__array__'):
                option_exit_price = option_exit_price[0]
            
            # Convert to float
            lot_size = float(lot_size)
            option_price = float(option_price)
            option_exit_price = float(option_exit_price)
            
            # Calculate PnL
            pnl = lot_size * (option_exit_price - option_price)
                
            #     # Reset position flags
            # position[i] = 0
            # option_type[i] = ''
            # option_tokenid[i] = ''
                
            print(f'option_exit_price', option_exit_price)
            print(f'option_exit_time', option_exit_time)
            
            position[i] = 0
            # option_type[i] = ''
            # option_tokenid[i] = ''
            print(f"Position closed at {exit_time}")
            print(f"Exit price: {exit_price}")
            print(f"Option Exit price: {option_exit_price}")
            print(f"Reason: {reason}")

            
            if option_price is not None:
                    print('breaking for loop')
                    break
            



            
        # global last_stock_traded
    last_stock_traded=self.ticker
    print(f'last_stock_traded',last_stock_traded)
        # global last_stock_traded_time
    last_stock_traded_time=exit_time
    print(f'last_stock_traded_time', last_stock_traded_time)
        # global stop_loss_reason
    stop_loss_reason=reason
    print(f'stop_loss_reason', stop_loss_reason)
        # global reversed_signal
    reversed_signal = 0
    print(f'reversed_signal', reversed_signal)
    print(reason)

    if reason == 'Initial stop loss hit':
            if signal == 1:
                reversed_signal = -1
                print(f'reversed_signal', reversed_signal)
            elif signal == -1:
                reversed_signal = 1
                print(f'reversed_signal', reversed_signal)
            else:
                reversed_signal = 0
                print(f'reversed_signal', reversed_signal)
                
                
        

    technicals = self.calculate_technicals_at_times(entry_time,option_exit_time)
    print(f'technicals',technicals)
    
       

    print('back to main run, finalizaing')
    print('calulating returns finalizaing')
    self.data['position'] = position
    self.data['cash'] = cash
    self.data['option_type'] = actual_option['type']
    self.data['pnl'] = pnl  # Add P&L column
    self.data['option_tokenid'] = option_tokenid  # Add this line
    self.data['option_entry_price'] = option_price  # Add this line
    self.data['option_entry_time'] = entry_time  # Add this line
    self.data['option_exit_price'] = option_exit_price  # Add this line
    self.data['option_exit_time'] = option_exit_time  # Add this line
    self.data['option_exit_reason'] = reason  # Add this line
    self.data['option_underlying_price'] = underlying_price  # Add this line
    self.data['option_lot_size'] = lot_size
    self.data['option_entry_underlying_price']= entry_price
    self.data['option_entry_strike'] = option_strike
    self.data['entry_SMA_10'] = technicals.get('entry_SMA_10', None)
    self.data['entry_EMA_20'] = technicals.get('entry_EMA_20', None)
    self.data['entry_RSI'] = technicals.get('entry_RSI', None)
    self.data['entry_ADX'] = technicals.get('entry_ADX', None)
    self.data['entry_MACD'] = technicals.get('entry_MACD', None)
    self.data['entry_MACD_signal'] = technicals.get('entry_MACD_signal', None)
    self.data['entry_MACD_hist'] = technicals.get('entry_MACD_hist', None)
    self.data['entry_ATR'] = technicals.get('entry_ATR', None)
    self.data['entry_BB_UPPER'] = technicals.get('entry_BB_UPPER', None)
    self.data['entry_BB_MIDDLE'] = technicals.get('entry_BB_MIDDLE', None)
    self.data['entry_BB_LOWER'] = technicals.get('entry_BB_LOWER', None)
    self.data['entry_OBV'] = technicals.get('entry_OBV', None)
    self.data['exit_SMA_10'] = technicals.get('exit_SMA_10', None)
    self.data['exit_EMA_20'] = technicals.get('exit_EMA_20', None)
    self.data['exit_RSI'] = technicals.get('exit_RSI', None)
    self.data['exit_ADX'] = technicals.get('exit_ADX', None)
    self.data['exit_MACD'] = technicals.get('exit_MACD', None)
    self.data['exit_MACD_signal'] = technicals.get('exit_MACD_signal', None)
    self.data['exit_MACD_hist'] = technicals.get('exit_MACD_hist', None)
    self.data['exit_ATR'] = technicals.get('exit_ATR', None)
    self.data['exit_BB_UPPER'] = technicals.get('exit_BB_UPPER', None)
    self.data['exit_BB_MIDDLE'] = technicals.get('exit_BB_MIDDLE', None)
    self.data['exit_BB_LOWER'] = technicals.get('exit_BB_LOWER', None)
    self.data['exit_OBV'] = technicals.get('exit_OBV', None)
      
    # self.data['portfolio'] = self.data['position'] * self.data['Close'] * (1 - self.option_cost) + self.data['cash']
      # Portfolio value is now just cash (which includes P&L from closed positions)
    self.data['portfolio'] = self.data['cash']
    self.data = calcReturns(self.data)
 
    
def time_range(start, end, delta):
    current = start
    while current <= end:
        yield current
        current += delta

def calcReturns(df):
    """ Log trade details to Excel for each strategy run and calculate returns """
    import pandas as pd
    import numpy as np
    from datetime import datetime
    import os
    excel_file = 'trade_log.xlsx'
    
    # Check if there was any trade in this run
    if 'pnl' in df.columns and not df['pnl'].isna().all():
        # Get non-zero PnL rows
        trade_rows = df[df['pnl'] != 0].dropna()
        
        if not trade_rows.empty:
            trade_data = []
            last_trade = trade_rows.iloc[-1]
            
            # Calculate exit price using iloc for integer-based indexing
            last_trade_idx = trade_rows.index[-1]
            df_idx = df.index.get_loc(last_trade_idx)
            
            # Calculate exit price safely using integer indexing
            if df_idx > 0:
                exit_price = df['portfolio'].iloc[df_idx] - df['portfolio'].iloc[df_idx - 1]
            else:
                exit_price = df['portfolio'].iloc[df_idx]
            
            trade_info = {
                'Date': datetime.now().strftime('%Y-%m-%d'),
                'Ticker': getattr(df, 'ticker', last_stock_traded),  # Get from global if attr not available
                'Option Type': last_trade['option_type'],
                'Option Underying Price': last_trade['option_underlying_price'],
                'Option Entry Underlying Price': last_trade['option_entry_underlying_price'],
                'Option Entry Strike': last_trade['option_entry_strike'],
                'Option Lot size': last_trade['option_lot_size'],
                'Entry Time': last_trade['option_entry_time'],
                'Entry Price': last_trade['option_entry_price'],
                'Exit Time': last_trade['option_exit_time'],
                'Exit Price': last_trade['option_exit_price'],
                'PnL': last_trade['pnl'],
                'Exit Reason': last_trade['option_exit_reason'],
                'Entry SMA_10': last_trade.get('entry_SMA_10', None),
                'Entry EMA_20': last_trade.get('entry_EMA_20', None),
                'Entry RSI': last_trade.get('entry_RSI', None),
                'Entry ADX': last_trade.get('entry_ADX', None),
                'Entry MACD': last_trade.get('entry_MACD', None),
                'Entry MACD_signal': last_trade.get('entry_MACD_signal', None),
                'Entry MACD_hist': last_trade.get('entry_MACD_hist', None),
                'Entry ATR': last_trade.get('entry_ATR', None),
                'Entry BB_UPPER': last_trade.get('entry_BB_UPPER', None),
                'Entry BB_MIDDLE': last_trade.get('entry_BB_MIDDLE', None),
                'Entry BB_LOWER': last_trade.get('entry_BB_LOWER', None),
                'Entry OBV': last_trade.get('entry_OBV', None),
                'Exit SMA_10': last_trade.get('exit_SMA_10', None),
                'Exit EMA_20': last_trade.get('exit_EMA_20', None),
                'Exit RSI': last_trade.get('exit_RSI', None),
                'Exit ADX': last_trade.get('exit_ADX', None),
                'Exit MACD': last_trade.get('exit_MACD', None),
                'Exit MACD_signal': last_trade.get('exit_MACD_signal', None),
                'Exit MACD_hist': last_trade.get('exit_MACD_hist', None),
                'Exit ATR': last_trade.get('exit_ATR', None),
                'Exit BB_UPPER': last_trade.get('exit_BB_UPPER', None),
                'Exit BB_MIDDLE': last_trade.get('exit_BB_MIDDLE', None),
                'Exit BB_LOWER': last_trade.get('exit_BB_LOWER', None),
                'Exit OBV': last_trade.get('exit_OBV', None)
            }
            trade_data.append(trade_info)
            
            # Create DataFrame for new trade
            new_trade_df = pd.DataFrame(trade_data)
              # If file exists, check for matching rows with the previous row before appending
            if os.path.exists(excel_file):
                existing_df = pd.read_excel(excel_file)
                if len(existing_df) > 0:  # Ensure there's at least one row to compare with
                    last_row = existing_df.iloc[-1]  # Get the last row
                    if (
                        last_row['Entry Time'] == trade_info['Entry Time']
                        and last_row['Ticker'] == trade_info['Ticker']
                        and last_row['Option Type'] == trade_info['Option Type']
                    ):
                        print("Skipping adding the trade data as it is a duplicate entry")
                         # Keep minimum required returns calculations for compatibility
                        df['strat_returns'] = df['portfolio'] / df['portfolio'].shift(1)
                        df['strat_log_returns'] = np.log(df['strat_returns'].replace([np.inf, -np.inf], np.nan))
                        df['log_returns'] = np.log(df['Close'] / df['Close'].shift(1))
                        return df  # Exit the function early
            
            try:
                # If file exists, append to it
                if os.path.exists(excel_file):
                    with pd.ExcelWriter(excel_file, mode='a', if_sheet_exists='overlay') as writer:
                        # Get the last row of existing data
                        existing_df = pd.read_excel(excel_file)
                        start_row = len(existing_df) + 1
                        new_trade_df.to_excel(writer, startrow=start_row, index=False, header=False)
                else:
                    new_trade_df.to_excel(excel_file, index=False)
                
                print(f"Trade logged successfully for {trade_info['Ticker']}")
                print(f"PnL: {trade_info['PnL']:.2f}")
                
                # Calculate cumulative PnL from Excel
                if os.path.exists(excel_file):
                    all_trades_df = pd.read_excel(excel_file)
                    cumulative_pnl = all_trades_df['PnL'].sum()
                    print(f"Cumulative PnL: {cumulative_pnl:.2f}")
            
            except Exception as e:
                print(f"Error writing to Excel: {e}")
    
    # Keep minimum required returns calculations for compatibility
    df['strat_returns'] = df['portfolio'] / df['portfolio'].shift(1)
    df['strat_log_returns'] = np.log(df['strat_returns'].replace([np.inf, -np.inf], np.nan))
    df['log_returns'] = np.log(df['Close'] / df['Close'].shift(1))
    return df

def getStratStats(log_returns: pd.Series, risk_free_rate=0.02):
    """ Calculate strategy statistics including total P&L from Excel """
    import os
    stats = {}
    
    if log_returns.empty:
        return stats
    
    # Calculate returns-based metrics
    stats['Strategy Returns'] = np.exp(log_returns.sum()) - 1
    
    # Add cumulative PnL from Excel if available
    excel_file = 'trade_log.xlsx'
    if os.path.exists(excel_file):
        try:
            trades_df = pd.read_excel(excel_file)
            stats['Total P&L'] = trades_df['PnL'].sum()
            stats['Number of Trades'] = len(trades_df)
            stats['Average P&L per Trade'] = stats['Total P&L'] / stats['Number of Trades']
            
            # Add win rate calculation
            winning_trades = len(trades_df[trades_df['PnL'] > 0])
            stats['Win Rate'] = winning_trades / len(trades_df) if len(trades_df) > 0 else 0
            
        except Exception as e:
            print(f"Error reading Excel for stats: {e}")
            stats['Total P&L'] = 0
            stats['Number of Trades'] = 0
            stats['Average P&L per Trade'] = 0
            stats['Win Rate'] = 0
    
    return {k: np.round(v, 4) if isinstance(v, (float, np.float_)) else v 
            for k, v in stats.items()}
    
import datetime as datetime
import datetime
import io
from docx import Document
import traceback
import linecache
import re
from collections import defaultdict

def extract_symbol_details(symbol):
    """
    Extract the base symbol from option symbols like 'AARTIIND28NOV24C520'
    Returns the base symbol (e.g., 'AARTIIND')
    """
    match = re.match(r'^([A-Z]+)', symbol)
    if match:
        return match.group(1)
    return None

def get_script_details(api, symbol):
    """
    Search for script details using the API
    Returns token and trading symbol
    """
    try:
        search_text = f"{symbol}-EQ"
        ret = api.searchscrip(exchange='NSE', searchtext=search_text)
        if ret['values']:
            return {
                'token': ret['values'][0]['token'],
                'tsym': ret['values'][0]['tsym']
            }
        return None
    except Exception as e:
        print(f"Error searching for {symbol}: {str(e)}")
        return None

def process_options_list(api, options_list, date):
    """
    Process a list of options and run the trading system for each unique base symbol
    """
    # Group options by base symbol
    symbol_groups = defaultdict(list)
    for option in options_list:
        base_symbol = extract_symbol_details(option)
        if base_symbol:
            symbol_groups[base_symbol].append(option)
    
    results = []
    processed_symbols = set()  # Keep track of processed symbols
    # global last_stock_traded
        # last_stock_traded=self.ticker
    # global last_stock_traded_time
        # last_stock_traded_time=exit_time
    # global stop_loss_reason
        # stop_loss_reason=reason
    global reversed_signal
    reversed_signal = 0
    
    for base_symbol, related_options in symbol_groups.items():
        if base_symbol in processed_symbols:
            print(f"Skipping {base_symbol} as it was already processed")
            continue
            
        print(f"Processing {base_symbol} (Options: {', '.join(related_options)})")
        
        script_details = get_script_details(api, base_symbol)
        if not script_details:
            print(f"Could not find script details for {base_symbol}")
            continue
            
        tokenid = script_details['token']
        
        # Trading time parameters
        market_start = datetime.datetime.strptime('12:00', '%H:%M')
        market_end = datetime.datetime.strptime('15:30', '%H:%M')
        delta = datetime.timedelta(minutes=1)
        window = datetime.timedelta(hours=1, minutes=30)
        
        output = io.StringIO()
        doc = Document()
        trading_system = None
        
        try:
            for current_time in time_range(market_start, market_end, delta):
                # print(f'Processing {base_symbol} at {current_time}')
                # print(f'window', window)
                # print(f'current_time',current_time)
                start_1 = current_time.strftime('%H:%M')
                # print(f'start_1',start_1)
                # time_str = current_time.split()[1]  # Get the time part
            
                # Create a datetime object with the extracted time
                time_obj = datetime.datetime.strptime(start_1, '%H:%M').time()
                start_time = max(current_time - window, market_start)
                end_time = min(current_time, market_end)
                start = start_time.strftime('%H:%M')
                end = end_time.strftime('%H:%M')
                # from datetime import datetime
                # time_str = selected_option['time'].split()[1]  # Get the time part
                    
                    # Create a datetime object with the extracted time
                # time_obj_1 = datetime.datetime.strptime(start, '%H:%M').time()
                # print(f'time_obj',time_obj)
                    # Check if the time is greater than 15:00:00
                if time_obj > datetime.datetime.strptime('15:00:00', '%H:%M:%S').time():
                        x=1
                        # print("Time is greater than 15:00:00")
                        continue

                # print(f'stop_loss_reason -1',stop_loss_reason)
                # print(f'reversed_signal -1', reversed_signal)
                
                # print(f'last_stock_traded',last_stock_traded)
                # print(f'last_stock_traded_time',last_stock_traded_time)
                if 'last_stock_traded' in globals() and 'last_stock_traded_time' in globals():
                    if base_symbol == last_stock_traded:
                        last_traded_time = last_stock_traded_time.strftime('%H:%M')
                        if last_traded_time > end:
                            x=1
                            # print(f'start',start)
                            # print(f'end',end)
                            # print(f"Skipping {base_symbol} as it was traded recently at {last_stock_traded_time}")
                            continue

                if 'stop_loss_reason' in globals():
                    if base_symbol == last_stock_traded:
                        if stop_loss_reason  == 'Initial stop loss hit':
                            print(f"Conditions met for {base_symbol} due to initial stop loss. Running StarterSystem")
                            winsound.Beep(1000, 500)
                            trading_system = StarterSystem(
                                base_symbol, 'NSE', start, end, date, tokenid
                            )
                            trading_system.run(reversed_signal)
                            
                
                # print(f"Checking conditions for {base_symbol} - start: {start}, end: {end}")
                
                conditions_met, signal = check_sideways_and_nadarya(
                    base_symbol, tokenid, 'NSE', start, end, date
                )
                
                # print(f'Signal for {base_symbol}: {signal}')
                
                if conditions_met and signal!=0:
                    global reversed_signal_dummy
                    reversed_signal_dummy = 0
                    if 'last_stock_traded' in globals() and 'last_stock_traded_time' in globals():
                        if base_symbol == last_stock_traded:
                            last_traded_time = last_stock_traded_time.strftime('%H:%M')
                            if last_traded_time > end:
                                x=1
                                # print(f'start',start)
                                # print(f'end',end)
                                # print(f"Skipping {base_symbol} as it was traded recently at {last_stock_traded_time}")
                                continue
                    print(f"Conditions met for {base_symbol}. Running StarterSystem")
                    # print(f'last_stock_traded',last_stock_traded)
                    # print(f'last_stock_traded_time',last_stock_traded_time)
                    winsound.Beep(1000, 500)
                    trading_system = StarterSystem(
                        base_symbol, 'NSE', start, end, date, tokenid
                    )
                    trading_system.run(reversed_signal_dummy)
                    # print(f'last_stock_traded',last_stock_traded)
                    # print(f'last_stock_traded_time', last_stock_traded_time)
                else:
                    x=1
                    # print(f"Conditions not met for {base_symbol} at {start}-{end}")
                    continue

                    
        except Exception as e:
            error_info = traceback.extract_tb(e.__traceback__)[-1]
            line_number = error_info.lineno
            filename = error_info.filename
            error_line = linecache.getline(filename, line_number).strip()
            print(f"Error processing {base_symbol}: {str(e)}")
            print(f"Error occurred on line {line_number}: {error_line}")
            continue
            
        processed_symbols.add(base_symbol)  # Mark this symbol as processed
        
        results.append({
            'symbol': base_symbol,
            'options': related_options,
            'trading_system': trading_system
        })
    
    return results

# Example usage
# options_list = new_list_nfo
options_list = ['BATAINDIA31JUL2025']
date = '20-06-2025'

# Run the processing
results = process_options_list(api, options_list, date)

print('completed')
