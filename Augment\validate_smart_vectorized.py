"""
Validate Smart Vectorized Backtester

This script validates the Smart Vectorized Backtester against the standalone version
to ensure 100% accuracy while maintaining performance benefits.
"""

import sys
import os
import logging
from datetime import datetime
import warnings
import numpy as np
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_specific_minutes():
    """Test specific minutes against standalone version"""
    print("\n" + "="*100)
    print("🔍 VALIDATING SMART VECTORIZED vs STANDALONE")
    print("="*100)
    
    from enhanced_nadarya_watson_signal import check_vander_enhanced
    from shared_sideways_signal_helper import check_sideways
    from smart_vectorized_backtester import SmartVectorizedBacktester
    
    # Test parameters
    ticker = 'BATAINDIA'
    date = '24-06-2025'
    start_time = '09:15'
    exchange = 'NSE'
    tokenid = '371'
    
    # Test critical minutes
    test_minutes = ['12:29', '12:30', '12:31', '12:50', '12:51', '12:54', '12:55']
    
    print(f"\n📊 TESTING {len(test_minutes)} CRITICAL MINUTES")
    print("-" * 80)
    
    # Create smart vectorized backtester
    smart_backtester = SmartVectorizedBacktester(
        ticker=ticker,
        exchange=exchange,
        start=start_time,
        end='13:00',
        date=date,
        tokenid=tokenid,
        enable_momentum_validation=True,
        enable_realtime_detection=True
    )
    
    results = []
    
    for minute in test_minutes:
        print(f"\n🕐 TESTING MINUTE: {minute}")
        print("-" * 40)
        
        # Test 1: Standalone Enhanced Nadarya Watson
        try:
            is_vander_standalone, vander_text_standalone = check_vander_enhanced(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date,
                starttime_input=start_time,
                endtime_input=minute,
                enable_momentum_validation=True,
                enable_realtime_detection=True
            )
            print(f"   📊 Standalone Nadarya: {is_vander_standalone}")
            print(f"   📊 Standalone Text: {vander_text_standalone}")
        except Exception as e:
            print(f"   ❌ Standalone Error: {str(e)}")
            is_vander_standalone = False
            vander_text_standalone = f"Error: {str(e)}"
        
        # Test 2: Standalone Sideways
        try:
            is_sideways_standalone, sideways_text_standalone = check_sideways(
                tokenid=tokenid,
                exchange=exchange,
                date_input=date,
                starttime_input=start_time,
                endtime_input=minute
            )
            print(f"   📊 Standalone Sideways: {is_sideways_standalone}")
        except Exception as e:
            print(f"   ❌ Sideways Error: {str(e)}")
            is_sideways_standalone = False
        
        # Test 3: Smart Vectorized (extract for this specific minute)
        minute_time = datetime.strptime(f"{date} {minute}", '%d-%m-%Y %H:%M')
        window_data = smart_backtester._extract_window_data(minute_time)
        
        # Test sideways
        is_sideways_smart, sideways_text_smart = smart_backtester._calculate_sideways_for_window(window_data)
        print(f"   📊 Smart Sideways: {is_sideways_smart}")
        
        # Test Nadarya Watson if sideways
        if is_sideways_smart:
            nadarya_result = smart_backtester._calculate_nadarya_watson_for_window(window_data)
            
            # Apply enhanced signal detection
            upper_band_signal = nadarya_result['upper_band_signal']
            lower_band_signal = nadarya_result['lower_band_signal']
            close_prices = nadarya_result['close_prices']
            
            # Check signals (same logic as smart backtester)
            current_upper_band = not np.isnan(upper_band_signal[-1]) if len(upper_band_signal) > 0 else False
            current_lower_band = not np.isnan(lower_band_signal[-1]) if len(lower_band_signal) > 0 else False
            
            minutes_check = -2
            upper_band_present_recent = any(not np.isnan(x) for x in upper_band_signal[minutes_check:]) if len(upper_band_signal) >= abs(minutes_check) else current_upper_band
            lower_band_present_recent = any(not np.isnan(x) for x in lower_band_signal[minutes_check:]) if len(lower_band_signal) >= abs(minutes_check) else current_lower_band
            
            upper_band_present_last_3 = current_upper_band or upper_band_present_recent
            lower_band_present_last_3 = current_lower_band or lower_band_present_recent
            
            # Momentum validation
            momentum_validated = True
            if upper_band_present_last_3:
                momentum_validated, momentum_text = smart_backtester._validate_momentum_strength(close_prices, 'upper')
            elif lower_band_present_last_3:
                momentum_validated, momentum_text = smart_backtester._validate_momentum_strength(close_prices, 'lower')
            
            has_signal_smart = (upper_band_present_last_3 or lower_band_present_last_3) and momentum_validated
            signal_type_smart = 'PUT' if upper_band_present_last_3 else 'CALL' if lower_band_present_last_3 else 'NONE'
            
            print(f"   📊 Smart Nadarya: {has_signal_smart}")
            print(f"   📊 Smart Signal: {signal_type_smart}")
        else:
            has_signal_smart = False
            signal_type_smart = 'NONE'
            print(f"   📊 Smart Nadarya: False (not sideways)")
        
        # Compare results
        standalone_has_signal = is_vander_standalone and is_sideways_standalone
        smart_has_signal = has_signal_smart and is_sideways_smart
        
        match = standalone_has_signal == smart_has_signal
        sideways_match = is_sideways_standalone == is_sideways_smart
        
        print(f"   🎯 Sideways Match: {'✅' if sideways_match else '❌'}")
        print(f"   🎯 Signal Match: {'✅' if match else '❌'}")
        
        results.append({
            'minute': minute,
            'standalone_sideways': is_sideways_standalone,
            'smart_sideways': is_sideways_smart,
            'standalone_signal': standalone_has_signal,
            'smart_signal': smart_has_signal,
            'sideways_match': sideways_match,
            'signal_match': match
        })
    
    # Summary
    print(f"\n" + "="*100)
    print("📊 VALIDATION SUMMARY")
    print("="*100)
    
    sideways_matches = sum(1 for r in results if r['sideways_match'])
    signal_matches = sum(1 for r in results if r['signal_match'])
    total_tests = len(results)
    
    print(f"Sideways Detection Accuracy: {sideways_matches}/{total_tests} ({sideways_matches/total_tests*100:.1f}%)")
    print(f"Signal Detection Accuracy: {signal_matches}/{total_tests} ({signal_matches/total_tests*100:.1f}%)")
    
    print(f"\n📋 DETAILED RESULTS:")
    for r in results:
        status = "✅" if r['signal_match'] else "❌"
        print(f"   {r['minute']}: {status} Standalone={r['standalone_signal']}, Smart={r['smart_signal']}")
    
    if signal_matches == total_tests:
        print(f"\n🎉 PERFECT MATCH! Smart Vectorized Backtester is 100% accurate!")
    else:
        print(f"\n⚠️ Some differences found. Need to investigate mismatches.")
    
    return results

def main():
    """Main execution function"""
    logger.info("🚀 Starting Smart Vectorized validation...")
    
    results = test_specific_minutes()
    
    logger.info("🎉 Validation completed!")

if __name__ == "__main__":
    main()
