# checking higher highs, higher lows, etc to detect the sideways trend during <PERSON><PERSON><PERSON> (check_vander) signal  to see if there is signal in last 3 minutes of selected script and time for MCX and NFO and the stock is trending sideways

import datetime
from scipy.signal import argrelextrema
import numpy as np
from collections import deque
from matplotlib.lines import Line2D
from datetime import timedelta

def live_data(data):
    # pairs = "ltcusdt"  # self.pairs
    t_frame = "1min"  # self.time_frame
    import pandas as pd
    from datetime import datetime

    response = data

    time = []
    open_ = []
    high_ = []
    low_ = []
    close_ = []
    volume = []

    for candle in response:
        time.append(datetime.strptime(candle['time'], '%d-%m-%Y %H:%M:%S'))
        open_.append(float(candle['into']))
        high_.append(float(candle['inth']))
        low_.append(float(candle['intl']))
        close_.append(float(candle['intc']))
        volume.append(float(candle['intv']))

    candles = pd.DataFrame({
        "Open": open_,
        "High": high_,
        "Low": low_,
        "Close": close_,
        "volume": volume,
        "time": time
    })

    candles = candles.set_index("time")
    return candles

def get_start_end_timestamps(date_input, starttime_input, endtime_input):
    # Parse the input date
    date_parts = date_input.split('-')
    year, month, day = int(date_parts[2]), int(date_parts[1]), int(date_parts[0])

    # Parse the input times
    start_time = datetime.datetime.strptime(starttime_input, '%H:%M').time()
    end_time = datetime.datetime.strptime(endtime_input, '%H:%M').time()

    # Combine the input date with the input times
    start_datetime = datetime.datetime(year, month, day, start_time.hour, start_time.minute)
    end_datetime = datetime.datetime(year, month, day, end_time.hour, end_time.minute)


    start_timestamp = start_datetime.timestamp()
    end_timestamp = end_datetime.timestamp()

    return start_timestamp, end_timestamp

 

import pandas as pd
# import tradingpatterns.tradingpatterns as tp1
import sys


import pandas as pd

import pandas as pd

def find_pivots(df, window_minutes=30):
    """
    Find pivots (higher highs, lower lows, lower highs, and higher lows) in the given DataFrame using non-overlapping windows.

    Args:
        df (pandas.DataFrame): The DataFrame containing 'High', 'Low', and 'Close' columns.
        window_minutes (int): The size of the non-overlapping window in minutes. Default is 30 minutes.

    Returns:
        pandas.DataFrame: The original DataFrame with an additional 'signal' column containing the pivot signals.
    """
    # Convert the index to datetime
    df.index = pd.to_datetime(df.index)
    # print('inside find pivot')
    # print(df)
    # Resample data to the specified window size and calculate the high and low values
    window_size = f'{window_minutes}min'
    resampled_data = df['High'].resample(window_size).max().to_frame('High')
    resampled_data['Low'] = df['Low'].resample(window_size).min()

    # Calculate differences between consecutive highs and lows
    high_diffs = resampled_data['High'].diff()
    low_diffs = resampled_data['Low'].diff()

    # Find higher high
    higher_high_mask = (high_diffs > 0) & (high_diffs.shift(-1) < 0)

    # Find lower low
    lower_low_mask = (low_diffs < 0) & (low_diffs.shift(-1) > 0)

    # Find lower high
    lower_high_mask = (high_diffs < 0) & (high_diffs.shift(-1) > 0)

    # Find higher low
    higher_low_mask = (low_diffs > 0) & (low_diffs.shift(-1) < 0)

    # Create signals column
    resampled_data['signal'] = ''
    resampled_data.loc[higher_high_mask, 'signal'] = 'HH'
    resampled_data.loc[lower_low_mask, 'signal'] = 'LL'
    resampled_data.loc[lower_high_mask, 'signal'] = 'LH'
    resampled_data.loc[higher_low_mask, 'signal'] = 'HL'

    # Merge the resampled DataFrame with the original DataFrame to include the 'Close' column
    result = pd.merge(df, resampled_data[['High', 'Low', 'signal']], left_index=True, right_index=True, how='left')

    return result

    # return resampled_data

# %matplotlib inline
import matplotlib.pyplot as plt1


import winsound


def plot_pivots(df, tokenid, exchange, current=False, date_input =None, starttime_input=None, endtime_input=None,  plot=False):
    plot=plot


    close = df['Close'].values
    dates = df.index
    sideways = False
    isvander = False
    overall_trend =''
    trend_label = ''
    signal_text = '' 
    isvander = False

    order = 5
    K = 2

    hh = getHigherHighs(close, order, K)
    hl = getHigherLows(close, order, K)
    ll = getLowerLows(close, order, K)
    lh = getLowerHighs(close, order, K)


    # plt.text(dates[-1], close[-1], trend_label, ha='right', va='top', fontsize=12, bbox=dict(facecolor='white', edgecolor='black', boxstyle='round,pad=0.5'))
    # Determine the overall trend
    isvander = False
    isvander, signal_text = check_vander1(tokenid=tokenid, exchange = exchange,  current=current, date_input=date_input, starttime_input = starttime_input, endtime_input = endtime_input)
    # print(f'isvander: {isvander}')
    latest_hh_confirmation = bool(hh and hh[-1][-1] >= order)
    latest_hl_confirmation = bool(hl and hl[-1][-1] >= order)
    latest_ll_confirmation = bool(ll and ll[-1][-1] >= order)
    latest_lh_confirmation = bool(lh and lh[-1][-1] >= order)

    if latest_hh_confirmation and not latest_ll_confirmation:
        overall_trend = 'Bullish'
    elif latest_ll_confirmation and not latest_hh_confirmation:
        overall_trend = 'Bearish'
    elif latest_hl_confirmation and latest_lh_confirmation:
        overall_trend = 'Sideways'
        sideways = True
        # isvander = False
        
          # # Find the overall latest trend
        latest_hh = hh[-1][-1] if hh else None
        latest_hl = hl[-1][-1] if hl else None
        latest_ll = ll[-1][-1] if ll else None
        latest_lh = lh[-1][-1] if lh else None
    
        latest_trend = max(latest_hh, latest_hl, latest_ll, latest_lh, key=lambda x: x if x is not None else float('-inf'))
    
        if latest_trend == latest_hh:
            trend_label = 'Latest Trend: Higher High,'
        elif latest_trend == latest_hl:
            trend_label = 'Latest Trend: Higher Low,'
        elif latest_trend == latest_ll:
            trend_label = 'Latest Trend: Lower Low,'
        else:
            trend_label = 'Latest Trend: Lower High,'
        if (isvander ==  True):
            new0=0
            
    else:
        overall_trend = 'Inconclusive'
   
    if (overall_trend == 'Sideways' and plot == True):
         if(isvander == True and plot == True):
            # print('plotting inside plot piviot function')
            colors = plt.rcParams['axes.prop_cycle'].by_key()['color']
         
            isvander, signal_text = check_vander(tokenid=tokenid, exchange = exchange,  current=current, date_input=date_input, starttime_input = starttime_input, endtime_input = endtime_input)
    
            plt.figure(figsize=(15, 8))
            plt.plot(df['Close'], zorder=0)
            _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[1]) for indices in hh]
            _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[2]) for indices in hl]
            _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[3]) for indices in ll]
            _ = [plt.plot(dates[list(indices)], close[list(indices)], c=colors[4]) for indices in lh]
        
            # Add confirmation markers
            _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[1], marker='^', s=100) for indices in hh]
            _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[2], marker='^', s=100) for indices in hl]
            _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[3], marker='v', s=100) for indices in ll]
            _ = [plt.scatter(dates[indices[-1]], close[indices[-1]], c=colors[4], marker='v', s=100) for indices in lh]

            plt.xlabel('Time')
            plt.ylabel('Price')
            plt.title('Potential Divergence Points for Closing Price')
            legend_elements = [
                Line2D([0], [0], color=colors[0], label='Close'),
                Line2D([0], [0], color=colors[1], label='Higher Highs'),
                Line2D([0], [0], color='w', marker='^', markersize=10, markerfacecolor=colors[1], label='Higher High Confirmation'),
                Line2D([0], [0], color=colors[2], label='Higher Lows'),
                Line2D([0], [0], color='w', marker='^', markersize=10, markerfacecolor=colors[2], label='Higher Lows Confirmation'),
                Line2D([0], [0], color=colors[3], label='Lower Lows'),
                Line2D([0], [0], color='w', marker='v', markersize=10, markerfacecolor=colors[3], label='Lower Lows Confirmation'),
                Line2D([0], [0], color=colors[4], label='Lower Highs'),
                Line2D([0], [0], color='w', marker='v', markersize=10, markerfacecolor=colors[4], label='Lower Highs Confirmation')
                ]
    
            plt.legend(handles=legend_elements)

            
            # winsound.PlaySound('SystemAsterisk', winsound.SND_ALIAS)
            plt.show()
            # plt.close()
         if (overall_trend == 'Sideways'):
           sideways = True
           # print("sideways")
    else: 
        no0=0
        # print(overall_trend)
    # print('completed plot piviot function')
    
    return sideways, overall_trend, trend_label, signal_text, isvander

def getHigherLows(data: np.array, order=5, K=2):
    '''
    Finds consecutive higher lows in price pattern.
    Must not be exceeded within the number of periods indicated by the width
    parameter for the value to be confirmed.
    K determines how many consecutive lows need to be higher.
    '''
    # Get lows
    low_idx = argrelextrema(data, np.less, order=order)[0]
    lows = data[low_idx]
    # Ensure consecutive lows are higher than previous lows
    extrema = []
    ex_deque = deque(maxlen=K)
    for i, idx in enumerate(low_idx):
        if i == 0:
            ex_deque.append(idx)
            continue
        if lows[i] < lows[i-1]:
            ex_deque.clear()

        ex_deque.append(idx)
        if len(ex_deque) == K:
            extrema.append(ex_deque.copy())

    return extrema

def getLowerHighs(data: np.array, order=5, K=2):
    '''
    Finds consecutive lower highs in price pattern.
    Must not be exceeded within the number of periods indicated by the width
    parameter for the value to be confirmed.
    K determines how many consecutive highs need to be lower.
    '''
    # Get highs
    high_idx = argrelextrema(data, np.greater, order=order)[0]
    highs = data[high_idx]
    # Ensure consecutive highs are lower than previous highs
    extrema = []
    ex_deque = deque(maxlen=K)
    for i, idx in enumerate(high_idx):
        if i == 0:
            ex_deque.append(idx)
            continue
        if highs[i] > highs[i-1]:
            ex_deque.clear()

        ex_deque.append(idx)
        if len(ex_deque) == K:
            extrema.append(ex_deque.copy())

    return extrema

def getHigherHighs(data: np.array, order=5, K=2):
    '''
    Finds consecutive higher highs in price pattern.
    Must not be exceeded within the number of periods indicated by the width
    parameter for the value to be confirmed.
    K determines how many consecutive highs need to be higher.
    '''
    # Get highs
    high_idx = argrelextrema(data, np.greater, order=5)[0]
    highs = data[high_idx]
    # Ensure consecutive highs are higher than previous highs
    extrema = []
    ex_deque = deque(maxlen=K)
    for i, idx in enumerate(high_idx):
        if i == 0:
            ex_deque.append(idx)
            continue
        if highs[i] < highs[i-1]:
            ex_deque.clear()

        ex_deque.append(idx)
        if len(ex_deque) == K:
            extrema.append(ex_deque.copy())

    return extrema

def getLowerLows(data: np.array, order=5, K=2):
    '''
    Finds consecutive lower lows in price pattern.
    Must not be exceeded within the number of periods indicated by the width
    parameter for the value to be confirmed.
    K determines how many consecutive lows need to be lower.
    '''
    # Get lows
    low_idx = argrelextrema(data, np.less, order=order)[0]
    lows = data[low_idx]
    # Ensure consecutive lows are lower than previous lows
    extrema = []
    ex_deque = deque(maxlen=K)
    for i, idx in enumerate(low_idx):
        if i == 0:
            ex_deque.append(idx)
            continue
        if lows[i] > lows[i-1]:
            ex_deque.clear()

        ex_deque.append(idx)
        if len(ex_deque) == K:
            extrema.append(ex_deque.copy())

    return extrema

    
def generate_sample_df_with_pattern(pattern, data1, date_input, starttime_input,endtime_input ):
    date_rng = pd.date_range(start='1/1/2020', end='1/10/2020', freq='D')
    data = {'date': date_rng}
    
    # start_date = pd.Timestamp('2024-05-24 12:34:00')
    # end_date = pd.Timestamp('2024-05-24 13:36:00')
    # date_rng = pd.date_range(start=start_date, end=end_date, freq='T')
    start_date = pd.Timestamp(date_input + ' ' + starttime_input)
    end_date = pd.Timestamp(date_input + ' ' + endtime_input)
    date_rng = pd.date_range(start=start_date, end=end_date, freq='min')

    data = {'date': date_rng}

    data = {'date': date_rng}
    if pattern == 'Head and Shoulder':
        data['Open'] = data1['Open'].tolist()
        data['High'] = data1['High'].tolist()
        data['Low'] = data1['Low'].tolist()
        data['Close'] = data1['Close'].tolist()
        data['Volume'] = data1['volume'].tolist()
    elif pattern == 'Inverse Head and Shoulder':
        data['Open'] = data1['Open'].tolist()
        data['High'] = data1['High'].tolist()
        data['Low'] = data1['Low'].tolist()
        data['Close'] = data1['Close'].tolist()
        data['Volume'] = data1['volume'].tolist()
    elif pattern == "Double Top" or "Double Bottom" or "Ascending Triangle" or "Descending Triangle":
        data['High'] = data1['High'].tolist()
        data['Low'] = data1['Low'].tolist()
        data['Close'] = data1['Close'].tolist()
        df = pd.DataFrame(data)
        df.iloc[3:5,1] =100
        df.iloc[6:8,1] =70
        df.iloc[6:9,2] =70
    df = pd.DataFrame(data)
    return df
    
def test_detect_head_shoulder(tokenid, exchange, hours = 1, current=False, date_input=None,starttime_input=None,endtime_input=None, plot = False):
    
    
     # Get the current date and time
    now = datetime.datetime.now()
    sideways = False
    overall_trend =''
    trend_label = ''
    signal_text = '' 
    isvander = False

    if date_input is None:
        # print('dates none')
            # Extract the date, start time, and end time from the current datetime
        date_input = now.date().strftime('%d-%m-%Y')
        endtime_input = now.time().strftime('%H:%M')
        starttime_input = (now - datetime.timedelta(hours=hours)).time().strftime('%H:%M')
    else:
        # print('dates not none')
        date_input = date_input
        endtime_input = endtime_input
        starttime_input = starttime_input
        
    
    # date_input = '27-05-2024'
    # starttime_input = '14:20'
    # endtime_input = '15:29'
            
    # print("date_input:", date_input)
    # print("starttime_input:", starttime_input)
    # print("endtime_input:", endtime_input)
            
            
            
            
            
            
            
            
    start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
    # print(f'starttime_input2', starttime_input)
    # print(f'endtime_input2', endtime_input)
    # print(f'date_input2', date_input)
    # print(f'Start timestamp: {start_timestamp}')
    # print(f'End timestamp: {end_timestamp}')
    #print()
            # Retry the API call up to 3 times if there is an error
    max_retries = 3
    retries = 0
    while retries < max_retries:
                try:
                    # data = api.get_time_price_series(exchange='NSE', token='11872', starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    if exchange == 'NFO' or exchange == 'NSE':
                        # print('fetching data for NFO/NSE')
                        data = api.get_time_price_series(exchange='NSE', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    elif exchange == 'MCX':
                        data = api.get_time_price_series(exchange='MCX', token=tokenid, starttime=start_timestamp, endtime=end_timestamp, interval=1)
                    break  # If the API call is successful, exit the loop
                except Exception as e:
                    print(f"Error: {e}")
                    retries += 1
                    if retries == max_retries:
                        print("Maximum number of retries reached. Unable to fetch data.")
                    else:
                        print(f"Retrying in 2 seconds... (Attempt {retries}/{max_retries})")
                        time.sleep(1)
                        #ver5
            
    if retries < max_retries:
                new0=0
            
    data = live_data(data)
    data = data.sort_values(by='time')
    data1=data
    # print(data)
            
    data = data['Close'].values
    # print(data1['Open'].values)

                
    # Generate data with head and shoulder pattern
    pattern = "Double Top"
    # df_head_shoulder = generate_sample_df_with_pattern("Head and Shoulder")
    # df_inv_shoulder = generate_sample_df_with_pattern("Inverse Head and Shoulder")
    df_pivot_1 = generate_sample_df_with_pattern(pattern, data1, date_input, starttime_input, endtime_input )

     # Set the 'date' column as the index
    df_pivot_1 = df_pivot_1.set_index('date')


    
    # df_with_detection = tp1.detect_head_shoulder(df_head_shoulder)
    # df_with_inv_detection = tp1.detect_head_shoulder(df_inv_shoulder)
    df_pivot = find_pivots(df_pivot_1, 2)
    
    # print(df_with_inv_detection)
    # print(df_head_shoulder)
    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_columns', None)
    # print(df_pivot)
    sideways, overall_trend, trend_label, signal_text,isvander=plot_pivots(df_pivot, tokenid, exchange, current, date_input, starttime_input, endtime_input, plot=plot)
    return sideways, overall_trend, trend_label, signal_text, isvander
    
    # assert "Head and Shoulder" in df_with_detection['head_shoulder_pattern'].values
    # assert "Inverse Head and Shoulder" in df_with_inv_detection['head_shoulder_pattern'].values

