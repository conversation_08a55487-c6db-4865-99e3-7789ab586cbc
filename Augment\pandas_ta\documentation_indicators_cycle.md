Title: Cycle - Pandas TA

URL Source: https://www.pandas-ta.dev/api/cycle/

Markdown Content:
* * *

ebsw[#](https://www.pandas-ta.dev/api/cycle/#src.pandas_ta.cycle.ebsw.ebsw "Permanent link")
--------------------------------------------------------------------------------------------

```
ebsw(
    close: Series,
    length: Int = None,
    bars: Int = None,
    initial_version: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Even Better SineWave

This indicator attempts to quantify market cycles using a low pass filter.

Sources
*   [rengel8](https://github.com/rengel8)
*   J<PERSON><PERSON><PERSON>s 'Cycle Analytics for Traders', 2014
*   [Pandas TA Issue #350](https://github.com/twopirllc/pandas-ta/issues/350)
*   [Proreal Code](https://www.prorealcode.com/prorealtime-indicators/even-better-sinewave/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | Max cycle/trend period. Values between `40-48` work as expected with minimum value: `39`. Default: `40` | `None` |
| `bars` | `int` | Period of low pass filtering. Default: `10` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | Replaces `na`'s with `value`. |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
The _default_ is more cycle oriented and seems to be less whipsaw-prune. The older version might offer earlier signals at medium and stronger reversals. Compared to TradingView, returns very close results but appears to be one bar earlier.

reflex[#](https://www.pandas-ta.dev/api/cycle/#src.pandas_ta.cycle.reflex.reflex "Permanent link")
--------------------------------------------------------------------------------------------------

```
reflex(
    close: Series,
    length: Int = None,
    smooth: Int = None,
    alpha: IntFloat = None,
    pi: IntFloat = None,
    sqrt2: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Reflex

This cycle indicator, by John F. Ehlers, attempts to reduce lag.

Sources
*   [rengel8](https://github.com/rengel8) (2021-08-11) based on the implementation from "ProRealCode"
*   [traders.com](http://traders.com/Documentation/FEEDbk_docs/2020/02/TradersTips.html)
*   [prorealcode](https://www.prorealcode.com/prorealtime-indicators/reflex-and-trendflex-indicators-john-f-ehlers/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `smooth` | `int` | SuperSmoother period. Default: `20` | `None` |
| `alpha` | `float` | Alpha weight of Difference Sums. Default: `0.04` | `None` |
| `pi` | `float` | Ehlers's truncated value: `3.14159`. Default: `3.14159` | `None` |
| `sqrt2` | `float` | Ehlers's truncated value: `1.414`. Default: `1.414` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | Replaces `na`'s with `value`. |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Tip
This implementation has a separate control parameter for the internal applied SuperSmoother.

Note
John F. Ehlers introduced two indicators within the article "Reflex: A New Zero-Lag Indicator” in February 2020, TASC magazine. One of which is Reflex, a lag reduced cycle indicator. Both indicators (Reflex/Trendflex) are oscillators that complement each other with the focus for cycle and trend.

