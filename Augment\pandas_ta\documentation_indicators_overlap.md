Title: Overlap - Pandas TA

URL Source: https://www.pandas-ta.dev/api/overlap/

Markdown Content:
* * *

alligator[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.alligator.alligator "Permanent link")
---------------------------------------------------------------------------------------------------------------

```
alligator(
    close: Series,
    jaw: Int = None,
    teeth: Int = None,
    lips: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

<PERSON>tor

This indicator, by <PERSON>, attempts to identify trends.

Sources
*   [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=175&Name=<PERSON>_<PERSON>_Alligator)
*   [tradingview](https://www.tradingview.com/scripts/alligator/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `jaw` | `int` | Jaw period. Default: `13` | `None` |
| `teeth` | `int` | Teeth period. Default: `8` | `None` |
| `lips` | `int` | Lips period. Default: `5` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

Tip
To avoid data leaks, offsets are to be done manually.

Note
Williams believed the fx market trends between 15% and 30% of the time. Otherwise it is range bound. Inspired by fractal geometry, where the outputs are meant to resemble an alligator opening and closing its mouth. It It consists of 3 lines: Jaw, Teeth, and Lips which each have differing lengths.

* * *

alma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.alma.alma "Permanent link")
------------------------------------------------------------------------------------------------

```
alma(
    close: Series,
    length: Int = None,
    sigma: IntFloat = None,
    dist_offset: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Arnaud Legoux Moving Average

This indicator attempts to reduce lag with Gaussian smoothing.

Sources
*   [prorealcode](https://www.prorealcode.com/prorealtime-indicators/alma-arnaud-legoux-moving-average/)
*   [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=475&Name=Moving_Average_-_Arnaud_Legoux)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `9` | `None` |
| `sigma` | `float` | Smoothing value. Default `6.0` | `None` |
| `dist_offset` | `float` | Distribution offset, range `[0, 1]`. Default `0.85` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

dema[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.dema.dema "Permanent link")
------------------------------------------------------------------------------------------------

```
dema(
    close: Series,
    length: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Double Exponential Moving Average

This indicator attempts to create a smoother average with less lag than the EMA.

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/double-exponential-moving-average-dema/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Warning
TA-Lib Correlation: `np.float64(0.9999894518202522)`

Tip
Corrective contributions welcome!

* * *

ema[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.ema.ema "Permanent link")
---------------------------------------------------------------------------------------------

```
ema(
    close: Series,
    length: Int = None,
    talib: bool = None,
    presma: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Exponential Moving Average

This Moving Average is more responsive than the Simple Moving Average (SMA).

Sources
*   [investopedia](https://www.investopedia.com/ask/answers/122314/what-exponential-moving-average-ema-formula-and-how-ema-calculated.asp)
*   [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:moving_averages)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `presma` | `bool` | Initialize with SMA like TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `adjust` | `bool` |  |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

fwma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.fwma.fwma "Permanent link")
------------------------------------------------------------------------------------------------

```
fwma(
    close: Series,
    length: Int = None,
    asc: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Fibonacci's Weighted Moving Average

This indicator, by Kevin Johnson, is similar to a Weighted Moving Average (WMA) where the weights are based on the Fibonacci Sequence.

Sources
*   Kevin Johnson

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `asc` | `bool` | Recent values weigh more. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

hilo[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.hilo.hilo "Permanent link")
------------------------------------------------------------------------------------------------

```
hilo(
    high: Series,
    low: Series,
    close: Series,
    high_length: Int = None,
    low_length: Int = None,
    mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Gann HiLo Activator

This indicator, by Robert Krausz, uses two different Moving Averages to identify trends.

Sources
*   Gann HiLo Activator, , Stocks & Commodities Magazine, 1998
*   [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=447&Name=Gann_HiLo_Activator)
*   [tradingview](https://www.tradingview.com/script/XNQSLIYb-Gann-High-Low/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `high_length` | `int` | High period. Default: `13` | `None` |
| `low_length` | `int` | Low period. Default: `21` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"sma"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

Note
Increasing `high_length` and decreasing `low_length` is better for short trades and vice versa for long trades.

* * *

hl2[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.hl2.hl2 "Permanent link")
---------------------------------------------------------------------------------------------

```
hl2(
    high: Series,
    low: Series,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

HL2

HL2 is the midpoint/average of high and low.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)`. Only works when offset. |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

hlc3[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.hlc3.hlc3 "Permanent link")
------------------------------------------------------------------------------------------------

```
hlc3(
    high: Series,
    low: Series,
    close: Series,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

HLC3

HLC3 is the average of high, low and close.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)`. Only works when offset. |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

hma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.hma.hma "Permanent link")
---------------------------------------------------------------------------------------------

```
hma(
    close: Series,
    length: Int = None,
    mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Hull Moving Average

This indicator, by Alan Hull, attempts to reduce lag compared to classical moving averages.

Sources
*   [Alan Hull](https://alanhull.com/hull-moving-average)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `mamode` | `str` | One of: 'ema', 'sma', or 'wma'. Default: `"wma"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

hwma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.hwma.hwma "Permanent link")
------------------------------------------------------------------------------------------------

```
hwma(
    close: Series,
    na: IntFloat = None,
    nb: IntFloat = None,
    nc: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Holt-Winter Moving Average

This indicator uses a three parameter Holt-Winter Moving Average for smoothing.

Sources
*   [rengel8](https://github.com/rengel8) based on a publication for MetaTrader 5.
*   [mql5](https://www.mql5.com/en/code/20856)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `na` | `float` | Smoothed series parameter (from 0 to 1). Default: 0.2 | `None` |
| `nb` | `float` | Trend parameter (from 0 to 1). Default: 0.1 | `None` |
| `nc` | `float` | Seasonality parameter (from 0 to 1). Default: 0.1 | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Name | Type | Description |
| --- | --- | --- |
| `Series` | `Series` | 1 column |

ichimoku[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.ichimoku.ichimoku "Permanent link")
------------------------------------------------------------------------------------------------------------

```
ichimoku(
    high: Series,
    low: Series,
    close: Series,
    tenkan: Int = None,
    kijun: Int = None,
    senkou: Int = None,
    include_chikou: bool = True,
    offset: Int = None,
    **kwargs: DictLike,
) -> Tuple[DataFrame, DataFrame]
```

Ichimoku Kinkō Hyō

A forecasting model used in Japaese financial markets Pre WWII.

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/ichimoku-ich/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `tenkan` | `int` | Tenkan period. Default: `9` | `None` |
| `kijun` | `int` | Kijun period. Default: `26` | `None` |
| `senkou` | `int` | Senkou period. Default: `52` | `None` |
| `include_chikou` | `bool` | Whether to include chikou component. Default: `True` | `True` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |
| `lookahead` | `value` | To avoid data leakage set to `False`. |

Returns:

| Type | Description |
| --- | --- |
| `Tuple[pd.DataFrame, pd.DataFrame]` | * Historical DataFrame, 5 columns * Forward Looking DataFrame, 2 columns |

Possible Data Leak
Set `lookahead=False` to avoid data leakage. Issue [#60](https://github.com/twopirllc/pandas-ta/issues/60#).

* * *

jma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.jma.jma "Permanent link")
---------------------------------------------------------------------------------------------

```
jma(
    close: Series,
    length: IntFloat = None,
    phase: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Jurik Moving Average Average

This indicator, by Mark Jurik, attempts to eliminate noise. It claims to have extremely low lag, is very smooth and is responsive to gaps.

Sources
*   [mql5](https://c.mql5.com/forextsd/forum/164/jurik_1.pdf)
*   [prorealcode](https://www.prorealcode.com/prorealtime-indicators/jurik-volatility-bands/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `7` | `None` |
| `phase` | `float` | Phase value between [-100, 100]. Default: `0` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

kama[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.kama.kama "Permanent link")
------------------------------------------------------------------------------------------------

```
kama(
    close: Series,
    length: Int = None,
    fast: Int = None,
    slow: Int = None,
    mamode: str = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Kaufman's Adaptive Moving Average

This indicator, by Perry Kaufman, attempts to find the overall trend by adapting to volatility.

Sources
*   [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:kaufman_s_adaptive_moving_average)
*   [tradingview](https://www.tradingview.com/script/wZGOIz9r-REPOST-Indicators-3-Different-Adaptive-Moving-Averages/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `fast` | `int` | Fast MA period. Default: `2` | `None` |
| `slow` | `int` | Slow MA period. Default: `30` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"sma"` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

linreg[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.linreg.linreg "Permanent link")
------------------------------------------------------------------------------------------------------

```
linreg(
    close: Series,
    length: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Linear Regression Moving Average

This indicator is a simplified version of Standard Linear Regression. It is one variable rolling regression whereas a Standard Linear Regression is between two or more variables.

Sources
*   [TA Lib](https://ta-lib.org/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `angle` | `bool` | Returns the slope angle in radians. Default: `False` |
| `degrees` | `bool` | Return the slope angle in degrees. Default: `False` |
| `intercept` | `bool` | Return the intercept. Default: `False` |
| `r` | `bool` | Return the 'r' correlation. Default: `False` |
| `slope` | `bool` | Return the slope. Default: `False` |
| `tsf` | `bool` | Return the Time Series Forecast value. Default: `False` |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Warning
TA-Lib Correlation: `np.float64(0.9985638477660118)`

Tip
Corrective contributions welcome!

* * *

mama[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.mama.mama "Permanent link")
------------------------------------------------------------------------------------------------

```
mama(
    close: Series,
    fastlimit: IntFloat = None,
    slowlimit: IntFloat = None,
    prenan: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

MESA Adaptive Moving Average

This indicator, aka the Mother of All Moving Averages by John Ehlers, attempts to adapt to volatility by using a Hilbert Transform Discriminator

Sources
*   [Ehlers's Mother of Adaptive Moving Averages](http://traders.com/documentation/feedbk_docs/2014/01/traderstips.html)
*   [tradingview](https://www.tradingview.com/script/foQxLbU3-Ehlers-MESA-Adaptive-Moving-Average-LazyBear/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `fastlimit` | `float` | Fast limit. Default: `0.5` | `None` |
| `slowlimit` | `float` | Slow limit. Default: `0.05` | `None` |
| `prenan` | `int` | Prenans to apply. TV-LB `3`, Ehler's `6`, TA Lib `32`. Default: `3` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

Tip
**FAMA** also included

* * *

mcgd[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.mcgd.mcgd "Permanent link")
------------------------------------------------------------------------------------------------

```
mcgd(
    close: Series,
    length: Int = None,
    c: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

McGinley Dynamic Indicator

This indicator, by John R. McGinley, is not a moving average but a differential smoothing technique.

Sources
*   John R. McGinley, a Certified Market Technician (CMT) and former editor of the Market Technicians Association's Journal of Technical Analysis.
*   [investopedia](https://www.investopedia.com/articles/forex/09/mcginley-dynamic-indicator.asp)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `c` | `float` | Denominator multiplier. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
Sometimes `c` is set to `0.6`.

* * *

midpoint[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.midpoint.midpoint "Permanent link")
------------------------------------------------------------------------------------------------------------

```
midpoint(
    close: Series,
    length: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Midpoint

The Midpoint is the average of the rolling high and low of period length.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `2` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

midprice[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.midprice.midprice "Permanent link")
------------------------------------------------------------------------------------------------------------

```
midprice(
    high: Series,
    low: Series,
    length: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Midprice

The Midprice is the average of the rolling high and low of period length.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `length` | `int` | The period. Default: `2` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

ohlc4[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.ohlc4.ohlc4 "Permanent link")
---------------------------------------------------------------------------------------------------

```
ohlc4(
    open_: Series,
    high: Series,
    low: Series,
    close: Series,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

OHLC4

OHLC4 is the average of open, high, low and close.

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `open_` | `Series` | `open` Series | _required_ |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)`. Only works when offset. |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

pwma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.pwma.pwma "Permanent link")
------------------------------------------------------------------------------------------------

```
pwma(
    close: Series,
    length: Int = None,
    asc: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Pascal's Weighted Moving Average

This indicator, by Kevin Johnson, creates a weighted moving average using Pascal's Triangle.

Sources
*   Kevin Johnson

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `asc` | `bool` | Ascending. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 1 column |

* * *

rma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.rma.rma "Permanent link")
---------------------------------------------------------------------------------------------

```
rma(
    close: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

wildeR's Moving Average

This indicator, by Wilder, is simply an EMA where _alpha_ is the recipical of its _length_.

Sources
*   [incrediblecharts](https://www.incrediblecharts.com/indicators/wilder_moving_average.php)
*   [thinkorswim](https://tlc.thinkorswim.com/center/reference/Tech-Indicators/studies-library/V-Z/WildersSmoothing)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

sinwma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.sinwma.sinwma "Permanent link")
------------------------------------------------------------------------------------------------------

```
sinwma(
    close: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Sine Weighted Moving Average

This indicator is a weighted average using sine cycles where the central values have greater weight.

Source
*   [Everget](https://www.tradingview.com/u/everget/)
*   [tradingview](https://www.tradingview.com/script/6MWFvnPO-Sine-Weighted-Moving-Average/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

sma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.sma.sma "Permanent link")
---------------------------------------------------------------------------------------------

```
sma(
    close: Series,
    length: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Simple Moving Average

This indicator is the the textbook moving average, a rolling sum of values divided by the window period (or length).

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/simple-moving-average-sma/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `adjust` | `bool` | Adjust the values. Default: `True` |
| `presma` | `bool` | If True, uses SMA for initial value. |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

smma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.smma.smma "Permanent link")
------------------------------------------------------------------------------------------------

```
smma(
    close: Series,
    length: Int = None,
    mamode: str = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

SMoothed Moving Average

This indicator attempts to confirm trends and identify support and resistance areas. It tries to reduce noise in contrast to reducing lag.

Sources
*   [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=173&Name=Moving_Average_-_Smoothed)
*   [tradingview](https://www.tradingview.com/scripts/smma/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"sma"` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
A core component of Bill Williams Alligator indicator.

* * *

ssf[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.ssf.ssf "Permanent link")
---------------------------------------------------------------------------------------------

```
ssf(
    close: Series,
    length: Int = None,
    everget: bool = None,
    pi: IntFloat = None,
    sqrt2: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Ehlers's Super Smoother Filter

This indicator, by John F. Ehlers's © 2013, is a (Recursive) Digital Filter that attempts to reduce lag and remove aliases. This version has two poles.

Sources
*   [mql5](https://www.mql5.com/en/code/588)
*   [traders.com](http://traders.com/documentation/feedbk_docs/2014/01/traderstips.html)
*   [tradingview](https://www.tradingview.com/script/VdJy0yBJ-Ehlers-Super-Smoother-Filter/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `everget` | `bool` | Everget's implementation of ssf that uses pi instead of 180 for the b factor of ssf. Default: `False` | `None` |
| `pi` | `float` | The default is Ehlers's truncated value: `3.14159`. Default: `3.14159` | `None` |
| `sqrt2` | `float` | The default is Ehlers's truncated value: `1.414`. Default: `1.414` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
*   Everget's calculation on TradingView: `pi=np.pi`, `sqrt2=np.sqrt(2)`

Danger
Possible Data Leak

* * *

ssf3[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.ssf3.ssf3 "Permanent link")
------------------------------------------------------------------------------------------------

```
ssf3(
    close: Series,
    length: Int = None,
    pi: IntFloat = None,
    sqrt3: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
)
```

Ehlers's 3 Pole Super Smoother Filter

This indicator, by John F. Ehlers's © 2013, is a (Recursive) Digital Filter that attempts to reduce lag and remove aliases. This version has two poles.

Sources
*   [mql5](https://www.mql5.com/en/code/589)
*   [tradingview](https://www.tradingview.com/script/VdJy0yBJ-Ehlers-Super-Smoother-Filter/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `pi` | `float` | The value of `PI`. The default is Ehler's truncated value: `3.14159`. Default: `3.14159` | `None` |
| `sqrt3` | `float` | The value of `sqrt(3)` to use. The default is Ehler's truncated value: `1.732`. Default: `1.732` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
*   Everget's calculation on TradingView: `pi=np.pi`, `sqrt2=np.sqrt(2)`

* * *

supertrend[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.supertrend.supertrend "Permanent link")
------------------------------------------------------------------------------------------------------------------

```
supertrend(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    atr_length: Int = None,
    multiplier: IntFloat = None,
    atr_mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Supertrend

This indicator attempts to identify trend direction as well as support and resistance levels.

Sources
*   [freebsensetips](http://www.freebsensetips.com/blog/detail/7/What-is-supertrend-indicator-its-calculation)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `7` | `None` |
| `atr_length` | `int` | ATR period. Default: `length` | `None` |
| `multiplier` | `float` | Coefficient for upper and lower band distance to midrange. Default: `3.0` | `None` |
| `atr_mamode` | `str)` | MA type to be used for ATR calculation. See `help(ta.ma)`. Default: `"rma"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 4 columns |

* * *

swma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.swma.swma "Permanent link")
------------------------------------------------------------------------------------------------

```
swma(
    close: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Symmetric Weighted Moving Average

This indicator is based on a Symmetric Weighted Moving Average where weights are based on a symmetric triangle.

Source
*   [tradingview](https://www.tradingview.com/study-script-reference/#fun_swma)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
*   `n=3` ->`[1, 2, 1]`
*   `n=4` ->`[1, 2, 2, 1]`
*   etc...

t3[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.t3.t3 "Permanent link")
------------------------------------------------------------------------------------------

```
t3(
    close: Series,
    length: Int = None,
    a: IntFloat = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

T3

This indicator, by Tim Tillson, attempts to be smoother and more responsive relative to other moving averages.

Sources
*   [binarytribune](http://www.binarytribune.com/forex-trading-indicators/t3-moving-average-indicator/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `a` | `float` | The a factor, 0 < a < 1. Default: `0.7` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `adjust` | `bool` |  |
| `presma` | `bool` | If True, uses SMA for initial value. |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Warning
TA-Lib Correlation: `np.float64(0.9999994265973177)`

Tip
Corrective contributions welcome!

* * *

tema[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.tema.tema "Permanent link")
------------------------------------------------------------------------------------------------

```
tema(
    close: Series,
    length: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Triple Exponential Moving Average

This indicator attempts to be less laggy than the EMA.

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/triple-exponential-moving-average-tema/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `adjust` | `bool` |  |
| `presma` | `bool` | If True, uses SMA for initial value. |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Warning
TA-Lib Correlation: `np.float64(0.9999355450605516)`

Tip
Corrective contributions welcome!

* * *

trima[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.trima.trima "Permanent link")
---------------------------------------------------------------------------------------------------

```
trima(
    close: Series,
    length: Int = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Triangular Moving Average

This indicator is a weighted moving average where the shape of the weights are triangular with the greatest weight is in the middle of the period.

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/triangular-moving-average-trima/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `adjust` | `bool` |  |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
tma = sma(sma(src, ceil(length / 2)), floor(length / 2) + 1) # Tradingview trima = sma(sma(x, n), n) # Tradingview

Warning
TA-Lib Correlation: `np.float64(0.9991752493891967)`

Tip
Corrective contributions welcome!

* * *

vidya[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.vidya.vidya "Permanent link")
---------------------------------------------------------------------------------------------------

```
vidya(
    close: Series,
    length: Int = None,
    talib: bool = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Variable Index Dynamic Average

This indicator, by Tushar Chande, is similar to an EMA but it has a dynamically adjusted lookback period dependent based on CMO.

Sources
*   [perfecttrendsystem](https://www.perfecttrendsystem.com/blog_mt4_2/en/vidya-indicator-for-mt4)
*   [tradingview](https://www.tradingview.com/script/hdrf0fXV-Variable-Index-Dynamic-Average-VIDYA/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
Sometimes used as a moving average or a trend identifier.

* * *

wcp[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.wcp.wcp "Permanent link")
---------------------------------------------------------------------------------------------

```
wcp(
    high: Series,
    low: Series,
    close: Series,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Weighted Closing Price

This indicator is a weighted value of: high, low and twice the close.

Sources
*   [fmlabs](https://www.fmlabs.com/reference/default.htm?url=WeightedCloses.htm)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

wma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.wma.wma "Permanent link")
---------------------------------------------------------------------------------------------

```
wma(
    close: Series,
    length: Int = None,
    asc: bool = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Weighted Moving Average

This indicator is a Moving Average where the weights are linearly increasing and the most recent data has the heaviest weight.

Sources
*   [wikipedia](https://en.wikipedia.org/wiki/Moving_average#Weighted_moving_average)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `asc` | `bool` | Recent values weigh more. Default: `True` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

zlma[#](https://www.pandas-ta.dev/api/overlap/#src.pandas_ta.overlap.zlma.zlma "Permanent link")
------------------------------------------------------------------------------------------------

```
zlma(
    close: Series,
    length: Int = None,
    mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Zero Lag Moving Average

This indicator, by John Ehlers and Ric Way, attempts to eliminate the lag often introduced in other moving averages.

Sources
*   [wikipedia](https://en.wikipedia.org/wiki/Zero_lag_exponential_moving_average)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `mamode` | `str` | One of: "dema", "ema", "fwma", "hma", "linreg", "midpoint", "pwma", "rma", "sinwma", "ssf", "swma", "t3", "tema", "trima", "vidya", or "wma". Default: `"ema"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

