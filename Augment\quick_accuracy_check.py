"""
Quick Accuracy Check

Compare the true vectorized results with the known Ver4 baseline.
"""

import json
import sys

def main():
    print("🔍 QUICK ACCURACY CHECK")
    print("=" * 50)
    
    # Known baseline from previous comparison
    ver4_signals = 0  # Ver4 generated 0 signals for 12:00-12:30
    
    # Load true vectorized results
    try:
        with open('true_vectorized_results_BATAINDIA_20062025_20250625_024613.json', 'r') as f:
            vectorized_results = json.load(f)
            
        vectorized_signals = vectorized_results.get('total_signals_generated', 0)
        
        print(f"📊 Ver4 Baseline: {ver4_signals} signals")
        print(f"🚀 True Vectorized: {vectorized_signals} signals")
        print(f"🎯 Match: {'✅ YES' if ver4_signals == vectorized_signals else '❌ NO'}")
        
        if ver4_signals != vectorized_signals:
            print("\n❌ ACCURACY ISSUE DETECTED!")
            print("The true vectorized approach is still not matching Ver4 exactly.")
            print("The Nadarya Watson implementation needs further refinement.")
        else:
            print("\n✅ PERFECT ACCURACY ACHIEVED!")
            print("The true vectorized approach matches Ver4 exactly.")
            
    except FileNotFoundError:
        print("❌ Results file not found")
        
if __name__ == "__main__":
    main()
