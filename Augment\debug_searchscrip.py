"""
Debug searchscrip method to understand why it's returning None
"""

import sys
import os
import logging

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Add parent directory to path for api_helper
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_searchscrip_debug():
    """Debug searchscrip method"""
    try:
        logger.info("🔍 Testing searchscrip method...")
        
        # Import shared API manager
        from shared_api_manager import get_api
        
        # Get API instance
        api = get_api()
        logger.info("✅ API instance obtained")
        
        # Test different search patterns based on official documentation
        test_cases = [
            ('NSE', 'REL'),           # From official example
            ('NSE', 'NIFTY'),         # Index search
            ('NSE', 'NIFTY-EQ'),      # With -EQ suffix
            ('NSE', 'RELIANCE'),      # Full name
            ('NSE', 'RELIANCE-EQ'),   # Full name with -EQ
            ('NSE', 'BATAINDIA'),     # User's example
            ('NSE', 'BATAINDIA-EQ'),  # User's example with -EQ
            ('NSE', 'ACC'),           # From documentation example
            ('NSE', 'ACC-EQ'),        # From documentation example with -EQ
        ]
        
        for exchange, searchtext in test_cases:
            logger.info(f"🔍 Testing: exchange='{exchange}', searchtext='{searchtext}'")
            
            try:
                result = api.searchscrip(exchange=exchange, searchtext=searchtext)
                logger.info(f"📊 Result type: {type(result)}")
                logger.info(f"📊 Result: {result}")
                
                if result is None:
                    logger.warning(f"⚠️ searchscrip returned None for {searchtext}")
                elif isinstance(result, dict):
                    if 'stat' in result:
                        logger.info(f"📊 Status: {result.get('stat')}")
                        if result.get('stat') == 'Ok' and 'values' in result:
                            logger.info(f"📊 Found {len(result['values'])} results")
                            for i, item in enumerate(result['values'][:3]):  # Show first 3
                                logger.info(f"  {i+1}. {item.get('tsym')} - Token: {item.get('token')}")
                        elif result.get('stat') == 'Not_Ok':
                            logger.error(f"❌ API Error: {result.get('emsg', 'Unknown error')}")
                    else:
                        logger.info(f"📊 Unexpected response format: {result}")
                else:
                    logger.info(f"📊 Unexpected result type: {type(result)} - {result}")
                    
            except Exception as e:
                logger.error(f"❌ Exception for {searchtext}: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
            
            logger.info("---")
        
        # Test with debug logging enabled
        logger.info("🔍 Testing with debug logging...")
        
        # Enable debug logging for the API
        api_logger = logging.getLogger('NorenApi')
        api_logger.setLevel(logging.DEBUG)
        
        result = api.searchscrip(exchange='NSE', searchtext='RELIANCE-EQ')
        logger.info(f"📊 Debug result: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Debug test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_other_api_methods():
    """Test other API methods to see if they work"""
    try:
        logger.info("🧪 Testing other API methods...")
        
        from shared_api_manager import get_api
        api = get_api()
        
        # Test get_limits (we know this works)
        logger.info("💰 Testing get_limits()...")
        limits = api.get_limits()
        logger.info(f"📊 get_limits result: {limits}")
        
        # Test get_quotes with a known token (ACC token from documentation)
        logger.info("📈 Testing get_quotes() with token '22' (ACC)...")
        quotes = api.get_quotes(exchange='NSE', token='22')
        logger.info(f"📊 get_quotes result: {quotes}")
        
        # Test get_security_info
        logger.info("🔍 Testing get_security_info() with token '22' (ACC)...")
        security_info = api.get_security_info(exchange='NSE', token='22')
        logger.info(f"📊 get_security_info result: {security_info}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Other API methods test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Main debug execution"""
    logger.info("🚀 Starting searchscrip debug...")
    
    # Test searchscrip
    searchscrip_success = test_searchscrip_debug()
    
    # Test other methods
    other_methods_success = test_other_api_methods()
    
    if searchscrip_success and other_methods_success:
        logger.info("🎉 All debug tests completed!")
    else:
        logger.error("❌ Some debug tests failed.")
    
    logger.info("🏁 Debug completed.")

if __name__ == "__main__":
    main()
