"""
Test ADX Filter for 1-Minute Charts

This script tests the new ADX-based filter that replaces ATR.
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_adx_filter():
    """Test the ADX filter functionality"""
    print("🎯 Testing ADX Filter for 1-Minute Charts")
    print("="*60)
    
    try:
        # Import the enhanced backtester
        import importlib.util
        spec = importlib.util.spec_from_file_location("smart_vectorized_backtester_copy", "smart_vectorized_backtester copy.py")
        smart_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(smart_module)
        SmartVectorizedBacktester = smart_module.SmartVectorizedBacktester
        
        # Test parameters
        ticker = 'BATAINDIA'
        exchange = 'NSE'
        date = '25-06-2025'
        start_time = '09:15'
        end_time = '13:00'
        tokenid = '371'
        
        print(f"📊 Testing with {ticker} on {exchange}")
        print(f"📅 Date: {date}, Time: {start_time} to {end_time}")
        
        # Test 1: No filters
        print(f"\n1️⃣ NO FILTERS")
        print("-" * 30)
        
        no_filters = SmartVectorizedBacktester(
            ticker=ticker, exchange=exchange, start=start_time, end=end_time,
            date=date, tokenid=tokenid,
            enable_momentum_validation=True, enable_realtime_detection=True,
            enable_institutional_filters=False, enable_breakout_protection=False
        )
        
        try:
            no_filter_signals = no_filters.run_smart_vectorized_backtest()
            print(f"✅ No filters: {len(no_filter_signals)} signals")
        except Exception as e:
            print(f"❌ No filters test failed: {str(e)}")
            no_filter_signals = []
        
        # Test 2: ADX filter only
        print(f"\n2️⃣ ADX FILTER ONLY")
        print("-" * 30)
        
        adx_only = SmartVectorizedBacktester(
            ticker=ticker, exchange=exchange, start=start_time, end=end_time,
            date=date, tokenid=tokenid,
            enable_momentum_validation=True, enable_realtime_detection=True,
            enable_institutional_filters=True, enable_breakout_protection=True,
            # ADX filter configuration
            enable_adx_filter=True,           # ✅ Enable ADX filter
            enable_volume_filter=False,      # ❌ Disable others
            enable_momentum_divergence_filter=False,
            enable_bb_width_filter=False,
            enable_pattern_filter=False,
            enable_structure_filter=False
        )
        
        try:
            adx_only_signals = adx_only.run_smart_vectorized_backtest()
            print(f"✅ ADX only: {len(adx_only_signals)} signals")
            
            # Show detailed signal information
            if adx_only_signals:
                print(f"\n📋 ADX-Filtered Signal Details:")
                for i, signal in enumerate(adx_only_signals[:3], 1):  # Show first 3
                    print(f"   {i}. {signal['time']}: {signal['signal_type']}")
                    print(f"      Reason: {signal['reason'][:80]}...")
                    if 'institutional_filters' in signal:
                        print(f"      Filters: {signal['institutional_filters'][:60]}...")
                    if 'breakout_analysis' in signal:
                        print(f"      Analysis: {signal['breakout_analysis'][:60]}...")
                    print()
            
        except Exception as e:
            print(f"❌ ADX filter test failed: {str(e)}")
            adx_only_signals = []
        
        # Analysis
        print(f"\n3️⃣ ADX FILTER EFFECTIVENESS")
        print("-" * 40)
        
        if no_filter_signals and adx_only_signals:
            filtered_out = len(no_filter_signals) - len(adx_only_signals)
            filter_rate = (filtered_out / len(no_filter_signals) * 100) if len(no_filter_signals) > 0 else 0
            
            print(f"📊 Original signals: {len(no_filter_signals)}")
            print(f"📊 ADX-filtered signals: {len(adx_only_signals)}")
            print(f"📊 Filtered out: {filtered_out} ({filter_rate:.1f}%)")
            
            if filtered_out > 0:
                print(f"✅ ADX filter successfully removed {filtered_out} potentially false signals!")
                print(f"💡 Filter effectiveness: {filter_rate:.1f}% of signals filtered")
            else:
                print(f"ℹ️ All signals passed ADX filter (low volatility period)")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def explain_adx_filter():
    """Explain how the ADX filter works"""
    print(f"\n🎓 ADX FILTER EXPLANATION")
    print("="*40)
    
    print("🎯 What is ADX?")
    print("   • ADX = Average Directional Index")
    print("   • Measures strength of trend direction")
    print("   • Values: 0-100 (higher = stronger trend)")
    print("   • Used by professional traders worldwide")
    print()
    
    print("🚀 How ADX Filter Works:")
    print("   1. Calculate current ADX value")
    print("   2. Compare with ADX from 3 candles ago")
    print("   3. If ADX suddenly spikes >15 points = strong breakout")
    print("   4. Reject reversal signals during breakout conditions")
    print()
    
    print("💡 Why ADX is Better than ATR for 1-Minute:")
    print("   ✅ Detects directional momentum (not just volatility)")
    print("   ✅ More sensitive to sudden trend changes")
    print("   ✅ Works better on short timeframes")
    print("   ✅ Used by institutional traders")
    print("   ❌ ATR only measures volatility, not direction")
    print()
    
    print("🔧 ADX Filter Settings:")
    print("   • Period: 10 candles (optimized for 1-minute)")
    print("   • Threshold: 15-point sudden increase")
    print("   • Lookback: 3 candles")
    print("   • Purpose: Detect sudden directional breakouts")

def show_cli_usage():
    """Show how to use the CLI filter selection"""
    print(f"\n📱 CLI FILTER SELECTION USAGE")
    print("="*45)
    
    print("When you run the backtester, you'll see:")
    print()
    print("🏛️ Institutional Filter Options:")
    print("   1. No Filters (Original signals)")
    print("   2. ADX Only (Recommended for 1-minute)")
    print("   3. ADX + Volume (Conservative)")
    print("   4. All Filters (Very Conservative)")
    print()
    print("🏛️ Select filter level (1-4, default=2): ")
    print()
    print("💡 Recommendations:")
    print("   • Choice 1: Maximum signals, higher risk")
    print("   • Choice 2: Balanced (recommended)")
    print("   • Choice 3: Conservative, fewer signals")
    print("   • Choice 4: Very conservative, minimal signals")

def main():
    """Run ADX filter tests"""
    print("🚀 ADX FILTER TEST FOR 1-MINUTE CHARTS")
    print("="*50)
    
    # Explain ADX filter
    explain_adx_filter()
    
    # Show CLI usage
    show_cli_usage()
    
    # Test the filter
    success = test_adx_filter()
    
    print(f"\n🎯 SUMMARY")
    print("="*20)
    
    if success:
        print("✅ ADX filter is working correctly!")
        print("🎯 Key improvements:")
        print("   • Replaced ATR with ADX (better for 1-minute)")
        print("   • Detects sudden directional momentum")
        print("   • Removes momentum rejection logs (less noise)")
        print("   • Added CLI filter selection")
        print("   • Shows ADX rejection logs for learning")
        print()
        print("💡 Use 'ADX Only' configuration for best results!")
    else:
        print("❌ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
