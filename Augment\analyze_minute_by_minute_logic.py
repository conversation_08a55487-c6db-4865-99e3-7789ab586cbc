"""
Minute-by-Minute Logic Analysis

This script analyzes the exact step-by-step calculations in enhanced Nadarya Watson
and sideways detection to understand how parameters change for each minute.

The goal is to understand the subtle changes that occur minute-by-minute so we can
replicate this logic in a vectorized approach that runs in parallel but maintains
the exact same accuracy as the standalone version.
"""

import sys
import os
import logging
from datetime import datetime, timedelta
import warnings
import pandas as pd
import numpy as np
import math
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_nadarya_watson_minute_by_minute():
    """
    Analyze how Nadarya Watson calculations change minute by minute
    """
    print("\n" + "="*100)
    print("🔍 ANALYZING NADARYA WATSON MINUTE-BY-MINUTE LOGIC")
    print("="*100)
    
    from shared_api_manager import get_api
    from enhanced_nadarya_watson_signal import get_start_end_timestamps, live_data
    
    # Test parameters
    ticker = 'BATAINDIA'
    date = '24-06-2025'
    start_time = '09:15'
    exchange = 'NSE'
    tokenid = '371'
    
    # Test 5 consecutive minutes
    test_minutes = ['12:29', '12:30', '12:31', '12:32', '12:33']
    
    api = get_api()
    
    print(f"\n📊 TESTING {len(test_minutes)} CONSECUTIVE MINUTES")
    print("-" * 80)
    
    for minute in test_minutes:
        print(f"\n🕐 MINUTE: {minute}")
        print("-" * 40)
        
        # Get data from market start to this minute (like standalone does)
        start_timestamp, end_timestamp = get_start_end_timestamps(date, start_time, minute)
        
        data = api.get_time_price_series(
            exchange='NSE', 
            token=tokenid, 
            starttime=start_timestamp, 
            endtime=end_timestamp, 
            interval=1
        )
        
        data_df = live_data(data)
        data_df = data_df.sort_values(by='time')
        close_prices = data_df['Close'].values
        
        print(f"   📊 Data points: {len(close_prices)}")
        print(f"   📊 Data range: {data_df.index[0].strftime('%H:%M')} to {data_df.index[-1].strftime('%H:%M')}")
        print(f"   📊 Last close: {close_prices[-1]:.2f}")
        
        # Perform Nadarya Watson calculation (Ver4 exact)
        h = 8
        k = 1.75
        src = close_prices
        y = []
        
        # Step 1: Calculate Nadarya Watson curve
        sum_e = 0
        for i in range(len(close_prices)):
            sum_val = 0
            sumw = 0
            for j in range(len(close_prices)):
                w = math.exp(-(math.pow(i-j, 2)/(h*h*2)))
                sum_val += src[j] * w
                sumw += w
            y2 = sum_val / sumw
            sum_e += abs(src[i] - y2)
            y.append(y2)
        
        # Step 2: Calculate MAE
        mae = sum_e / len(close_prices) * k
        
        # Step 3: Calculate bands
        upper_band = []
        lower_band = []
        upper_band_signal = []
        lower_band_signal = []
        
        for i in range(len(close_prices)):
            upper_band.append(y[i] + mae * k)
            lower_band.append(y[i] - mae * k)
            
            if close_prices[i] > upper_band[i]:
                upper_band_signal.append(close_prices[i])
            else:
                upper_band_signal.append(np.nan)
                
            if close_prices[i] < lower_band[i]:
                lower_band_signal.append(close_prices[i])
            else:
                lower_band_signal.append(np.nan)
        
        # Analyze current minute signals
        current_upper_signal = not np.isnan(upper_band_signal[-1])
        current_lower_signal = not np.isnan(lower_band_signal[-1])
        
        print(f"   📊 MAE: {mae:.4f}")
        print(f"   📊 Last y value: {y[-1]:.2f}")
        print(f"   📊 Upper band: {upper_band[-1]:.2f}")
        print(f"   📊 Lower band: {lower_band[-1]:.2f}")
        print(f"   📊 Upper signal: {'YES' if current_upper_signal else 'NO'}")
        print(f"   📊 Lower signal: {'YES' if current_lower_signal else 'NO'}")
        
        # Check last 3 minutes for signals (Ver4 logic)
        if len(upper_band_signal) >= 3:
            upper_last_3 = any(not np.isnan(x) for x in upper_band_signal[-3:])
            lower_last_3 = any(not np.isnan(x) for x in lower_band_signal[-3:])
            print(f"   📊 Upper last 3: {'YES' if upper_last_3 else 'NO'}")
            print(f"   📊 Lower last 3: {'YES' if lower_last_3 else 'NO'}")
        
        # Show how parameters change
        if minute != test_minutes[0]:  # Not first minute
            print(f"   🔄 Parameter changes from previous minute:")
            print(f"      - Data points changed")
            print(f"      - MAE recalculated based on new data")
            print(f"      - All y values recalculated")
            print(f"      - All bands recalculated")

def analyze_sideways_minute_by_minute():
    """
    Analyze how sideways detection changes minute by minute
    """
    print("\n" + "="*100)
    print("🔍 ANALYZING SIDEWAYS DETECTION MINUTE-BY-MINUTE LOGIC")
    print("="*100)
    
    from shared_api_manager import get_api
    from enhanced_nadarya_watson_signal import get_start_end_timestamps, live_data
    
    # Test parameters
    ticker = 'BATAINDIA'
    date = '24-06-2025'
    start_time = '09:15'
    exchange = 'NSE'
    tokenid = '371'
    
    # Test 5 consecutive minutes
    test_minutes = ['12:29', '12:30', '12:31', '12:32', '12:33']
    
    api = get_api()
    
    print(f"\n📊 TESTING {len(test_minutes)} CONSECUTIVE MINUTES")
    print("-" * 80)
    
    for minute in test_minutes:
        print(f"\n🕐 MINUTE: {minute}")
        print("-" * 40)
        
        # Get data from market start to this minute (like standalone does)
        start_timestamp, end_timestamp = get_start_end_timestamps(date, start_time, minute)
        
        data = api.get_time_price_series(
            exchange='NSE', 
            token=tokenid, 
            starttime=start_timestamp, 
            endtime=end_timestamp, 
            interval=1
        )
        
        data_df = live_data(data)
        data_df = data_df.sort_values(by='time')
        
        close_prices = data_df['Close'].values
        high_prices = data_df['High'].values
        low_prices = data_df['Low'].values
        
        print(f"   📊 Data points: {len(close_prices)}")
        print(f"   📊 Last close: {close_prices[-1]:.2f}")
        
        # Ver4 exact sideways calculation
        lookback_period = 20  # Ver4 hardcoded value
        sideways_threshold = 0.02  # 2% threshold
        
        if len(close_prices) >= lookback_period:
            # Calculate on last 20 candles
            recent_prices = close_prices[-lookback_period:]
            recent_highs = high_prices[-lookback_period:]
            recent_lows = low_prices[-lookback_period:]
            
            # Calculate metrics
            max_price = np.max(recent_highs)
            min_price = np.min(recent_lows)
            price_range = max_price - min_price
            avg_price = np.mean(recent_prices)
            range_percentage = price_range / avg_price
            
            price_std = np.std(recent_prices)
            mean_price = np.mean(recent_prices)
            coefficient_of_variation = price_std / mean_price
            
            # Trend analysis
            first_half = recent_prices[:lookback_period//2]
            second_half = recent_prices[lookback_period//2:]
            first_half_avg = np.mean(first_half)
            second_half_avg = np.mean(second_half)
            trend_change = abs(second_half_avg - first_half_avg) / first_half_avg
            
            # Ver4 exact conditions
            is_low_volatility = range_percentage < sideways_threshold
            is_stable_oscillation = coefficient_of_variation < 0.015
            is_no_strong_trend = trend_change < 0.01
            
            is_sideways = is_low_volatility and is_stable_oscillation and is_no_strong_trend
            
            print(f"   📊 Range %: {range_percentage:.4f} ({'✅' if is_low_volatility else '❌'})")
            print(f"   📊 CV: {coefficient_of_variation:.4f} ({'✅' if is_stable_oscillation else '❌'})")
            print(f"   📊 Trend: {trend_change:.4f} ({'✅' if is_no_strong_trend else '❌'})")
            print(f"   📊 Sideways: {'YES' if is_sideways else 'NO'}")
            
            # Show how the 20-candle window changes
            print(f"   📊 Window: {data_df.index[-lookback_period].strftime('%H:%M')} to {data_df.index[-1].strftime('%H:%M')}")
        else:
            print(f"   ❌ Insufficient data: {len(close_prices)} < {lookback_period}")

def analyze_key_differences():
    """
    Analyze the key differences between standalone and vectorized approaches
    """
    print("\n" + "="*100)
    print("🎯 KEY DIFFERENCES ANALYSIS")
    print("="*100)
    
    print("\n1️⃣ NADARYA WATSON DIFFERENCES:")
    print("-" * 50)
    print("   STANDALONE (Correct):")
    print("   - Gets data from market_start to current_minute for EACH minute")
    print("   - Recalculates ALL parameters (y, MAE, bands) for each minute")
    print("   - Uses different data window size for each minute")
    print("   - Example: 12:29 uses 09:15-12:29, 12:30 uses 09:15-12:30")
    print("")
    print("   VECTORIZED (Incorrect):")
    print("   - Gets data from market_start to end_time ONCE")
    print("   - Uses same full data for all minutes")
    print("   - Doesn't recalculate parameters for each minute")
    print("   - Example: All minutes use 09:15-13:00 data")
    
    print("\n2️⃣ SIDEWAYS DETECTION DIFFERENCES:")
    print("-" * 50)
    print("   STANDALONE (Correct):")
    print("   - Gets data from market_start to current_minute for EACH minute")
    print("   - Uses last 20 candles from that specific data window")
    print("   - Different 20-candle window for each minute")
    print("   - Example: 12:29 uses last 20 from 09:15-12:29 data")
    print("")
    print("   VECTORIZED (Incorrect):")
    print("   - Uses same full data for all minutes")
    print("   - Doesn't adjust the 20-candle window per minute")
    print("   - Uses fixed window from full data")

def propose_solution():
    """
    Propose the solution for accurate vectorized implementation
    """
    print("\n" + "="*100)
    print("💡 PROPOSED SOLUTION")
    print("="*100)
    
    print("\n🚀 SMART VECTORIZED APPROACH:")
    print("-" * 50)
    print("1. Pre-fetch full data once (09:15 to 13:00)")
    print("2. For each minute, extract the correct data window:")
    print("   - Extract data from market_start to current_minute")
    print("   - This gives us the exact same data as standalone")
    print("3. Apply the exact same calculations on the extracted window:")
    print("   - Nadarya Watson: Recalculate y, MAE, bands for each window")
    print("   - Sideways: Use last 20 candles from each window")
    print("4. Process all minutes in parallel/batch but with correct windows")
    print("")
    print("🎯 KEY INSIGHT:")
    print("   The vectorized version should simulate running the standalone")
    print("   version minute-by-minute, but do it efficiently in batch.")
    print("")
    print("✅ BENEFITS:")
    print("   - 100% accuracy (same as standalone)")
    print("   - Massive performance gain (single API call)")
    print("   - Parallel processing of multiple minutes")
    print("   - Maintains exact Ver4 logic")

def main():
    """Main execution function"""
    logger.info("🚀 Starting minute-by-minute logic analysis...")
    
    try:
        analyze_nadarya_watson_minute_by_minute()
        analyze_sideways_minute_by_minute()
        analyze_key_differences()
        propose_solution()
        
        logger.info("✅ Analysis completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error during analysis: {str(e)}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
