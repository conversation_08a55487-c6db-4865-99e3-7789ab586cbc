"""
Demo script for Technical Indicators Analyzer

This script demonstrates the usage of the Technical Indicators Analyzer
with sample data since we don't have access to live market data in this demo.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from technical_indicators_analyzer import TechnicalIndicatorsAnalyzer

def create_realistic_market_data(ticker="BATAINDIA", date_str="24-06-2025"):
    """Create realistic market data for demonstration"""
    
    # Parse date
    date_obj = datetime.strptime(date_str, '%d-%m-%Y')
    
    # Create minute-by-minute data from 9:15 to 15:30
    start_time = date_obj.replace(hour=9, minute=15)
    end_time = date_obj.replace(hour=15, minute=30)
    
    # Generate time series
    time_range = []
    current_time = start_time
    while current_time <= end_time:
        time_range.append(current_time)
        current_time += timedelta(minutes=1)
    
    # Generate realistic price data
    np.random.seed(42)  # For reproducible results
    base_price = 1000.0
    
    # Create price movements with some trends and volatility
    price_changes = []
    trend = 0.0
    volatility = 2.0
    
    for i in range(len(time_range)):
        # Add some trend changes throughout the day
        if i % 60 == 0:  # Every hour
            trend = np.random.normal(0, 0.5)
        
        # Generate price change
        change = np.random.normal(trend, volatility)
        price_changes.append(change)
    
    # Calculate prices
    prices = [base_price]
    for change in price_changes:
        new_price = max(prices[-1] + change, 1.0)  # Ensure positive prices
        prices.append(new_price)
    
    # Create OHLCV data
    data = []
    for i, (timestamp, close_price) in enumerate(zip(time_range, prices[1:])):
        open_price = prices[i]
        
        # Generate high and low
        high_offset = abs(np.random.normal(0, 1.5))
        low_offset = abs(np.random.normal(0, 1.5))
        
        high = max(open_price, close_price) + high_offset
        low = min(open_price, close_price) - low_offset
        
        # Generate volume
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
    
    df = pd.DataFrame(data, index=time_range)
    return df

def demo_specific_candles():
    """Demo analyzing specific candles"""
    print("\n" + "="*60)
    print("🎯 DEMO: Analyzing Specific Candles")
    print("="*60)
    
    # Create sample data
    market_data = create_realistic_market_data()
    print(f"✅ Created market data with {len(market_data)} candles")
    
    # Initialize analyzer
    analyzer = TechnicalIndicatorsAnalyzer()
    
    # Select some interesting times
    candle_times = ["10:30", "12:15", "14:45"]
    
    print(f"🕐 Analyzing candles at: {', '.join(candle_times)}")
    
    # Analyze each candle
    for candle_time in candle_times:
        print(f"\n📊 Analyzing candle at {candle_time}")
        
        # Find the candle
        target_candle = None
        for idx, row in market_data.iterrows():
            if idx.strftime('%H:%M') == candle_time:
                target_candle = row
                break
        
        if target_candle is not None:
            print(f"   💰 OHLC: O={target_candle['Open']}, H={target_candle['High']}, "
                  f"L={target_candle['Low']}, C={target_candle['Close']}")
            
            # Analyze with different methods
            for method in ['standard', 'extension']:
                try:
                    result = analyzer._analyze_specific_candle(
                        market_data, candle_time, method, include_history=True
                    )
                    
                    if 'indicators' in result:
                        indicators = result['indicators']
                        print(f"   🔧 {method.upper()}: {len(indicators)} indicators")
                        
                        # Show key indicators
                        key_indicators = ['SMA_20', 'EMA_20', 'RSI_14', 'ATR_14']
                        for key in key_indicators:
                            if key in indicators:
                                print(f"      {key}: {indicators[key]:.2f}")
                    
                except Exception as e:
                    print(f"   ❌ Error with {method}: {str(e)}")
        else:
            print(f"   ⚠️ Candle at {candle_time} not found")

def demo_time_period():
    """Demo analyzing a time period"""
    print("\n" + "="*60)
    print("📈 DEMO: Analyzing Time Period")
    print("="*60)
    
    # Create sample data
    market_data = create_realistic_market_data()
    
    # Initialize analyzer
    analyzer = TechnicalIndicatorsAnalyzer()
    
    # Analyze morning session (10:00 to 12:00)
    start_time = "10:00"
    end_time = "12:00"
    
    print(f"🕐 Analyzing period: {start_time} to {end_time}")
    
    # Filter data for the period
    period_data = market_data.between_time(start_time, end_time)
    print(f"📊 Period contains {len(period_data)} candles")
    
    # Analyze with different methods
    methods = ['standard', 'extension', 'study']
    
    for method in methods:
        print(f"\n🔧 Using {method} method:")
        try:
            result = analyzer._analyze_dataframe(period_data, method)
            
            if 'indicators' in result:
                indicators = result['indicators']
                print(f"   ✅ Calculated {len(indicators)} indicators")
                
                # Show sample indicators
                sample_keys = list(indicators.keys())[:5]
                for key in sample_keys:
                    print(f"      {key}: {indicators[key]:.2f}")
            else:
                print(f"   ❌ Error: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")

def demo_method_comparison():
    """Demo comparing different analysis methods"""
    print("\n" + "="*60)
    print("⚖️ DEMO: Method Comparison")
    print("="*60)
    
    # Create sample data
    market_data = create_realistic_market_data()
    
    # Initialize analyzer
    analyzer = TechnicalIndicatorsAnalyzer()
    
    # Use last 50 candles for analysis
    analysis_data = market_data.tail(50)
    print(f"📊 Analyzing last {len(analysis_data)} candles")
    
    # Compare methods
    methods = ['standard', 'extension', 'study', 'strategy_common', 'custom_strategy']
    results = {}
    
    for method in methods:
        print(f"\n🔧 Testing {method} method...")
        try:
            start_time = datetime.now()
            result = analyzer._analyze_dataframe(analysis_data, method)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            if 'indicators' in result:
                indicator_count = len(result['indicators'])
                results[method] = {
                    'indicators': indicator_count,
                    'time': processing_time,
                    'success': True
                }
                print(f"   ✅ {indicator_count} indicators in {processing_time:.3f}s")
            else:
                results[method] = {
                    'indicators': 0,
                    'time': processing_time,
                    'success': False,
                    'error': result.get('error', 'Unknown error')
                }
                print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            results[method] = {
                'indicators': 0,
                'time': 0,
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ Exception: {str(e)}")
    
    # Summary
    print(f"\n📋 COMPARISON SUMMARY:")
    print(f"{'Method':<15} {'Indicators':<12} {'Time (s)':<10} {'Status'}")
    print("-" * 50)
    
    for method, data in results.items():
        status = "✅ Success" if data['success'] else "❌ Failed"
        print(f"{method:<15} {data['indicators']:<12} {data['time']:<10.3f} {status}")

def main():
    """Main demo function"""
    print("🚀 Technical Indicators Analyzer - Demo")
    print("="*60)
    print("This demo shows the capabilities of the Technical Indicators Analyzer")
    print("using simulated market data since we don't have live data access.")
    print("="*60)
    
    try:
        # Demo 1: Specific candles analysis
        demo_specific_candles()
        
        # Demo 2: Time period analysis
        demo_time_period()
        
        # Demo 3: Method comparison
        demo_method_comparison()
        
        print("\n" + "="*60)
        print("✅ Demo completed successfully!")
        print("\n💡 To use with real data, run:")
        print("   python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025")
        print("="*60)
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")

if __name__ == "__main__":
    main()
