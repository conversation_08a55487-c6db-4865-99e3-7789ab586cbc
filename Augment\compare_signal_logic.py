import pandas as pd
from datetime import datetime, timedelta
import logging
import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import necessary components from both systems
from smart_vectorized_backtester import SmartVectorizedBacktester
from enhanced_nadarya_watson_signal import check_vander_enhanced
from shared_sideways_signal_helper import check_sideways
from shared_api_manager import get_api

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_token_id(ticker, exchange='NSE'):
    """Helper to get token ID."""
    api = get_api()
    search_result = api.searchscrip(exchange=exchange, searchtext=ticker + '-EQ')
    if search_result and 'values' in search_result and search_result['values']:
        return search_result['values'][0]['token']
    raise ValueError(f"Could not find token ID for {ticker}")

def run_standalone_logic_minute_by_minute(ticker, tokenid, exchange, date, start_time, end_time):
    """
    Simulates the standalone logic by calling its signal functions for each minute.
    """
    logger.info("Running Standalone (v4) logic minute-by-minute...")
    signals = []
    
    market_start = datetime.strptime(f"{date} {start_time}", '%d-%m-%Y %H:%M')
    market_end = datetime.strptime(f"{date} {end_time}", '%d-%m-%Y %H:%M')
    
    current_minute = market_start
    while current_minute <= market_end:
        minute_str = current_minute.strftime('%H:%M')
        
        # For each minute, we call the signal functions with the time range from market start to *that minute*.
        # This simulates how the logic would be applied in real-time.
        
        # 1. Check Sideways Market
        is_sideways, sideways_text = check_sideways(
            tokenid=tokenid,
            exchange=exchange,
            date_input=date,
            starttime_input=start_time,
            endtime_input=minute_str
        )
        
        if not is_sideways:
            current_minute += timedelta(minutes=1)
            continue
            
        # 2. Check Nadarya Watson Signal (Enhanced version, as used in standalone)
        is_vander, vander_text = check_vander_enhanced(
            tokenid=tokenid,
            exchange=exchange,
            date_input=date,
            starttime_input=start_time,
            endtime_input=minute_str,
            enable_momentum_validation=True,
            enable_realtime_detection=True
        )
        
        if is_vander:
            # The text from check_vander_enhanced determines the signal type
            signal_type = "UNKNOWN"
            if "CALL" in vander_text.upper():
                signal_type = "CALL"
            elif "PUT" in vander_text.upper():
                signal_type = "PUT"

            signals.append({
                'time': minute_str,
                'signal_type': signal_type,
                'reason': f"Sideways: {sideways_text} | Vander: {vander_text}"
            })
            logger.info(f"Standalone Logic Signal at {minute_str}: {signal_type}")

        current_minute += timedelta(minutes=1)
        
    return signals

def run_smart_vectorized_logic(ticker, tokenid, exchange, date, start_time, end_time):
    """
    Runs the smart vectorized backtester to get its signals.
    """
    logger.info("Running Smart Vectorized logic...")
    try:
        backtester = SmartVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start=start_time,
            end=end_time,
            date=date,
            tokenid=tokenid,
            enable_momentum_validation=True,
            enable_realtime_detection=True
        )
        signals = backtester.run_smart_vectorized_backtest()
        # We only need time and signal_type for a fair comparison
        return [{'time': s['time'], 'signal_type': s['signal_type'], 'reason': s['reason']} for s in signals]
    except Exception as e:
        logger.error(f"Error running Smart Vectorized Backtester: {e}")
        return []


def compare_results(standalone_signals, vectorized_signals):
    """
    Compares the two lists of signals and prints a report.
    """
    standalone_df = pd.DataFrame(standalone_signals).set_index('time')
    vectorized_df = pd.DataFrame(vectorized_signals).set_index('time')
    
    # Combine results for easy comparison
    comparison_df = pd.merge(
        standalone_df,
        vectorized_df,
        left_index=True,
        right_index=True,
        how='outer',
        suffixes=('_standalone', '_vectorized')
    )
    
    comparison_df['match'] = comparison_df['signal_type_standalone'] == comparison_df['signal_type_vectorized']
    
    print("\n" + "="*100)
    print("SIGNAL COMPARISON REPORT")
    print("="*100)
    
    print("\n--- Full Signal Log ---")
    print(comparison_df)
    
    mismatches = comparison_df[comparison_df['match'] == False]
    
    print("\n\n--- Mismatched Signals ---")
    if mismatches.empty:
        print("✅ All signals match perfectly!")
    else:
        print(mismatches)
        
    print("\n\n--- Analysis ---")
    total_signals_standalone = len(standalone_df)
    total_signals_vectorized = len(vectorized_df)
    total_matches = comparison_df['match'].sum()
    total_mismatches = len(mismatches)
    
    print(f"Standalone Logic Signals: {total_signals_standalone}")
    print(f"Smart Vectorized Signals: {total_signals_vectorized}")
    print(f"Matching Signals: {total_matches}")
    print(f"Mismatched Signals: {total_mismatches}")
    
    print("\n--- Conclusion ---")
    if total_mismatches == 0 and total_signals_standalone == total_signals_vectorized:
        print("The signal generation logic appears to be IDENTICAL.")
        print("The 'Smart Vectorized' backtester is a faithful and more efficient implementation.")
    else:
        print("The signal generation logic has DIFFERENCES.")
        if total_signals_vectorized > total_signals_standalone:
             print("The 'Smart Vectorized' backtester appears to be MORE SENSITIVE or uses slightly different data windowing.")
        elif total_signals_standalone > total_signals_vectorized:
             print("The 'Standalone' logic appears to be MORE SENSITIVE or uses slightly different data windowing.")
        else:
             print("The logic differs in how signals are classified at specific times.")
        print("Review the mismatched signals above to debug the subtle differences.")
        print("\n--- Potential Reasons for Differences ---")
        print("1. Data Windowing: Exactly which candles are included for calculation at each minute (e.g., 09:15 to 12:30 vs 09:14 to 12:29).")
        print("2. Floating Point Precision: Minor differences in math libraries or data types.")
        print("3. Parameter Mismatches: A hardcoded parameter in one script might be slightly different in the other.")
        print("4. Edge Case Handling: How each script handles the very first few minutes of the day or missing data points.")


def main():
    # --- Configuration ---
    TICKER = "BATAINDIA"
    EXCHANGE = "NSE"
    DATE = "20-06-2025"  # A recent date for which data should be available
    START_TIME = "09:15"
    END_TIME = "10:15" # Let's just check the first hour
    
    print(f"Comparing {TICKER} on {DATE} from {START_TIME} to {END_TIME}")
    
    try:
        tokenid = get_token_id(TICKER, EXCHANGE)
        logger.info(f"Found token for {TICKER}: {tokenid}")
        
        # Run both logics
        standalone_signals = run_standalone_logic_minute_by_minute(TICKER, tokenid, EXCHANGE, DATE, START_TIME, END_TIME)
        vectorized_signals = run_smart_vectorized_logic(TICKER, tokenid, EXCHANGE, DATE, START_TIME, END_TIME)
        
        # Compare and report
        compare_results(standalone_signals, vectorized_signals)
        
    except Exception as e:
        logger.error(f"An error occurred during the comparison: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
