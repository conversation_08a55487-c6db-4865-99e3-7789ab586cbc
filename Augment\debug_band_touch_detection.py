"""
Debug Band Touch Detection

This script debugs the exact band touch detection logic to understand
why signals are not being detected at specific times like 12:49.
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_band_touch(tokenid, exchange, date_input, starttime_input, endtime_input):
    """Debug the exact band touch detection logic"""
    
    from shared_api_manager import get_api
    import math
    
    try:
        logger.info(f'🔍 Debugging band touch for {exchange}:{tokenid} from {starttime_input} to {endtime_input}')
        
        # Get shared API instance
        api = get_api()
        
        # Get timestamps
        from enhanced_nadarya_watson_signal import get_start_end_timestamps
        start_timestamp, end_timestamp = get_start_end_timestamps(date_input, starttime_input, endtime_input)
        
        # Get data
        data = api.get_time_price_series(
            exchange='NSE', 
            token=tokenid, 
            starttime=start_timestamp, 
            endtime=end_timestamp, 
            interval=1
        )
        
        if not data:
            logger.error("❌ No data received")
            return
        
        # Process data
        from enhanced_nadarya_watson_signal import live_data
        data_df = live_data(data)
        data_df = data_df.sort_values(by='time')
        
        close_prices = data_df['Close'].values
        
        print(f"\n📊 Data Summary:")
        print(f"   Total candles: {len(close_prices)}")
        print(f"   Time range: {data_df.index[0]} to {data_df.index[-1]}")
        print(f"   Last 5 closes: {close_prices[-5:] if len(close_prices) >= 5 else close_prices}")
        
        # Ver4 exact Nadarya Watson calculation
        h = 8
        mult = 3
        src = close_prices
        k = 1.75
        y = []
        
        # Calculate Nadarya Watson
        sum_e = 0
        for i in range(len(close_prices)):
            sum = 0
            sumw = 0   
            for j in range(len(close_prices)):
                w = math.exp(-(math.pow(i-j,2)/(h*h*2)))
                sum += src[j]*w
                sumw += w
            y2 = sum/sumw
            sum_e += abs(src[i] - y2)
            y.insert(i,y2)
        
        mae = sum_e/len(close_prices)*k
        
        # Calculate bands and signals
        upper_band = []
        lower_band = []
        upper_band_signal = []
        lower_band_signal = []
        
        for i in range(len(close_prices)):
            # Calculate bands
            upper_band.append(y[i] + mae * k)
            lower_band.append(y[i] - mae * k)
            
            # Check band touches
            if close_prices[i] > upper_band[i]:
                upper_band_signal.append(close_prices[i])
            else:
                upper_band_signal.append(np.nan)
                
            if close_prices[i] < lower_band[i]:
                lower_band_signal.append(close_prices[i])
            else:
                lower_band_signal.append(np.nan)
        
        # Debug last few minutes
        print(f"\n🔍 DETAILED BAND ANALYSIS (Last 5 minutes):")
        print(f"{'Time':<8} | {'Close':<8} | {'Upper':<8} | {'Lower':<8} | {'U-Touch':<8} | {'L-Touch':<8} | {'U-Signal':<8} | {'L-Signal':<8}")
        print("-" * 80)
        
        start_idx = max(0, len(close_prices) - 5)
        for i in range(start_idx, len(close_prices)):
            time_str = data_df.index[i].strftime('%H:%M')
            close_val = close_prices[i]
            upper_val = upper_band[i]
            lower_val = lower_band[i]
            
            upper_touch = "YES" if close_val > upper_val else "NO"
            lower_touch = "YES" if close_val < lower_val else "NO"
            
            upper_sig = "YES" if not np.isnan(upper_band_signal[i]) else "NO"
            lower_sig = "YES" if not np.isnan(lower_band_signal[i]) else "NO"
            
            print(f"{time_str:<8} | {close_val:<8.2f} | {upper_val:<8.2f} | {lower_val:<8.2f} | {upper_touch:<8} | {lower_touch:<8} | {upper_sig:<8} | {lower_sig:<8}")
        
        # Check current minute specifically
        current_minute_idx = len(close_prices) - 1
        current_close = close_prices[current_minute_idx]
        current_upper = upper_band[current_minute_idx]
        current_lower = lower_band[current_minute_idx]
        current_time = data_df.index[current_minute_idx].strftime('%H:%M')
        
        print(f"\n🎯 CURRENT MINUTE ANALYSIS ({current_time}):")
        print(f"   Close Price: {current_close:.2f}")
        print(f"   Upper Band: {current_upper:.2f}")
        print(f"   Lower Band: {current_lower:.2f}")
        print(f"   Upper Touch: {'YES' if current_close > current_upper else 'NO'} (Close > Upper: {current_close:.2f} > {current_upper:.2f})")
        print(f"   Lower Touch: {'YES' if current_close < current_lower else 'NO'} (Close < Lower: {current_close:.2f} < {current_lower:.2f})")
        
        # Check signal arrays
        current_upper_signal = not np.isnan(upper_band_signal[current_minute_idx])
        current_lower_signal = not np.isnan(lower_band_signal[current_minute_idx])
        
        print(f"   Upper Signal Array: {'YES' if current_upper_signal else 'NO'}")
        print(f"   Lower Signal Array: {'YES' if current_lower_signal else 'NO'}")
        
        # Check last 3 minutes for comparison
        minutes_check = -3
        upper_band_present_last_3 = any(not np.isnan(x) for x in upper_band_signal[minutes_check:]) if len(upper_band_signal) >= abs(minutes_check) else False
        lower_band_present_last_3 = any(not np.isnan(x) for x in lower_band_signal[minutes_check:]) if len(lower_band_signal) >= abs(minutes_check) else False
        
        print(f"\n📊 SIGNAL DETECTION RESULTS:")
        print(f"   Current minute upper signal: {current_upper_signal}")
        print(f"   Current minute lower signal: {current_lower_signal}")
        print(f"   Last 3 minutes upper signal: {upper_band_present_last_3}")
        print(f"   Last 3 minutes lower signal: {lower_band_present_last_3}")
        
        # Final determination
        if current_upper_signal or current_lower_signal:
            signal_type = "Upper band" if current_upper_signal else "Lower band"
            print(f"   🎯 RESULT: {signal_type} signal detected in current minute!")
        else:
            print(f"   ❌ RESULT: No band signal detected in current minute")
        
        return {
            'current_upper_signal': current_upper_signal,
            'current_lower_signal': current_lower_signal,
            'last_3_upper_signal': upper_band_present_last_3,
            'last_3_lower_signal': lower_band_present_last_3,
            'current_close': current_close,
            'current_upper_band': current_upper,
            'current_lower_band': current_lower,
            'current_time': current_time
        }
        
    except Exception as e:
        logger.error(f"❌ Error in debug_band_touch: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def test_critical_moments():
    """Test critical moments where band touch should occur"""
    
    from shared_api_manager import get_api
    
    # Get token ID
    api = get_api()
    search_result = api.searchscrip(exchange='NSE', searchtext='BATAINDIA-EQ')
    tokenid = search_result['values'][0]['token']
    
    # Test critical moments
    test_moments = [
        {'end': '12:29', 'description': 'Lower band touch (working)'},
        {'end': '12:49', 'description': 'Upper band touch (should work)'},
        {'end': '12:53', 'description': 'Upper band touch (should work)'},
        {'end': '12:50', 'description': 'After upper band touch'},
    ]
    
    print("\n" + "="*100)
    print("🧪 TESTING CRITICAL BAND TOUCH MOMENTS")
    print("="*100)
    
    for moment in test_moments:
        print(f"\n{'='*50}")
        print(f"🔍 TESTING {moment['end']} - {moment['description']}")
        print(f"{'='*50}")
        
        result = debug_band_touch(
            tokenid=tokenid,
            exchange='NSE',
            date_input='24-06-2025',
            starttime_input='09:15',
            endtime_input=moment['end']
        )
        
        if result:
            if result['current_upper_signal'] or result['current_lower_signal']:
                signal_type = "Upper" if result['current_upper_signal'] else "Lower"
                print(f"   ✅ SUCCESS: {signal_type} band signal detected at {moment['end']}")
            else:
                print(f"   ❌ ISSUE: No band signal detected at {moment['end']}")
                print(f"   📊 Close: {result['current_close']:.2f}, Upper: {result['current_upper_band']:.2f}, Lower: {result['current_lower_band']:.2f}")

def main():
    """Main execution function"""
    logger.info("🚀 Starting band touch debug analysis...")
    
    test_critical_moments()
    
    logger.info("🎉 Band touch debug analysis completed!")

if __name__ == "__main__":
    main()
