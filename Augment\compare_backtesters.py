import subprocess
import json
import os
from datetime import datetime
import pandas as pd

# Import the class from the other script
from smart_vectorized_backtester import SmartVectorizedBacktester

def run_backtester_v4(ticker, date):
    """
    Runs the run_backtester_v4_standalone.py script and returns the generated signals.
    """
    print(f"Running backtester_v4 for {ticker} on {date}...")
    cmd = [
        "python",
        "run_backtester_v4_standalone.py",
        "--ticker", ticker,
        "--date", date,
        "--start", "09:15",
        "--end", "15:30",
        "--test-signals",
        "--signal-only",
        "--output-dir", "backtest_results",
    ]
    try:
        # Run the script and wait for it to complete
        process = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=300)
        print(process.stdout)

        # Find the latest signal test file generated by the script
        output_dir = "backtest_results"
        files = [os.path.join(output_dir, f) for f in os.listdir(output_dir) if f.startswith("signal_test") and date.replace("-", "") in f.replace("_", "")]
        if not files:
            print("Error: No signal test file found for the specified date.")
            return None

        files.sort(key=os.path.getmtime)
        latest_file = files[-1]
        print(f"Found result file: {latest_file}")

        with open(latest_file, 'r') as f:
            return json.load(f)

    except subprocess.CalledProcessError as e:
        print(f"Error running backtester_v4: {e.stderr}")
        return None
    except FileNotFoundError:
        print(f"Error: The 'backtest_results' directory does not exist.")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None

def run_smart_vectorized_backtester(ticker, date, start_time, end_time):
    """
    Runs the smart_vectorized_backtester.py script and returns the generated signals.
    """
    print(f"Running smart_vectorized_backtester for {ticker} on {date}...")
    try:
        # We need to get the token for the ticker first
        from shared_api_manager import get_api
        api = get_api()
        search_result = api.searchscrip(exchange='NSE', searchtext=ticker + '-EQ')
        if not (search_result and 'values' in search_result and search_result['values']):
            print(f"Could not find token for {ticker}")
            return []
        token = search_result['values'][0]['token']

        backtester = SmartVectorizedBacktester(
            ticker=ticker,
            exchange='NSE',
            start=start_time,
            end=end_time,
            date=date,
            tokenid=token,
            enable_momentum_validation=True,
            enable_realtime_detection=True
        )
        signals = backtester.run_smart_vectorized_backtest()
        return signals
    except Exception as e:
        print(f"Error running smart vectorized backtester: {e}")
        return []

def compare_results(v4_results, smart_results):
    """
    Compares the results from both backtesters.
    """
    print("\n" + "="*100)
    print("COMPARISON REPORT")
    print("="*100)

    if not v4_results:
        print("No results from backtester_v4 to compare.")
        return

    if not smart_results:
        print("No results from smart_vectorized_backtester to compare.")
        return

    # Process v4 results
    v4_signals = {}
    if v4_results.get('final_signal', {}).get('signal'):
        # v4 gives one signal for the whole day, let's find out when it happened
        time_str = v4_results['nadarya_watson']['text'].split(' at ')[-1].split(' ')[0]
        v4_signals[time_str] = v4_results['final_signal']['strength']

    # Process smart results
    smart_signals = {s['time']: s['signal_type'] for s in smart_results}

    # Create a DataFrame for comparison
    all_times = sorted(list(set(v4_signals.keys()) | set(smart_signals.keys())))
    comparison_data = []
    for t in all_times:
        comparison_data.append({
            "Time": t,
            "V4_Signal": v4_signals.get(t, "NO SIGNAL"),
            "Smart_Signal": smart_signals.get(t, "NO SIGNAL"),
        })

    df = pd.DataFrame(comparison_data)
    df["Match"] = df["V4_Signal"] == df["Smart_Signal"]

    print("Signal Comparison:")
    print(df)

    print("\n" + "="*100)
    print("ANALYSIS")
    print("="*100)

    num_mismatches = len(df[df['Match'] == False])
    if num_mismatches == 0:
        print("✅ The signals from both backtesters are identical.")
    else:
        print(f"❌ Found {num_mismatches} mismatches in signals.")
        print("Mismatched signals:")
        print(df[df['Match'] == False])

    print("\n" + "="*100)
    print("DETAILS")
    print("="*100)
    print("V4 Backtester (run_backtester_v4_standalone.py):")
    print("- Analyzes the entire day's data at once to give a single signal.")
    print("- Less granular, provides a single outlook for the day.")

    print("\nSmart Vectorized Backtester (smart_vectorized_backtester.py):")
    print("- Analyzes data minute-by-minute, simulating a live trading environment.")
    print("- More granular, can detect multiple trading signals throughout the day.")
    print("- Likely more accurate for intra-day trading strategies.")


if __name__ == "__main__":
    TICKER = "BATAINDIA"
    DATE = "20-06-2025"
    START_TIME = "09:15"
    END_TIME = "15:30"

    # It's better to run them sequentially to avoid any potential conflicts
    v4_results = run_backtester_v4(TICKER, DATE)
    smart_results = run_smart_vectorized_backtester(TICKER, DATE, START_TIME, END_TIME)

    compare_results(v4_results, smart_results)
