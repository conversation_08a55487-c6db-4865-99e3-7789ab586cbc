{"cells": [{"cell_type": "markdown", "id": "adac5b2b", "metadata": {"execution": {"iopub.execute_input": "2023-01-16T15:35:53.922482Z", "iopub.status.busy": "2023-01-16T15:35:53.922005Z", "iopub.status.idle": "2023-01-16T15:35:53.933506Z", "shell.execute_reply": "2023-01-16T15:35:53.932116Z", "shell.execute_reply.started": "2023-01-16T15:35:53.922446Z"}, "papermill": {"duration": 0.008789, "end_time": "2023-01-23T21:40:33.464528", "exception": false, "start_time": "2023-01-23T21:40:33.455739", "status": "completed"}, "tags": []}, "source": ["<h1 style=\"background-color:red;font-family:newtimeroman;font-size:250%;text-align:center;border-radius: 10px 10px;\">Machine Learning for Stocks Trading</h1>"]}, {"attachments": {"4446566d-5335-4234-af2c-7da1aac6359b.jpg": {"image/jpeg": "/9j/4AAQSkZJRgABAQEAYABgAAD/4SLKRXhpZgAATU0AKgAAAAgABgALAAIAAAAmAAAIYgESAAMAAAABAAEAAAExAAIAAAAmAAAIiAEyAAIAAAAUAAAIrodpAAQAAAABAAAIwuocAAcAAAgMAAAAVgAAEUYc6gAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFdpbmRvd3MgUGhvdG8gRWRpdG9yIDEwLjAuMTAwMTEuMTYzODQAV2luZG93cyBQaG90byBFZGl0b3IgMTAuMC4xMDAxMS4xNjM4NAAyMDIzOjAxOjI0IDAwOjM2OjM3AAAGkAMAAgAAABQAABEckAQAAgAAABQAABEwkpEAAgAAAAM3OQAAkpIAAgAAAAM3OQAAoAEAAwAAAAEAAQAA6hwABwAACAwAAAkQAAAAABzqAAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMjAyMzowMToyNCAwMDozNTo1NgAyMDIzOjAxOjI0IDAwOjM1OjU2AAAAAAYBAwADAAAAAQAGAAABGgAFAAAAAQAAEZQBGwAFAAAAAQAAEZwBKAADAAAAAQACAAACAQAEAAAAAQAAEaQCAgAEAAAAAQAAER4AAAAAAAAAYAAAAAEAAABgAAAAAf/Y/9sAQwAIBgYHBgUIBwcHCQkICgwUDQwLCwwZEhMPFB0aHx4dGhwcICQuJyAiLCMcHCg3KSwwMTQ0NB8nOT04MjwuMzQy/9sAQwEJCQkMCwwYDQ0YMiEcITIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIy/8AAEQgAZgEAAwEhAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A9/ooAKKACigAooAKKACigAooAKKACigAooAKKACigAooAKKACigAooAKKACigAooAKKACigAooAKKACigAooAKKACkyM470ALRQAUUAFFABQTgZNABRQAUZoAKKACigAo7UAYya87XBh/sfUwQM7jCApOSMZzjtn8anXWA9+1othfFlYKZDDtTB/i3EjI69PQ+2QDSooAKKACigAooAKKAA57VGd3nLwPunv9KAH/N6D86Pm9B+dAB83oPzo+b0H50AHzeg/Oj5vQfnQAnzeg/OkfdsbgdPWgB3zeg/OloAKQdT9aAFpu8b9vfGelADqKAImuIVdk8wF1GSi8tj6Dmq02qQwruKuRjJOMbeMgEHkZx3FAGRLq121wZ7yBIdNQeWAl0Ekabr1YqCoAxgN1JBHBxr2NzMI47e/2LeBeSpysuByVOB+Ixx9MEgF6igAooAKKACigAooAKjJHnL/ALp/pQA/I9aMj1oAZNKIYJJdrPsUttQZJwOg96bHcpNZpcxfOjxiRdrA7gRkYOcfrigCvpuqQapHM8CsqxTNCdxXkqcHoT+uD7dKjvtat7C/gs5I5XkmBKlNuBj1544B+uDjJ4oA0cimuR5bc9qAHZFLQAUg6n60ALVW43eRdFZBGQv32OAvHXPb60AY15cX1pMZbaeNbJiEYSOWZHwMEs33Q3TGOuD/ABHA92LmVlQzPG7bZC7blGDjO3Y2B1wTgHHWgB32i42gLJMy7gsckhjSJ+MjaEyzcduhwelVZ5pJt0EBWOULlDKjJ9nUAHd0x/d4wOcdcZIAadapFqBuLfU2lvDCFS2kYyiKDd97ZuG1iepzzgDBIrSFukWnW0cZa6sAi+XIGG+MAfKwPcdOe2fToAWra9OSkrAgDO/pj6jt/nPYm/QAUUAFFABRQAUUAVNUuI7XTLi4lEhSJN5EZIbjnjFUINWkMdo5t5pzPEXjKBVJBAIB3MBux6dcE8dAAT/2q4z5mn3UIABJbY5xnHCozMfyqlY+KtOv9TWzsZRclriaCRhkeTJGoLKwIznmgDfZVdCrKCrDBBHBFJHzGp9QKAK2nSvPbO8jbmE8yA47LIyj9AKlmdklt1U4DyENx1G1j/MCgCamv/q2+lADqKACkHf60ALVW48swXXmlgm3nb16dvegDmNZSykn3XLznUhGB9ncrveE+gTgc7ufXI9MUYZkkVYblYpI0T9067OeP4jgsD1HTHHHPUAueTFBukeO0MhyCqJtlJxnBfOMDJPHYDrUMUjxRieUSNtf5YIgEaRv9ogAAAYyMcf3j0ABasTp8motb2tmTqzW4kmmYtD+6JKhRIASwznjn+8cHGda0Nk9rDJp0i2m9AVg4Qc4PKdAffH50AK8cgnB8shxziPhl9wDwRzyMnr0B6raX4jXbwY1+8Fz8v4Hle/ynp09qANZXV1DKQQe4paAMZ7XWY3jzqsZjzhv9HGTkgDHPXrk/pTzY6wXyNYUL6fZV9vf/OaANaigAooAq6jbC80+4tmLASoUJUcjNcsyXFrL5VvcNqVuAqLA0WEgVYyjfOqncScEgnjHFAHH/DPT7jxD4LsbvXr9buG5tnjL3W2R/luWIU78hvudT04HYV2l14Pt4dSW+snW1jZpp7uSL5WZ2X74UDBYnqep75oAvLBfWV5Gk8klzEVPlTRuUYNnlWTOw8cg+xGM9b1n9oe3H2fUFmYYLCeEZXgfLhduPxGeaAI9Nnkt7WX7QoSP7ROfNU5UDzW69x1Ptx1q9PkzWpDD/Wnt/sNQBY+b1H5UjBipGRyPSgDl08YRPqEWmRBJ9Q8i3nlhRWyiSHBbp0Hy9/4q6qgBGzsOOuK51devN00bi0EiMVXiUA4HJ+7+H4UAaGj311fJK1wsACkBfK39e4O5R7c/XpU000Zt7xjGZFQEMjqQG456jp79KAOf1W6i81LaK1MlyfmS6jzKgzj5ST1bGPl9MGsp4/K3TsPs8uTvDkAMT1BBKtz6Z/POCATRzSXKeWFuY4/4Qm1lxwfr74GSxx1Cmr1uN1zvhTyJQo3Stj5V7DcWPX0wPx60AWIJ53nNnc2EX2JV8z7cR5gllyfl2nnOBndkg5wOa1UmkntomurCRHKBjg52kjkDHIoAiMSN8iIjbuSu/Y498rg8fTPvVG4f5y6uzSA4A4LZ6fw8njuDnGMg0AVDql9ZIz2Fobq4PWz3hN5/2c/d6delb8F5OySN5bT7IwwVIzGS3PyjccZ+pGO9AGdfzLLq0cE7KIRIGYPKF8vChlOM9d3fnpUdvebxDm4DxtErGX7eAS23P3frxQA6W82/aQJtipEzRyi+D7mA4G3/AD0ro6ACigBksccsZSVA6HqpGQfwqNtm9U2kLsIwFPtQBQ8P6Np/hzQ7bSdOgeG0tw2yMlnxuYseTknkmp9Qit2sLkmFSfKbkx+x9qAMDUhdWWpsXy9leXdvDFGXcBBg7ioBGGyAR+daUBSVo0a8kMgUrDcxqv7wDrzt68cjocZHQgAEGmavbv8AaoLO+ju4bS5eC6k8o7ops5YPjA6nkjAGemMkaTQSxsj2yR/I27YSyoc5ycYODyenrzQA5dUXzPKkt5o5s8IwHze455/Ck1MG90i8tUMkUk9u8YYxt8hKkZ49PagDnfCmjweHPDuk2E8cc2rJEsMlwISrsA27liM7Rx+nrXZUARXEIuIGiLugbvGxU/mKorolsImjM14QcDm7kJGCCCOeuQP19aAJbbSorWVJEnu2KAgCS4dwc+oJ561aT/WyfUfyoAi8uG8tWR4h5ZZlK/QkZ/TNctqrujG3RvNRcqpPJfHb8Ofy7DJABmQrHEokaNWXJYuwxjOOSedxPbH3G653VtwLb3yKhjnmkAyibyAf9rkng+vPucEUAW4tK1GO6MslyJ7Ty9qWLnCxtn7+8DJ7DGMDqK0ILK5S3jjmvpJWVAGJAGT68YP60APktUC7pfIbHVpELfqTVC6MgQpE0ca8D7hH/jpbH5/y5oAyZbC+jLyaRcJJqmwlHkQ+UDjhWOfu8DoM+ntv26XzI6l5Y3MeC8oVgH9VA7deD7e9ADXhnW/Mnkyuu8MSm3DjZtxyR35/CqlpBfW0cbTGfy1jCmNo4cL077u3SgB11BqE3mBFl8poinlGKIZJUjO7dkc4NbtABRQAUw/65f8AdP8ASgB9V7//AJB1z/1yb+RoAZe2EN+Lfzg37iZZ02nHzL0/nWbrWnSw6LKmkoUkjCmGGJVADbwSRx1xnvigDmPCvh/XfDWpa9cSy24srzVZ7xg75zGygqSccHrn6V11rf2/lrJbyZhIB8liAVHqvqPoT7dMUAWzc2N3AQ0sMsbdQSCPxrhrvxFcah4ptdM8MtNLb2l7JBqzcnyQIvl27+MZ3dMjge1AHS27Xf2n7S12rI7eVD50YZ2UHkgJgcnuOMAE1v0AFIOp+tAC1XkmW3S4mYEqg3ED6UAY2ra3LA32BYjFcum9pN3yInqGOBu68deCfTOTaolz80k8aqRhUDAnHbHvn2PTjpQBbl0yMLkOu7OVctls+q9djfT72exxiO1eSBsQEeYGBPOcE9CfY+vTr1zuIBurqEF5bhZGMMySKZIipLKVIbp1xx1qzJfRKQkR82QjIVOePU47UAUpJ5XYFiAc4U43HPoi9M+5Jx37ikgsneTLZ3Z5Ytu2fj6+uPxPagDUhgjgTai4Hf3qSgDIGuSDCtpV/uOM7YsgevPtTH1VL6Dym0y+Mc21QXgBHJAyeegzn8KAHrrpKBjpOpjjkeSOOnv70p10+Q8o0rUjt/h8jk844GaANC2n+0wCXypIskjZIuGGDjp+tTUANcqqkuwVR1JOKrma285f36/dPST6e9AD/Nj7ea2fQMar3xlbT7nbEw/dN9+T2PpmgCfy5iP9Yg9sMf60v2dj96eX8DigCvqGnrPpt1FGm+WSF1TexPJBxyfeuP0jXll1ibwr/Z032nTba1dp4nGQZBkkc9ACPrz1FAHTWj218zslwI7tDtaWF+JADgNjkEcd845+teeeDtJ1MeNPF7b54be5vnK3SgDzFwMlTzk8jp0z+FAHqVtYQWoGwMWChdzMScDt7D2GBVqgApB1P1oAWqk8jRpclYmdgm4LtJDcdOn6daAMO6hvbnbZwQRPYf6yaa2QJvbsgUk9MZJ+g9asW1usGBDD9nYjgO3zN691I7cdPagCw/2gQst07oD1JjVkA/PP51jajZQ2+J5oJZoQeXRCDg9c8Afrz355IAunLCdYKxWsqagLUbL4oVWa33cLuZSCwJzgZ6g55IrWtWjOmQm2j+y2Hlgrn77ggf5yTz9OoBZtrYuS5UxrjA5+Yj09h/kYHW+qqihVAAAwAB0oAWigAooAKKACigBD04qM7vOXgfdPf6UAPy390fnUN4skllOioCzRsAM9TigCb5sdB+dLlvQfnQAnzeg/OqEGjWFtrF1q0NnGl/dRrHNMCcuq/dB7cUAc74w8Palrtjc6LprxWME9tujuAMeXL5gJIxyMgY49a6KG2mgjs4n/AHjRW5jeTJ5OFGfxwaANDLeg/OloAKQdT9aAFzSUAHFBwRgjNAEX2aEY2KUx0CEqPyFQNYnBCTEBj84ZAQwPUYGBz64NAGQllew3LWU9tayaYVMkUjIZSknIKbMcA5yDk9x3GNW1hmuUiub6MJLgMsCtlYz7+p/l29SAXqXNABmjNABmjNABmjNABRQAVGT++X/dP9KAJMj1qpqVtLe2EttDdvavIAPOj+8ozzj3xkZ96ALS5CgMQWxyRxS5HrQAZFGRQAnG7PcDFU2tJBq/277ZIIRB5Rts/JnOd317UAXciigAooAKKACigAooAKKACigAooAKKACigAooACARgjNRmJfMB2rgAjp9KAHeWn9xfyo2J/cX8qADYn9xfyo8tP7i/lQAbE/uL+VGxP7i/lQAbE/uL+VI0alSAq5I9KAF2J/dX8qdQAUUrgFFFwCii4BRRcAoouAUUXAKKLgFFFwCii4BRRcAoouAUUXAKKLgFFFwCii4BRRcD//Z/+Ex6Gh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8APD94cGFja2V0IGJlZ2luPSfvu78nIGlkPSdXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQnPz4NCjx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iPjxyZGY6UkRGIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyI+PHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9InV1aWQ6ZmFmNWJkZDUtYmEzZC0xMWRhLWFkMzEtZDMzZDc1MTgyZjFiIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iPjx4bXA6Q3JlYXRvclRvb2w+V2luZG93cyBQaG90byBFZGl0b3IgMTAuMC4xMDAxMS4xNjM4NDwveG1wOkNyZWF0b3JUb29sPjx4bXA6Q3JlYXRlRGF0ZT4yMDIzLTAxLTI0VDAwOjM1OjU2Ljc5MDwveG1wOkNyZWF0ZURhdGU+PC9yZGY6RGVzY3JpcHRpb24+PC9yZGY6UkRGPjwveDp4bXBtZXRhPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD94cGFja2V0IGVuZD0ndyc/Pv/bAEMAAwICAwICAwMDAwQDAwQFCAUFBAQFCgcHBggMCgwMCwoLCw0OEhANDhEOCwsQFhARExQVFRUMDxcYFhQYEhQVFP/bAEMBAwQEBQQFCQUFCRQNCw0UFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFP/AABEIAZgD/AMBIgACEQEDEQH/xAAfAAABBQEBAQEBAQAAAAAAAAAAAQIDBAUGBwgJCgv/xAC1EAACAQMDAgQDBQUEBAAAAX0BAgMABBEFEiExQQYTUWEHInEUMoGRoQgjQrHBFVLR8CQzYnKCCQoWFxgZGiUmJygpKjQ1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4eLj5OXm5+jp6vHy8/T19vf4+fr/xAAfAQADAQEBAQEBAQEBAAAAAAAAAQIDBAUGBwgJCgv/xAC1EQACAQIEBAMEBwUEBAABAncAAQIDEQQFITEGEkFRB2FxEyIygQgUQpGhscEJIzNS8BVictEKFiQ04SXxFxgZGiYnKCkqNTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqCg4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2dri4+Tl5ufo6ery8/T19vf4+fr/2gAMAwEAAhEDEQA/AP1TooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooqhrVw9rpV1LGdsiRkhvSgC/RUULFo1Y9SBUtABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRSN0rM0K6e709JJW3Pvdd30Y0AalFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUm6jdQAtFJuo3UALRSbqN1AC0Um6loAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKQmmeZ9fyoAkrM8Sf8gO+/wCuLVf8z6/lWd4ibdod7/1yagDQt/8AUp/uipar27f6PF9FqTzPY0ASUVH5lHmexoAkoqPzPY0eZ7GgCSio/M9jR5lAElFR+ZR5nsaAJKKj8z2NHmH0NAElFR+Z7GjzPY0ASUVH5h9KPM9jQBJRUfmexo8z2NAElFR+Z7GjzPY0ASUVH5nsaPMPoaAJKKj8yjzPY0ASUVH5nsaPM9jQBJRUfmUeZQBJRUfmUeZQBJRUfmexo8ygBz/drJ8L/wDIIT/ff/0Nq1N25sVk+F5B/ZKf78n/AKGaANqim7hRuFADqKbupRzQAtFFFABRRRQAUUUUAFFFFADX+7UdqxkhRz1IqR/u1DY/8e0f0oAsUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVxnxe8R3vhH4W+Ldc010i1DT9Lubq3d13KrpGzLkd+RXZ1j+J/Dtj4u8P6lompRmfTtRt3tbiNWKsyOuGGR04NAHzc37Vl7NdaROul61Z2EfhS41e+j1DSnsnnmR7ZVaFpFww/ev93I+Zaf4s/ar1bT/ABFpl/b6DJa+DLe61aC5u2ljeW+aygYlFTrH+8VsH+Lb2r2fxF8F/C/iq3tIdRs5ZUtdMfR4VWdxttnaIsv1/cpz7VzVx+yz8P77xFd6rcWN5N9pN0WsZL6U2iNcpsuHSLOFZ16kd6APNb79q3X/AAXr3iOLxR4ejtZ/tFlb6XpP29NieZbSTSu9wE6YT+71/OupX9qiW4kt7m08GXsmiJaabe6jfT3aRPaJeSNEiiFl3OVdeenBzW1b/sq+CbfTbu3EuuPd3NxDdNqsmrzNeo8cZiTZNncBsZkx3BrqH+CfhaazvreW0uJY762sra4Z7h2d0tX3w5YnO4Hqf4u9AHIeJvjbd+B/EPxHfUGs59O0JNNFjb3M4tg7zo+5TLtbOSBgYJrP0f8Aay0vV/DFnrA0W4RbywkurSMThvtE8d59ke3Q4672Rge4auy8afs++E/HWuX2sagNQgv7xLdZJrO+khxLA5aGZdp4kTcwDejYrnbz9mvTF1f4c21kyr4d8Kajc6wy3kss93cXUm8jc56pvcuc91WgDij+3NpPneIjB4Yvby30uC6mge2nDNcNBKsbo/y4h3M3yZY7sdq+hfBuravrmhxXWuaKuhX7s2bNbpbkBP4TvVQOR2rhG/Zn8ESy+Id8GoNZ62jpcaf/AGjN9mj3yCV/Ki3bULOuTivWI4xGioo+VRgUAS0UUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUANbrSr0pG6ilXpQAtFFFABRRRQAUUUUAFFFFAEMyNJGyo5RiOGxnFZ/wDZd7/0EpP+/KVrUUAZP9l3v/QSk/78pVHXNPu49FvCdRkbEZ/5ZpXSVmeJP+QHff8AXFqAI0sLx448ajIvyf8APJKX+y73/oJSf9+UrRt/9Sn+6KloAyv7Lvf+glJ/35Sk/su9/wCglJ/35StaigDK/su9/wCglJ/35Sk/su9/6CUn/flK1qKAMn+y73/oJSf9+Uo/su9/6CUn/flK1qKAMn+y73/oJSf9+Uo/su+/6CUn/flK1qKAMptMvm/5icn/AH5Sk/su9/6CUn/flK1qKAMn+y77/oJSf9+Uo/su9/6CUn/flK1qKAMn+y73/oJSf9+Uo/su9/6CUn/flK1qKAMn+y73/oJSf9+Upf7Mve2pSf8AflK1aKAMn+y73/oJSf8AflKP7Lvf+glJ/wB+UrWooAyf7Lvv+glJ/wB+Uo/su+/6CUn/AH5StaigDK/sy+3Z/tOT/vylN/su8/6CUn/fpK16KAMn+y73/oJSf9+Uo/su9/6CUn/flK1qKAMn+y73/oJSf9+Uo/su9/6CUn/flK1qKAMn+y73/oJSf9+Uo/su9/6CUn/flK1qKAMn+y73/oJSf9+UpDpd5/0E5P8Avyla9FAGV/Z958v/ABMZOP8ApmtZnh+wu5dLQpqMirvf5fKT+8a6Z/u1k+F/+QQn/XR//Q2oAf8A2Zef9BJ/+/KUn9l3v/QSk/78pWtRQBk/2Xe/9BKT/vylX7eNo40R3Lso5bGM1PRQAUUUUAFFFFABRRRQAUUUUANf7tQ2P/HtH9Kmf7tQ2P8Ax7R/SgCxRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRSbqMigBaRulVrrULexj33FxHAn96Vwor5r/AGgP2/Pht+z74msND12a6upNQspLiC+09FuIUdDgI+1sg7vagD1Pxd8btL8OeIpfDelaZqfi7xNCgln0vRYg7WyN91pnYhI93bc2TWdZfH6Cy1C0svGHhbW/ArXkwgtrvVVjktJJD0TzoWdEY9g+3NH7MWjxWXwb8O6s84v9U8QW6azqOofea5nnXezFvQZ2j0VQK9F8TeHdP8V6DfaPqltHd6deRNDPBKPlZTQBqq25aXbXiP7K/wARIPFnwwstJudY/tTW9DebT7mSRv38yQzPHFMc/eDoinf0PNe3L0oAWiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAa3UUq9KRuopV6UALRRRQAUUUUAFFFFABRRRQAUUUUAFZfib/kA3//AFxNalZfiX/kB33/AFxagC9b/wCpT/dFTVDb/wCpT/dFTUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUANf7tZPhb/AJBKf9dJP/Q2rWf7tZPhf/kEJ/vv/wChtQBsUUUUAFFFFABRRRQAUUUUAFFFFABRRRQA1/u1DY/8e0f0qZ/u1DY/8e0f0oAsUUUUAFFFFABRRRQAUUjdK8+1i31PxL48udJi1u70jTrOwhuPLsVRZJHd3HLsp4ATt/eoA9B3UtYvh3w+2g2ssP8AaN/qO99+/UJvNZf9kcDitqgAopN1LQAUUUUAFFFJuoAWik3VXubyK0iaSaVIUXq0hwKALNFcle/FLwxasyDVobqVf+WVkGuH/JAazrX4gaxrTEaR4O1Rox0udVKWcTe4DEv/AOO0Ad9Td1cJc2Pj3WG2/wBo6T4eg7tbQPdzf99PtT/x2nL8L4rxca3r2s643dZbr7PEf+AQ7KANzXPG2g+GVDarq9np49LidUb8qwofi1ZaozDRdI1nW17T29kyQn/to+1arzL4A+Hcnlpaafa3v3hBBB51y/0UKzmnR654w8UWudJ0dPDds/CXOtLvmA9Vt0bj/gTD6UAR6lr/AI6u42lttL0fw5Zp8z3Ws3jSsF/3I1AH/Anrk5/EWoeIo5lsPEmra+sY/eT6LDFY2KHupuX3f+Osat6pY6NaalFaXh1D4jeKFcYtZHVoYG/vui4hhUf7QLemap3un6n4p1Q6ZM9vrF7F/wAuFsrLpGlgdPN7zSf7B/75WgDzx9Jk1qSKK1iXVL++kKxyo8twrqOpSaRmeTb3dVRB/e/hrhLz9kvwj+1B4iuNP1lWl0nwxI0U2s2Q2mS/43W0JPBiiH327vx/DXuPjOKXRfK8E+E7hrrxlrYWK/1iRRvtof4m44Tau7ai8D6sN3rngfwdp3w/8L6doOlxLBZ2cexf7znqzt6sxyx+tAHl2j6b4q/Z/kk0jRvDs3iz4fKxk06HTJFF9pmT81v5TsFkhH8G1tw6YPFT61418ffEq0bRfDHhPUvB0V2fKudf8QbIntYz95oYQzF5MdN2AvXnpWz4g+MV9d+Krrwz4J8NS+K9SseL+8e4Frp9k/8AzyeYqd0n+wgYj+LFZ938ZPEvgO4tpPH3g0aToc0gifXdHvPtltak9DcKyo8adt+0gd8UALr37P2mWXhnw9D4Pmbw54h8MWy2+k6pH8zlAOYZv+ekbn7ynv8AMOa0vhz8WG1ub+wvEdsNJ8VW7mGW33fu5nXn5P8AgPzbfTkZr0uGZLiNJI2DxuAVZTkEVw3xK+GkPjKBby2EcWr24GxpOEmQHd5b45Hs4+ZDyKAPQKK8c8F/Eq70OEWviETvZRTG3lvrkBZrB+0V2o42+ky/KRjOK9dhmSaMSRsHRhkMpyDQBNRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQA1uopV6UjdRSr0oAWiiigAooooAKKKKACiiigAoqGaYQRtI+dqjJ2jNUP+Eksv+ekv/fh//iaANWszxJ/yA77/AK4tTP8AhJLDbnzJf+/D/wDxNUNe16zuNFvUSSTcYW/5Yv8A/E0Ab9v/AKlP90VNWPD4gso4Yxvk+6Puwv8A/E0//hJLL+9L/wB+H/8AiaANWisr/hJLL+9L/wB+H/8AiaP+Eksv70v/AH4f/wCJoA1aKyP+Eksv78v/AH4f/wCJpW8SWX9+X/vy/wD8TQBrUVkN4jsl2/PL8x42wv8A/E07/hJLL+9L/wB+H/8AiaANWisr/hJLL+9L/wB+H/8Aiab/AMJJZf35f+/D/wDxNAGvRWR/wkll/fl/78P/APE07/hJLL+9L/34f/4mgDUbpXi3/DSAuPHV14f0/wCH3jHU7e21JtMk1q1sUaxEiPsdt+/dsU9Wx2r2C0vI72HzIizJnHzIV/nXxd4g8XaZ4d8Lap4k8D+MfEdr41/4Tx7SLw1qd0F+1yyX4W4tvsY+Xy2R3cPt3YVTuoA+2l6UtNWql9qUFgqmZnUN02oW/lQBcbpXkni/4ua94Y+N/gTwYdAtG0TxKbxV1Y3paZWgt/NI8nZxzxnca9Qs7+K+jZomZlU4O5Cv86+e/jdqd3F+0Z8IL238Pa/qOn6DNqL6jfWOlTTQwie18uL5wuD83Xb93vQBf8UfHLxjeeIvH8XgbQdN1PS/AOxNUXUJnSfUbjyFuHgtivyoVjdPnfOS2NvevYfA/i2w8feDtF8SaazHTtWs4r2Dd12SIrDPvzXz00fiT4O+Ivjda23hbVfET+LLr+2fD0mm27SxzzSWiQPbyv8AdhKSRbtz4GxuvFe1/BPwPP8ADX4SeD/C13Ikt3pOl29pPJH91pEQb9vtuzQB3VFQXNwtvEZHzsXrtGao/wDCSWX96X/vw/8A8TQBqN0rxXx78YPE9n4r8V6L4W0TTZ/+EW0qHVb+51u4mt0uVkErLFCyIRkLEdzk8Fh8teu2WqQ6hvERZtvXdGyfzr5l/aM8UXHijxvN4P1zRPGMvgCxtUm1C28OaNNM2vyurMLZrhPuQIPvqpBctjICnIB0Oj/tGeIviZcWNt8PvDNrdzr4ds/EOotrVy8KQrdIXhtU2K2ZWVS24/KBjrur1H4Q/Eqx+MHw50Txbp0Mtrb6lDve1n/1sEqsUlib3R1Zf+A14h4N1jU/hL8Qte8T6z4P1Kw0jxb4c0qe0s9KspLv7Fd2sLxtYOsSko2xosMV2/eGflr0j9lnwPrHw7+B/h/SvEEAs9aka51C8tVKt9nkuLiS4MWRx8nm7f8AgNAHrtFRSSCFGdvuqMms3/hJLL+/L/34f/4mgDXorI/4SSy/vy/9+H/+Jpf+Elsf+ekv/fh//iaANaisj/hJLL+/L/34f/4mj/hJLL+/L/34f/4mgDVf7tZPhf8A5BCf77/+htS/8JFZsyhHky396B//AIms3w7r1nb6WiO8md7t8sTn+M/7NAHU0Vlf8JJZf3pf+/D/APxNN/4Saw/56S/9+H/+JoA16KyP+EmsP+ekv/fh/wD4mtC3mFxGsiZ2MMjcMUAT0UUUAFFFFABRRRQAUUUUANf7tQ2P/HtH9Kmf7tQ2P/HtH9KALFFFFABRRRQAUUUUAI3SuN07/kq2tf8AYKtf/Rs1dk3SuN07/kq2tf8AYKtf/Rs1AHZ1598TPiZ/wg6WGn2Gnya/4o1cvHpejwNsaYou53d/+WcaZXc56bh1JAr0BuleN3FxBpf7Uls+pYQ6l4YNvpMrt8pkjuS9xGv+2VaFseie1ACxaX8bri3+2ya74OsbojP9kLp9xNEP9j7R5qn/AIFs/Ctv4bfEy/8AEmpXvhzxNozeG/GFhGs89ms3nW88JbAnt5eN8e75eQrKeCK9Gr5Y/ay8VeLvDfjfw3rXw3tLC/8AFHh7TL6+1SO+VzEmmv5SMGVOSd+11H/TJ6APqjdWXr3ifSfDNr9o1bU7XTYP+el1MsY/WvkTwt8RvG/ia2in8W+I7lJbiNZUj08RRWTofuvE6ffRv72+u803Q49i3Uj6vu+99s0zSra9b/e3jzWoA9hT4qaTfJv0u31HW17Np9m7of8AgZwn/j1VF8R+NtXmYaf4YtdMt+0+r33z/wDfqNW/9CFcTa6pol0RbXfxQ8SWEvTy7yCKyYf992611Fj4R0LVI1C+OdW1L/aTWV/9p7aANRvCnijUmzqHi57aNvvQ6VZJF/4++9v5UR/CPw3JMJdRguNcl+9u1e6e5XP+452/pT0+FeizJ895q1x/tSarcN/7PXPeKPD/AMPfC8a/2q8rzsdsduLu4nuJG/uiJWJb8qAPRrPT9P0mIR2tvb2iL91IkVAPypt5renafA8tzfW9vCi7neWZVAX1Oa8mXwH/AMJJt/szwXZ6NaHpfa8Wkm/4Dbhs/wDfbD6Vq6V+zj4NjmiutZ0yHxDfo28SX0CeVGf9iIDYv6n3oAg1D9pbwXJHImgavYa/cKSgeG/hit1b3mdtv5ZrJb4haZ4kZRrnxI0nTYiPn0zw9dLu2+huPvn/AICqV6j/AMIj4Z0m1V/7H0u1t4V4P2WJEQf988Vxd540i1bfF4WtNOsrJCUfxBqKLFap/wBck4M314T/AGqAKNr8TPhR8N7dBaXltbz3L7UaKCWe5vJcdAdpeV/zqrN8Rrzx0ZDMdW8K6B18uKwm+3Tp6u+3EA+mT/tCkj8feAvh759/Pql54p1pFxLe21vJeSszfwJ5a7Ez0CJTppvHPxInW4uvCT6boYOYNM1S/SHzx/C9wse8/wDbPj/a9gClpHiy11iwTTfB2i65o3hnG+XVYNKl86+z97yWfk57zNz6etausfE6w+GvhX7JpHhLWbaXDrbwyWoRWKjLzOWfJA+8zH5j9TVzxZda54Z8P3GpeJfGWn+F9Jt49xj0q0CuAP4EeQnJ/hACc14hZ6Onj7WLjWNQtJtU02z2te6n4mvnhto/lVorZU2r5gHyvJtUAvtH8NAHWfDv4gWnhe7vtSv9Mv7zxPqHzGGeWAXCR53cxByUL/eb04X+CvE/2pv+CkXiT4F+NPDkOleC0v8ARdQtJhcxXM22YXAfamx03Dj+6RzXu8fhqXUNJu9av7waD4ZSMyyXjWq2iun/AExtuvPZ5mJ9Eqj4G+BOgfGm3bXPGHhqP/hFo3VvDuj3efNADZ+2zHr5z4+X0T/eoA9H/ZfsbK1+A/g24s5mvG1CwTULq7f789zN+8md/cuzV6Tqlja6pp91Z3sUc9ncRtFLDKMo6EYKn2ryT/hD/Gvwn1a/l8C2th4h8K307XJ8N3k/2SWzmdsu1vNtKbGb5vLZRgscN2qPV7f4pfFK2fRrvSrX4eaFcjy766GoLe6hJH/GkPljZGT03sTjsKAOY/Zi+Mmlaf4H8M+EtcmutOuC9xaaFfaiNsOq2sczpC0UnRn2AfIfm719KfeWuL1j4SeFtc8C2vhC50qH+wrOJIrWBBg22wbUZG6hh6151Zal4r+C+pQaVqM76/oMz+VZ3N1JtY/3U808JJ/sPw/8LL0oA9B8Z+DZdQm/tnR0ih12GJoilx/x730f/PCYDqvo2CV/MVyfhXWLrw7avc6FbXN7odvN5Oo+HHwbvR3xuPlf30+bOz+7yn92vRPC/jTTPF1vI9hMwniOye1nQxTQv/ddDytZXizwpdtfLr/h+VLbxBbrtkR1xFfxD/ljL/7K/wDCfbIoA6Dw/wCIdP8AEmlwX+mXUd3ZzDKSRNkcHDD2IPBHatevJdNhfVjP4j8F5sNVSRk1XQLz93FNIPvB1/5Zy+kg4Yf3gQa7Xwl4ysfE9vMsYktL+2fyrvT7gbZrd/Rh6ejDhu1AHS0UUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQA1uopV6UjdRSr0oAWiiigAooooAKKKKACiiigDJ8QT6jbaJfTaPbQ3uqpA7WttcSmKKSTHyqzhW2gnvtNcb8O/jBaeNNSn0TUdI1Dwr4nto1mk0fWEQSvGf+WsTIzI6ZB6Nkcbgu4V6O3SuI+I3wv0z4kWVstzNd6VqljJ52n6zpsqxXdnJ3KPtIw3dWBU9xwKAO1rP8RNt0O9I/wCebV5BY/FW7+C95F4d+Jt676c0gi0nxayMUvgW4S52LiKZR1b7jBS3ydK9Y1q5jvPDd1LAyzRSQbkdDkMCOCDQBqQL+5T/AHVqXbUVv/qU/wB0VNQAm2jbS0UANajbVe/mNtY3EqY3pGzDd7CvPf2cfiBqfxS+CPhHxZrKwpqeq2f2idbddibtzDgfhQBi/GTxVq+gfFX4Nabpt/LaWGsa3dW9/BHjbPGljM6qfo6q3/Aa9kWvCfj1/wAls+AH/YwX3/puuK91aQKOTigB22jbVTT9St9Us4rq1lWe3mXdHInQirW6gBdtG2looATbWC3grw//AMJB/bp0XT/7bxj+0fsyfaMYx9/G6t+vMfidq17Y/EL4W21tdzW9veavcxXMUb7VmQWNw4Vx3G5Q3/AaAPTV6UbaF6UtACbaOKWkbpQBUhvre4uJ4I5o5ZoGCyxowLREjI3DtxVyvJfhf/yWj4xcf8vum/8ApCletUAFJtpaKAE20jU6mv8AdoAhhuIrhn8uRX2NsbYc4PoasV5l8FWLXXxBz/0NV5/6BFXptABSbaWigBNtG2looATbRtpaKAGv92snwx82kp/vv/6G1az/AHayfC//ACCE/wB9/wD0NqANfbRtpaKAE20tFFABRRRQAUUUUAFFFFABRRRQA1/u1DY/8e0f0qZ/u1DY/wDHtH9KALFFFFABRRRQAUUUUAI3SuN07/kq2tf9gq1/9GzV2TdK43Tv+Sra1/2CrX/0bNQB2dch8QPh1o/xI0H+zNWjlXy5RPbXlrKY7m0mH3ZYZV+ZHHt9OldfXmHxS+IGr6Xq2m+EvCFvDd+MNYilmhlu8m3sLdMB7mZRyQGZVCDlifrQBmr8NfibEVtofiy5sBx5k+gwPd7f9/cF3e+yum+Hnwp0j4e2d8LZrnVNS1OXztS1XU5POubx/wDbY9FA4VFwqjoK5WH4K+K5LHzbz4s+J5NcYZNzbRW0Vsr+1v5R+X/ZLfjV74d+MvEdj4pk8D+OTaT+IIbQ3tjq1ivlQ6nbK4R38o/6uRGZdyZI+YEewB4/L4fg+AfxEufC+tWS3vwi8TSvc6aWDZ0S6dt0sSMOUjLHcuD8vbpXo2sfCy60v7Pe6V5uuWCAMklndfZNTjX+FkmTCz/7svX1NdX8ZR4Z1bwndaRrupQWbyDfb/NulSQfcZUHJrwH4f8Ax91fw7DL4ZYrZW8L+TFdXML3c1q27b/qYf4Dxt3sMbh2YUAexad4m1e6tZn0/WLHXI7f/j60rxBB9gu4V9224/NMH+9WFffFTwdqW62n8DzTXmdhmksE+xBv+vwfu8f8CzSX3w7T4gSW13q/hbVPEF9bndBqetXKWXlN/ejiTkDgcMpq3NZ/Ffw5btHe6hY3vh6Pp/YtiH1CGP0ZXbZJgf3FB9qAG6T8FV8YW630niCbQLOQ7vsvhDVJkTb/AHWm3c/8BUVetfDPh74Y2622neOP7OlQbf8ATjDe3D/77uPMc/7zVBpXwZ8CeOIpdQsdW1WaaTidrPUXtPm/uywptXPsyVY/s24+CsbSxDRdWsnOIIFtRbai7f3FZMiQ/wDARQAJ488Zyb/7Cj/4Sjb9zz9KlsUf/tqWx/3yprC1L4wfFRr6PS7PwTpM+qeW3n2+nar9rlgbtvyiIg/3nz/smtVfH2o+Lb9LfxJ9u+HWl4+a1nG24us/d/0kZSMf7Knd7ivS/Dtl4f8ADGipFpBsrPTVG/dFIu1v7zs+eT7mgDw+68M6t4oWzn+J174tnaFzKNN0W0aGxQ+/kM7ybfV2/wCA1uN4m+FnhERW1naR32uzLtgstRRzcv8A7TNP9xPVjxWp4n+O0WqXj6L8P4ofE2qK5iutRhnQWOnn1llJwX/2Fyai0LwnG1zdTeIPGFqv2rHnwWdyglm/66zHkj/ZQIBQA/S/EXhnS9Sg1HWNbsdc8StHtt9K0nEy2i7ukMKc59ZGGf8AdHFdN9s8aeKJd8MEHhHS9ufOusXF4/8AwAfIn/AifpXK65pfwX8B6SouLPw3arI22OO3WL7VO7fwrt+d2JrzDW/7C8WahcafL4o/4QfwbYQtNrNvZ62fNeMo22B33kIzL8xVOQP4stQBNqd5Z6z4qibw3YzeMvEUk8kej3WoyG4hSdQQ97M7fJHDH82xEXcT2+Za9j8JfCvTfCGn2+o+Ib1dUu7GFmM9z8lrA33pZRETt3s2WLtlvevMvAMGh6Lp66rp3xDh8OebEsWnaZOlvLFY2a/cj24Vst988+38NYHiX4taz42voNJtPFfhvW9NeRlgVrSa3+1sh+a4fDOPIRunGJDt7UAeiMlx8e/FyRTCSLwbpbrKYdpX7S235d/+033tvZNvd/l9yhhS3hSONAiIMKqjgCvFvDnxSt/Culpp9vpQvUjy7y292Xlmc8s53ouWY815V8Z/+CkHg34J+NvD+ia7ompwWWp2k0818yf8ezq2EXYM7wT1ZelAH0J44+NHhrwHqUek3El3quuyJ5q6Ro9q93d7P7xRPuD/AGmwKzNA/aG8NatrNrpOp2useFNSvG2WsHiHT3tBO/8AcRz8jN/s5zVX9mvw+tv8MtM8TXjR33iPxREmsapqKqN00ko3BN39xFIRV7Ba7vxv4O0rx94Z1DQtZt1utPvI9jqfvA/wup7MpwQexFAHQrVPU9LtNYsZrO9t47q1mXZJDKMq615H+zT8VrXxt4DsNL1LX7XUvFmlNNp98jSKs0zQyvH52zvvCbsjj5q9o3DbQB4Z4k8HN4FmtpbmS8n0C1I+y69bOW1DSfRJTy08H+9nb/EpHI6vTvH194bjiPiURXmlTYNv4j00b7eRD91plH+q/wB7lP8AaHSvR2USIVxwa811DwzqHw8nlvvDNo2oaBJve98OZ+5nky22funrmLo3bB6gF/xJ4duLi6i8V+FZom1dYcGLzMW+pQ9djt0z/cft/usarfY7D4lW8OuaTPNofiKwcxebtAmgk/iguE/jT/ZP+8vY1n6LpEbWMOufDjUIYrGSRnn0WfP2WRv40A+9BID17bvvLVKTVG1LxA+paNB/YXjm3AXUNFv/AN2upW4/hDdH/wBiVc46HigDrvDPjaa4vl0LX4F0nxCq7lTOYbtR/HC3ceq9V/Wu0Vq4uH+w/ix4cQyxufLk5Rv3VzZTp+qOp/zg1QsfE2o+B7mLS/FM32qwlk2WniDCoh/ux3A/gftv+63+yeKAPRaKYkgZQQc5p9ABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQA1uopV6UjdRSr0oAWiiigAooooAKKKKACiiigClqV/Dp1jPdXU0dtBCjO8srhEQDuSelfA8/xk8c/bLkp4q1HbvLJtuDtxk9PavvTWtNtNZ0m7sb+2ivbO4iaKW2nRXSRCOQyngivzbvI4o7ydUA8iEsoReE2hm2rt9F/u0AfWum/tJfDKHwjp+keLfE9rd372KJfwT2slwrl0+dX2oyt15H515LdftKeD/g0J9O8M63deMvBV/+5t9Ht7eY3ulzOx4iMiIrwN/dZhs+b5iuAPp74NaXZ6T8L/DiWdrDbRyWiTSLBGEDyuuXc4/iZiSTXOeBvFTfGT4I3+oeIdNspVupr+xns1TdDIkNzLCuQc9QgagDnrX9qXUWhix8GPiQ25R8y6XDj/0dWpb/ABW+KGuxi90b4RtFp7/6tdf1uOxu+Ou+EI+32+Y1xF58Yof2YfiLpvgbxNe32qeDtR06fVLHVplaSTRYYpERobh+rxAugVzlh0bOa+j9D16w8TaPZ6ppl1Fe2F3Gs0E8R+V0IyDQB5Z/wsD4zf8ARJ9H/wDCsT/5Hqvd/Fb4o+H4Tfa18I/M06LmVdC1tL679Bsh8pN//fQ4zXc/CT4hx/FH4fad4ojtWsY7xpgIWk37fLmeLr77M/jXC3X7WHg2LVPs6C8ktliZnuFixhwR8u0/zoAjg/am8J3VvLYeJbTV/AGrTho4LDxLZNC8isuBKGTegTdlcsw+6ah/Yd1C1vv2Wfh6ba4iuPK0/wAqTynD7HDtlTjo3PSvT/EF9out+Cpf7S+ytpmp2J/cahsCTI6Z2MG4PB6V8v8A7JvwH8A+Lv2d/Al3HcXul6xNpavIuh63cWg8ze371oYZQjP93JYHO0bs0AepfHz/AJLX8AP+xgvf/TdcVjftf6zqGkw+GvsF7dWe8z7/ALNIyb/lTrjrX4wfH74ofELRfjh4r0+X4g+ItRl0LXbyK0vJtQcSIyyOm9FTaqFhn7gA+bFfePww+LB+IX7LnwvstUvtSuvEdhb3L3s2q72lnieVlSZXLZdG2sgbd1RuPloA+gf2Tda1G68fT2Vxf3Nxapp7skEszMifOn3VLV9erXxr+yHn/hZ11kFf+JdJ/wChpX17d6raWd5ZWk0qpPduyQof4iFLH9AaAL9FJupaACvKPi5/yUz4P/8AYcuv/Tdc16svSvKfi1/yUz4Qdf8AkOXX/puuaAPVl6UtN3U6gApG6UtI3SgDyX4X/wDJZ/jF/wBf2m/+kKV63Xknwv8A+Sz/ABi/6/dN/wDSFK9boAKKKKACmv8Adp1Nf7tAHmfwT/4/PiF/2NV5/wCgRV6dXmXwV/4/PiD/ANjVef8AoEVem0AFFFFABRRRQAUUUUANf7tZPhf/AJBCf77/APobVrP92snwx/yCk/3n/wDQ2oA2KKKKACiiigAooooAKKKKACiiigAooooAa/3ahsf+PaP6VM/3ahsf+PaP6UAWKKKKACiiigAooooARulcbp3/ACVbWv8AsFWv/o2auybpXG6d/wAlW1r/ALBVr/6NmoA7JuleMaxqUXgv9pLT77VAIbDxJoa6VZ3j/cS7hmeXyM9i6PuHr5Ve0Vz/AIu8G6R470G40bX7CHU9OuMb4Jl/iHKsD1VgeQRyKANzcu2vlT9qrR/FnxU8VWelfC3xA2ieLfCum3l/c6hZqjuPOVESzJP3XlAdvbYpr0tf2e54cW1t8SvHNtpQGFsV1RGwvoJWQyY/4FXdeA/h1oPw10d9O0DT1soZZGnnfJeW4lP3pJXPLufU0AfFPwb2f8I/FqOpyy37ySfZ7jXJQ0s32gNtaK4V9zxyK3ylG/Cux8YeH7m8dtRsJdTvWtQqX0Wm6yiyiPb8srW7ohO3dtK/xIzf3RXefFn4dXXw08bXnxF8LRr9k1TEXiLSpE3W91/Cszp/46x+hrW03wlpHj7SDqHhcwLLDxLoGrgvDCzL8yxEfPDkdCh2+1AHI/D3xJptusOkeI9R8bWEbN5NvPd3EzLFJ977M8sfDMF2lH/jT3r0fT7P4fXSTFPGd9L5S7pBPrsqFB75YYrwjWLy88G+IrjwvpD6hcap5K+T4f1WTzbSa3HzeSl4Ok0O1mj+YvhsYres/HWmX11Zab8XdKbxdq3ktdafFpVp/aFhJGD8rlQuVkXdz5uPUUAdjqnww0b4i6gJ/CEGoWDNGu/xeNSuE4DbgsSb/wB//wAC+T61UPgvw58G7pLzxvaSa7HKCn/CUNdzS3O4t9x7fdlB/wBcsj2FdDpPgOPx9uuNIks/B+ksNqNoN6XvD/wJG8uP6bWrRvPhvpXw1jm14eKdWW+zgXWqONQuJCflCJvXfz/dSgDJtdP0Hxtavc+HPC9ifD7xs0uta1cP5JXviHdk/wDAttcVY/steF/iLdPdWcVxp+lqFVdTsz9niuR82Ut7T/VrHhvvupJ3cetWPEnhPWrjULjXNT8S+H9DEj749Jw0TXTfwveRI7LMf9jHH96tKx/ap1rRFttO1rwBqGpX5T/j80A7rL2d/O2PCn+04x70AdTD8K4/hvokUFj4V8Ma1pFqOYltksZVT+Jucxk/981z8PjLwd4ouLa10bwnpmhCb5TrGu6WkVuD/dhbG2Zv91tvuazb3xd4o8YNb3fi+zh0Gyc749PlguL6zRf4WdoV2SH/AHmx/s112ravot14de51Tx9Jq1t8sSaVpTQ2qzuflSFU++cnjbuoANV8J+F/h3bwJp1npmoeLdVGwarqEcW5I/45j8uEjTsgwCdq1z+n3vgrUNWt/DuiwjWNC0SQXGpT2Ni1y+oX2d2xnC4O1vnfnrtHrWbN8D/D3grS7W8sIRZ+ONYZkhkW4+0W9knVsrLlPLiHzHpuf/eqK08ceJNLsbjwV4D0+PxdpthuXU9f06RdO+y5G98yybkeZ9xbcn3evpQBseMviFqnjJby00jwm2m+G7Obyb2+1CRLd72btaQqm8nc21Xbj0/vYm0wanockseoeJrSHxLdIhm0rwvpi3E1tCo+SBGfcEROm5lGTuNY3w/Wy8ealYt4q1iXw1ptgNmi+HYkexidCu0zPK/MzNzjaR8rZ/ir3V28N/DDw3JcW1vb6bYA58u1j+edz0Cgcu5/E0AeW3HgzUZNFutV8UajceHtMQb3k1W8a7u8f7q7YUJ/uhXryHVP2Q/Df7V82n65rOlzaB4d0W6U6LLIqvd6modWlluN3/LN9pAT/azXslro2p/HDxM1xrsctr4Z06cgWKn5C4/5Z7h99/77DhfuDnc1e5W9vHawJDCqxxRqEVFHCgdqAPDNB1TWv2eWbw5eeHtS13wDGXOkapo8LXM1hGWyLSaEfPtT7qON3y7QcYq5rXxe13x9EdE+HvhvWEu7vMUuv63YvZ2mmoV5l2ybXmcdkUdepUV7JeXttp8LTXNxFaxL1kmcIo/E1DpmuafrUbSWF/a3yDq1tMso/SgDzZf2b/B3/CD6F4eazPnaJbrDZawh2Xsb9Wk81ecs2WI6HdWP9o8a/CqeSK91Mapoipuhvr5GeEe0rr88P+829P8Adr3KopI1mQo6hlbgq3egDgtM+LVkltCfEVpL4deTaFlnYS2km77rJcJ8hB98V3UNxFdQrJE6yxOMh0O4GuB1L4f3Xh9Z5/CLwwQP80+gXnzWNx67V/5YsfVfl9VrB0HTdKuNWmstEub/AMBeIoV3y6ScfZ3/ANpYj+7kT3ix+FAHS+JPBd7YapL4i8JyR2msMD9psZTi21Lj7sn9x/SRefXIpLG60D4tae9tqNi1vqdkV+0WM52XdhNj7wYcj2dTg0R+KvE3hmB/+Ei0X+0rePn+0dBBfI9Wtz84/wCA76q3lronxKjh1zw1rMdpr1uu2DULb/Wxr/zxmjPzMmeqHHtg0Acr4i0PxZ8NNSTX9O+0eJ7ZEaO68lQLq4jH3RMnSR16LKvzdipBr0bwl4u8PfFjwimoadJFqWlXSNFLDKnKt0eKVD0YdCpqr4V8by3mof2D4gtl0jxJHHvNvu3Q3SdPNgb+JfVeq559a5zx98M9Ss9Ybxd4Duf7H8R7gb6zX/j21VPSZOhkA+6/B7bsUAaBi1X4Vsptxc614SL/ADxE77nTE9U7yQj+795e2RxXd6Tq1prlhDfWFzHd2k6B45om3K4rgPBfxksdchsrfWYho97cFokdm/0eWROGQOfuP/0zfDfWr2o+Er/wrfy6z4SUMsz+be6Gz7IbnPV4u0cn+10P8XrQB6DRXO+FfF1h4ss3mtHdJYZDFc2sy7JreQdUdOx/n1FdFQAUUUUAFFFFABRRRQAVgeMvGWleA9Bm1nW7k2mnQFRJMsTvtycfdQE1v1k+JYXuPD+qRRIXle1kVFXqWKNxQBkeAfiT4e+J2jf2v4bv/wC0tP3bBcLG6K3Gfl3qMjnrXV7hXxD8ZND+J1r8Kfhxo3h/QtXtb7T/AAyrJeafHcTTQ6kiIqQskcqBPult77x2210/iL4f+K7f4f8Aj3xLcvr0XiG41pEw2ouhOledb+clum9UTcgl27cHJbH3qAPrdmFYeqeMNH0fTdVv7vUYYrPSgTfTb8/Z8KGO/HT5SDXwZcaf468ZWl1/wi114qTwZba/qtrFEq3F3d20223W2YqkyPsT97jczBT1FfRXwf8AhvNZ+IPi5pOvafqBuNekt2l1O6Rlhv4XsY4XZfmZN+9Zdyr03D2oA9th8U6Xca1FpEd5G+oTWn29IFPzGDcF3/TLAVseYPWvjBvAPxQm+FfiXULuyvrLX7e4sPD6RwB5bifSbJ9skyBHV287cz7VYMRRovhHxZp8ng8eLLfxx4g8PNZSfYE0gTW1xZ3hu9yfaE81nCLDsUGVjwrZoA+wdP1S11aF5rO5iuYkleIvC4YB0ba6/UMCKvbhXxDD4B13w74ffT7jw14vXQk8V65data6LJKlxcvMzvYzIyupeH50zt4D9fumvTf2e/hv4tk8SXWufEOTWX1jT7LTY7Rp75/s5f7Jif5FbY53H5iR94UAfSdFIvSloAKKKKACiiigAooooAa3UUq9KRuopV6UALRRRQAUUUUAFFFFABRRRQBFN/qX/wB01+aF9/x8XX/XR/8A0Kv0rvFkktpRDt80oQu/7ufevknUf2SfEUmsrDHqloY7hJZTcLE2yFsjCn/ey3/fNAH0J8NNUs9M+F/hR7y7htUexhUNPIEBOzpzXmX7LOuadffs8wRQ31s91Jd6rL5Cygvzf3B+7uzXC/td/sbeLP2jPg94L8F6V4j0zRLjQLpJ3u50lZZAsJjwMc9Tmvm39gT9iHxX8Ptdsfi3ceKNNu9IS21KxOnbJfNyrvCzc/LjchP40AfZnidQ37bHw7Rhkf8ACFar/wClFtXzt/wUe8beKf2S/D+h+JvhV4juPCreItUki1DTIoopbZ5PLL+ciup2Me+3AbOTzXtPiHxRbN+2d4AnbWdH2p4O1RDKtyuwH7RbfKzbutfNv/BaC+nuPhb8PFluLOQSaxNLF9nbLFPJ4P096APOf+CX37VHxE8YfG3S/h3rnibz/B0OlXs0OnvDCi+ZnePm27j8ztxmvSb7DXM5HyrvbH/fVfEX7Auh3PiX4+JY2fhyTxNcNpN2y20Gorp80OFX99FOWGx07Ec19tOpjdQHG3O35vmx/sn/AOKoA+n/ANor9k7Qf2rfhL4M0/Xta1HR4tEt1vYn07ZuctbquG3L04rwb9gv9nL4c/s9+DtE+Nmo+Np7O+1DQ3W7s9RliW3hSSVfmVfv9UX/AL6r6Esf2fvFfjXwzptt4j+KWu6hoE0EUn9k2aRadsXZlFFxbKk2F+X+L5tvNbvw8/ZT8EfC+30+LR/D2m3Uuntm0vdUWS8ubf0RJZGLKg5wvQZNAH5xfHT9mT4K+NvjRpOr+GPFPi7xnB4u168l1aTw3py3YtneOW48uL5fmfdtO3+5k16b4Y1670v4eeHPhpceDNa8IW3hVZks18Q3Cf2heQySFhM0QRdifew3+8O1feni74TyeKfFXg7XPtFrZXHhm+lvraGCLCTPJbvCyv7Ycnj0ryb9r6O8Fr4aa8+zFmeZd0AK5+VfX8aAPnzSfE2q+F0vP7C8aaZ4D1W8hW1h1PUoGl+YureVEx+QSPt27nUgfNXW+F/F3iHXvEllpnij4qeKfD+u2LyNcX1za2FxaWHysq/Z7lIRHMXXapG3jc3da6T9l7S11bxxqFs8FpdwSabIjxXg3oQWXO5a1fEX7LmqWPijTrS11DTbWLUriYWsEELqlsgRn249FVdooA9K0LwT8VNNsYr/AMLfFez8YW18it5vivS0dEVfumH7KYvvfNndn7oqe91T49+ExHdzaT4U8fRsfL+waQ8ulSR9/MaSZ5VZeMbQufmrxbUvhR8Yv2cry71nwfreo+KNNuWFxPasftMMG3/lj9mPz+TzwtthyzNuPSvTfg/+1K3jj7NpfiC0j8Pa2peCW4uLeSKxkuEXcYllZvkkwGYwv867WU8igDatf2j9S0mMW/ir4XeNNN1iPm4i0fTH1O0X0KXKbVfjGeOOlch4s/aW+GXjj4r/AAktNE8Y6bfXUet3DSQiQo0e6wuAudy/3mUV9GW76rNbo3nWT7xncittP0+avG/jV4Ph8QeNfhfpmqaXpF9YXWsXKTW0trujkH2C5bDqeo+WgD2ux1zTdUcx2eoWt26jJW3nVyB+BrQ3V4Vrn7J3gzVo40sdHtvCTIdzXHhOebSZpOPuO8DKXTvtNP034K+OvBum+T4Y+KOpNHZqxsdJ1q3iu7Yt94RTXDq1w6FvvNv346UAe57qG6V4fZ6/8dPDO5td8OeF/FcTtnf4euZbRoEHXKTk+Yx7BWH3femWf7TOn7nbXdP1TwVB92O58UaNcWUUx/uoW6nbzigDZ+F//JaPjFz/AMv2m/8ApCletbq+c/gj48sfF/xW+KN1omu6TexXV/pyh1Zv3mLJM7FLAmvd1h1dUwJrP/v2/wD8VQBrUVleXq//AD2tP+/b/wDxVHl6v/z3tP8Av03/AMVQBq00/drM8vV/+e1p/wB+3/8AiqGj1f8A572n/ftv/iqAOG+Cf/H18Q/+xrvP/QIq9OryT4PW+qx3Xj3bLafN4pvGO6Nv7kX+1Xovl6x/z3s/+/b/APxVAGtRWV5er/8APe0/79N/8VS+Xq//AD1s/wDv2/8A8VQBqUVleXq//Pa0/wC/b/8AxVHl6v8A897T/v03/wAVQBq0Vl+Xq/8Az1s/+/b/APxVJ5er/wDPa0/79v8A/FUAab/drJ8L/wDIIT/ff/0NqeseqfLme19/3bf/ABVeXfFzx1r/AMIfDvhqexTT7xtS8R2GjusyPhEuZ9jOMN1G6gD2XdS1hJHre4bpLLn7zKH/AErnvDPj5vFHiTxPodjLA+oeHriG2vlkhdVR5IxKmD3+RhQB31FeT+MPi/D4XvILM6zoq3n26K1uYJXbdAj9Wb6V6RoupQ6tpdrewTxXcU8auk8H3HB7r7UAaFFFFABRRRQAUUUUAFFFRzSCOMsaAFeorH/j2j+lIvnSclgg/uqM1LDH5cap/dFAElFFFABRRRQAUUUUAI3SuN07/kq2tf8AYKtf/Rs1dk3SuN07/kq2tf8AYKtf/Rs1AHZ1w3xK+Jll8N9PtWltbnVdYv5fs+maPYgG5vZv7qZ4AA+Yu2FUda7huleOzeQv7VMP9o/60+FiNJ8z7uftP+k7P9rb9nz7UAO/tf423Q+2J4c8HWsLfMNMm1S4Nxt9DKsWzd+BFdB8N/ipH45uNQ0vUNKuvDfifTGAvtHviC6g/dljccSRN2dfodp4rveK+Wf2uPFHjLwX4w8Oa18MNJttZ8ZWek6g99bzltqab+6zIVHLlZdjKvfa1AH0J448UaB4b0eY67PClrMjJ9nk+Zpv9kJ3r5I1W61/S/EGNHS60vQ3TzYblZ1iV4f44riY9lX76Q5OPnDZV6y/hrqV3rlnZeM/Epl1u/1CNWj16e7Sa3/3YnC7EAb+FcEfWvTdf0ez8QWS/wBqx6NFdKVmtNQ1xLyZoJB8yOjOuz/vntxQBH9n8LfEzwbLo2razDb3Vq6ypoOg27yxQzD7krOis8yH+/kBgxrltEt9Y8P6csHhfwvcad4be7ZJ7HWJ1tn0bUt2Q8Mq75BG7bcK6kfN2DGtTwxfeIWmT+y9X8J6dPpsn2cXNjvmTTZHYMsTKH5tZvvD+4eKua94m8baxrGonTH0VNUSFbDxVbNYyvbw2/zKtwnz/vim5sbe27djbigBtt4kvfGWoHRNXgg8JfEaN/KTTtIhMN5MP+flJi2ww/xFtpx0xurorj4Qap4VaLxF408Wf8JzNF8qRa1dPaJbZ/gh8najN/tOmTXBN4N1XWLlPB9trmm+JPFFtJ9r0vxRPYFlO1QpEtxvzvReCkXO3aflrpvC/g/4iLqDalc+K4vFGs6UTFd6deacn2vT3bvbZfYUK9Gbkj+L+GgDotJ8TW+tOts3h+T4WacePtmoWaLc3S/3oZV3JGP9pzv/ANkV7H4X8O6JpWkCHSY4pbWQfPL5nnNN7u5yX/GvIrf4gNrlxdaXZeOrrW9ctxtutBsdGia5gb+7KhbCfV2xVfR/gX4xutQ+22/i+78BWT5MljpEMLSzsR95/l8tT9FJ/wBqgDs/F2l6N8PjPq+meIbfwpKwaWaznO+0ucdcw9Qf9qLB+teSL44uPG+qP4i8T+FrPwto1pGTpWreIIN1nMh+9coi/PvZfuLJsIDerUyx+GfiL+3L+zg1m+8WadZy+Xq2uWrQxaheShsrbbnUghOr7WA/h29an8UeKrbxh4os/B9v4j1We6STnSNYMVv9puBtZUdTFzDF959ucnYKAI7H4OWOu6lFd3QutS8YasPOt2aVobbQbH+FvJjYJvP3gGUku391a6+z+F93dXT+EPDfiXUoPDmnlX1WW+SO6W6mPzfZ9xXJ3fek+boyr9MbUPg/q/w7S10bwP4w1mfxLqm6a6try6/0fbwr3DMF3Rqv3UVflztG3rWnZ3Wg/D3SbTRL2/8AFGk61I7paaRPqfz6hMfmYxS/dk3HLFt3y/xYoA63xdda94Y0cLq3/CP+ILNv3VvYyWjxTTv/AAxoo3jP/Aa8cufCPifVvEk5ttG1TSLyEZuv+Efv4rm001D/AMsYopmVTcsvV1UbB0+9V3UG0tdeS0/4S/UtX8dXKMs39n6lcXcWh2z7dyIse7Ltt43ck88Ba7XRfBdrpmnJp3h/wp4omt1Jc3GparLaJI7NlnfL72LMxJO2gC/4f+JF/wCDdJgsrvw/J9itk2JFb2kttMiD/YfKMfo9Vde/bI+GHhnxZ4a8PatrradqevCYwLdRNEsJjXdtkJ+7u+6vYmmX/wAK9QhtZdQ1XVbHwzYQjfIy3tzdsB/tPI6j/wAdr5N/aI/Y4u/2v/FHhfVvClxer4f0S5WwvtS1eXY17A8uZXtk2/wDPJ+9+FAH1Z8OfAdr8dLG3+IPjy3k1O31Medo2gXT7rOwtSf3TNF915nXDMzZ27sDGK3PFn7PWhJb/wBqeCIIvBfiy0/eWV9pS+TE7j/llPEvySxN0KsPpg1V+CHja28J2Nj8MfE95Fp/irQYfslvHcts/tK1j+WK5hJ+/uQLuUcqd2a6z4lfFzQ/hzYos1wt/r13uj0vQ7Zw93fzdkROv1boo5NAFv4Q+Pz8TPh3oviKS1+wXV1GVurPdu+zzoxSVP8AgLqwrtN1fPHw7/Zo1Hwx4K014PFesaB4umD3eqNYXbvZTXUztLLmFjjG5yuRtPFbtxqHxC8HyL/aN1JcWaLzeR2X22E+7hNkif8AfL/71AHtlYPijwjpfi2zFvqdqJtnzRyoSksJ/vI45U/SuS8P/ETXdWsPtlppem+I7PO0z6HqIZge6skirhvbdWt/ws6ytY86rpur6L6teWLlB/wNNy/rQBm/2l4i+HsajVEm8TaACF+3wx5vbZf70qD/AFij+8nzf7J61fuPDPhX4iWkeqWwjllY7o9T06UxTIw/20w34H8q2ND8ceH/ABMzLpes2V869Y4J1Zx9V6isPWPh6Y9QudY8NXn9hazMwefam61vCP8AntF3PbeMNQBheJvAPiO50/7NJcR+J7WE+ZbyzsttqNs46PHMo2M3+8oz3ql4P+Na6bqlr4a8a+dpGrTtstLzUIPs63Tf3GP3BJ9DtP8AD6V1ujfEIR6hDpPiW0/sDWZG2ReY+ba7b/pjJ3/3Gw3tXQ+IvDOleLtHutK1mwt9T066QpLbXMYdHB9jQBxvjnwURNc65pNlDf8AnL/xMtGkRSmpRr3HpMv8LfxdD2xm+G5tS0fRotT8IXEniTw6QxOi3zst5b46xxO/cdNkv/fQrnpvDfiH4JqE0zxBez+Ekb93Jqe67SwT/nlNuO8R+kqt8vcY5qfVPEWqeDdSfxQNCuLfzlV9RGnyfabDUE28So45SQDoWUKRwx+6aAOp26X4/uF1rw3qB0fxTajbIJI9kv8A1xuYTyV/l1U1veF/HH9qXj6Rq9sNI1+D71m77lmX/npC38afqP4gtZMNj4Z+MGk6f4q0LUALgxt9j1rTJFEyL3Qt3Xd1Q8ZWsPxJczWdnFY+PoCIbVhLaeLNMGxIHHAd+rQP78oef92gD19Wp1edaF42uNBks9M8T3ELxXRVLDX4mC217u+4rdklPp0P8PpXoatQA6iiigAooooAKKKKAE2iqGsaNY+INNuNP1K0hv7GddktvOgdHX0INaFFAHm/j7TLXwF8L7q28N28WhQQSwiGOxQRBN1wm7GPXJr0VK8d/ai+Jnhf4Z/C+W68U6zb6LZ3V3bwRT3AbaXEivt4H91G/KvTfC/iTTfGHh/T9b0e6W+0zUIVuLa5jB2yRsMqRmgDX2ijaKWigBNoo20tFABRRRQAUUUUAFFFFABRRRQA1uopV6UjdRSr0oAWiiigAooooAKKKKACiiigCnqU09tZzS21u13MiEpArhfMPpk9K5b/AIWPFbsILzQtbhuh95IbB5kB9nUYNdkzbetea+NP2iPh/wCAdbOh6p4ktm8RtGskOg2bfaNQuAenlW6Zdz8p4A7GgDaX4p+HoV/4mF3JojfwLqsTWzP7qH615R8PNf8ACXwX+Cb6DrnjTQ52tXvrh7lbhUi2STyzD7/oHxWnD47+KPxMmkt9B8CQeDtObc8Ou+KnWVpIWbAKWyHekn8eyXA+XBqK4/Zh8O3VpNrPjmeXx/4hhDTR3OqjbbW0n963tx8kPRc7eu2gDxe6+C/wu+L3iaG8+GXwn8LmO3tpIZ/Eeu6b5OlypIV+e3iCbbp+MiTBQbSD96vnX/gql8ILP4a/Bz4dyTarJ4j1n+03tP7RnjSLybdIPkghiT5IY1wPkTALc9a/TiD4QeCxGhHhrT1+Ufdhr88v+CyngvR/Cvwx8AyaRaDT0m1mbzIIDtidvJ++V9fegD5s/wCCSMJuP2ureIY+fQr9fm6dEr9CLr9l/WF8ZRaE+t2jXVzaSXyz+W+3ajqm1v8Avuvzu/4JQ2d5fftbWcVhqB0y4/sa9bz1iEvy7U4wa/ZJvCPjBfF1prv23RrqW1sXsUaZJEMqu6Pvbb0b5PujjmgDu9C09tJ0XT7J3Ej21vHCzj+IqoXP6Voba4uTxF4t0ybybjw0mqlhuFxpl0iIPYiVgc0Q+P7y2aT+1vDOracu790YI/te9fX9zu2/jQB21effFr4Q2Pxas9OgvbyWy+xyO4kgRSxyuMc1q/8ACzPC6yNFc6zbWFyv3re+f7PKv1R8GtfSvEWl65btNp+oWt9CDsMlvKrjd6cUAeefCv4Bad8LdeuNUtdTub2WWA2+2VFUKu5T2/3a1vFGrWh+KXgzSxKr36C5uzBj5kiMLJv9hniu7aaNWwXUM3vXm6KjftJTn7xXwpHj2/0x6APTWr5w+Ovw707wXea342n0SPxb4W1qa1TxT4bvLdbiKREZIkvEyp2eSvzMvTYHPWvo9aimjS4jeORA8bjBVhkEUAeF2Phbx14AtYdQ+HmvW/jTwjJH5tt4f1iXDww43Klpcjls9FEp2qNoHArndX+NFh4n+L3wo0TW9MvvB/iSDWLnzNN1ePYkj/YLlWW3m+5OAxUbk4+Yetb1vFcfsz67qkskJn+GGrXbXfm26tjw9IwXcGQdLdzucuPuMzlvlq/8evDOj+OvFvwp0zV7G31bSL3WLlJoJxuSRPsFwy/qFNAHtqsNtPrxZPBnj74XwzDwbf2nibw1bjda+GdVzFNbRrz5VvcjO7P3UWXhBjnit3wB8aLLxVqUeh6zpN/4N8WNG7nRdWXG/Z9/yJh+7nChkJMTNt3rmgD0vbTHjSQcqG/3qeDQ3SgDwWH4QeEfH3xg+Ieoato1vJrdrLYRWmtQDytQs1+yq37m4HzxnLN90j7xrctNB+JHw7mf+zdSX4gaJlitjq0y2+oQjokcc+3ZKO5eb5zj73NbHgT/AJKp8Tf+vnT/AP0jSvR6AOM8E/FDw/4zvLvTLO9SHXrFA9/os52XdnuP/LRDyB6HowwRwRXZ1x/jj4c6X46htmu5LzTr+2ffb6lpdy1rdQ5xlVkTnDbQGXoa5Ob4mar8M7o2vj618nw7aQrnx1vRLR2/6eIhzb+7t+7zj5vmAoA9cpG6VQ0vV7PXNNttQsLuG+sbmNZoLi3kDpKhGVZWHBBFX26UAee/CH/j68ef9jNdf+gRV6HXnnwh/wCPrx5/2M11/wCgRV6HQAUUUUAFFFFABRRRQAV4J+1/j/hFfAO7/oe9D/8ASkV73XgX7YDBfCPgMudq/wDCd6H8zcf8vIoA92kmjhjZ5HCIv3mY4Arwb4G6ha/8Lz+PBNzDh9a0/Z+8Xn/iXQ9Kyv8AgoRfbv2M/idNbzjIsUxJE/8A02i7ivw4/Z71S8b48/D7N1OfN8Q2G/8AeN8/+kJ19aAP1e+PGP8Ahbvih+Nn2pVLKOrbF3V9kfBb/klPhT/rwj/9Brgfi58F/DWp65p2tTxS/b9U1a2tblvPZVMb/K6gdsqtew+H9DtPDei2Wl2SlLOziEMSsckKPegDTooooAKKKKACiiigAqrN81xCO2Sf0q1WXrt42n2Mt3GAXhiklCt32oTQBpLinV82eHf2rF8SX3h+CXT7vRoLrRrfUru5vNNmiUTSXFvFsi8zYHj/AHzfOGOOD81W9U/autptU0O10bw5rV0t3PBK0clou+7s5o7hopoPn7mDPzYwKAPoiivNNW+OnhvQ/CPh/wATXRuk0jW4mlt5/K+6RC8wR+flZgjgf7QxXGeLv2nLW1sLcabpV/a3VzqENpbz6lbhbeci7ihuUQhs703n72ORxnFAHv1FfOmpftgaLc+IrHRtC0m91G4bWG0m8lV4StqPslxOJhtcq/8Ax7n5cg4z/s5q6b+2J4c0CwspvE1zI51KeFdPNta7HkhNtbSNK6Fzj57leAzHnvigD6VopituVWHQ0+gBG6Vxunf8lW1r/sFWv/o2auybpXG6d/yVbWv+wVa/+jZqAOzrh/iT8NbD4jaTBDNcXGmarZS+fp2sWLBbmwmxjfGTx04KsCpHUV3FZmua9p/hvS7nU9VvYNO062QyT3NxIESNR3JNAHmMWg/Gm1t1sR4p8JXaKNn9p3OkXC3DD+8USbZu/Ie1b/w1+FUPgRb2+vtUvPEfibUsHUNa1IjzZlH3Y0RfljjXPCKPrk81zi/tSeDpbY3tvZ+JLzSFP/IVt9Au3tMf3t+zlfevSPCfjHRfG2iW+saDqVvqum3A3R3FtJvU+3sfagDwLxp8Mbr4L+KrzxZ4PVY/DOqTb9X0GRc2iyn70mzoFbvjoa67w+vhXxjo8smmXlz4J1aOPzpls51QIF6uqPmN0/2tv1wa9M8XeINI8O6JPd6zLGlkw2FHG7zM/wAAH8WfSvjjxlbzrrFhcO403w5cXSvpcc5bzS4Ztqoq8yT/ANxm/dn5UbeVBoA6f4naPea41rqKaJpOuWa/6Ptgt/s2p6/bH/WwxRdNjfeD8c9MBt1Y+m6L4e1q10zT9U1fUvhppMkc39h/6Q9vKMt89pdyuzRv/F8ityN2Wytd94D8Uazb3KBLGPTYb6Zon8aa8ha4lb/ni9v/AMsHDfLsZkT0XtSeNND8CeDtXuLrxZqi+KtK1Y7NQtrphcLZTNwLiK3TIjU92C8NtbPWgDjfCN9rLN/wrzw1qs0t7bFr3QrrULWJbS2UN83kzBUMyLu42qTsfB+7XW3Hhn4h+N9Qm1i9n0i48TaM32W60HT7iaxt54/vbJW+Z5EcfMjMwA/u9azL/wAKwa9f2ekWVhrukeJbCT7b4c1qJPs1vPsX5WaKRsZVW2uir845+lPQPGfxk1q+TULzSPD9n4u0YPbapY6a7m5u4Qy7tiPtRxj50O7gsF/ioA9H0PU9B8TeH4nk8AapoctuTEZdMVDcWUy/eDNGd4Ye6nPvXPeLfjrL4JhtvC8viSK7uLyNlbUtQi+yahZW/wDFM8TqqSP2G3GW7fKayvFniDR7i3t/Fll4l1q61m82Qv4alf8AsxtVb5lVFUbf3ifNzuPCsprvPDmn+BPhh8ObvxpqkdldSXCfbLy+8nzZJZOiwxB8v8rfIE65980AYer/ALRfg3Q/Aen6T8NZn8W6xfFrHSrPS4zKz3GNzvK5wF2gl3Y/1pdO0LVfBng+Z9U8K2V7eXBUNLrFz5091MzfJFGibv4j/eH8TGpvDfwV03xlZv4/8babBoPiW5Qz2v2VVhOk2vJSIleHYr80u7qfl6KK4aWTUdJ1pNXkGr3rtGz+HY9NuptkMJ+Vry7tn3+WG/vKp46LlqANi9+F/inwi0F5b+PNQsPF+sHd/YGjwJLby4+7HunV3jhRflLqyfxHq1Ra14RTw3craeMPCUXj3xbqYd475ZJb6Gzj3LuJhdt8cKfL93JJrsrOzvPCOkprOleL18W+JNcOyB5rRJTdSbflRCjL5cKdT6fMTzW14V0bx74Rurq91PS9G8SX16yvd6ha3b28zY+6iI6EBF7Lv9+rUAY/wx0G38A6bcR+AtWs9fjciW90rUGWG68zHOx8bk/2UlUgcAMorsLj41aLbj7A0N0niYpuTw9KAl23+1y23Z/00zt964jxj42tfG3nQweD7pPsrlbzxE8Cyw2Kr9/ypY23O4/2flHc9q85s9D0bxlYzW0XiV30V9rhfGSyw3mqv6/aW2yRw9MBc7vp1APTNO8N6v8AGfWDe65dMPDkLrtitX/0eVkb5kh4y4/heY9fupt5J9u0/T7fTbOK1tIFt7eFAkcSDAVfSvE9F+IGu+CbWzS6s31XRIk8qTyJUuDAo6MkyffX/fVD7mu60/42eCNSj04r4jsYZb+7XT7a3nlCTPclSRFsPzb8KaANrxf4B8O+PLOO08RaJZ6zbodyLeRK+xvVT1U/Ss7wf8IvBngG6e78PeGtO0u6dcNcQQr5pX03nnFcHZ6r4q+Nuraq+h6/J4T8C2U72UGoadGj32pzIzJM6M6lYoVYbVYAsxVm4GKdffDfx58O9Mn1Hwl431bxVcW3746H4oaOdLwDqiTKqvG5HQ8rn+GgD21aGWuZ+HvjjT/iP4N0vxHpfmC0vo9/lTjbLE4O143Xs6sGUj1FdNuoA5HXvhroutTTXcUMmk6nKv8AyEtLk8iYH+9kcP8A8DBFZm7xx4Pt40lih8a2UfBljxa32P7xX/Vufpsr0HdQ1AHnFneeBfHl4be6sLSPWF+/Z6ja/Z7tf+AsAx+q5FaN78LbBocaRqmreHpV6SafeswH/AJN6f8Ajtb3iDwtpXii1+z6rp8F9F/D5qfMnup6g/SuVk8H+JfCNm3/AAi2uNfxRnMema+xlQj+4tx/rF/4FvoAbe+CfElxpb6fearpviazbrHrFjtc/wDA42UZ99tcvb33xG+GdwV/4R2XxX4eaTLRWl+ktzZp/wBMvM2GRB/db5v9o12MPxQt9Nkgt/E9hc+GbiT5fOuBvtC/osw+X/vrBrtYbiK6hWSJ1licZDodwNAHnmi/HPwd4msGke4ubBNximg1Wylt2jbujqy/KfrWbayL4Mkl1HwldQ694WkKtPodpIJHtf7z22P4f4jF/wB8Y6Hr/E3gCz8QXUeoQSy6TrMP3NQtPlLL/clXpIn+y36VxdxDpdjcJaePvDun2sjyeVBr9nDstrj+7vcfNC5/uscZ6E0AZuveANOug/jXwDcTWM92ubxNHl8o3AB5kWI/J5y9MOvzdDzg10Gk+Ltfm0O3vltbXxpoU6bTc6aPKusdDvhdtrHsVBBz/DWXqXwZuvC95ca78Ptc1LTb2YiW50ya6+0Wl6PpJu8t9vRx7ZrAjufGGmy3/iTwZc22szowTWvC2o2ptLjevWVdhbEuPRdsg289DQBtQabpGsfaoPCF7aojxst54Q1hClu/8W5Yj88L+6/J7d6Z4W+Ik3gm9bTNVjvVsUJd7XUG33unL9R/r4B2dcsF+9Wm2tW3j7Qre71nwTLfwSpvjvNOkSYp/uN8kiMPoDXK+IJrGTTF05dcafyT+60/xfHLbXEf+1FeYDow7Mc0Ae+WN9b6jZxXNtNHcW8q70ljbcrj1Bq3XzBpnjLW/hRerc21tdat4enZWuLFmR3Rv4nhdP3bnvtXG7+7nmuwk/ay8CatY60vhjVI/Euv6aLaL+xrU7ZnuLg7YYTno2fvf3MHNAHtu6jdXjmn/D/4l+JLOK98Q/EWbQNQkXe+meHLOA21sx/g8yZGeXb/AHjjP90Uad4v8U/DTxppfh/xtfw69omtyfZtK8Rw2wt3W6wzfZrlFbaCwU7HXAY/LtBxuAPZaKaPu0bqAB/u14tN+09pf9qarZ2HgzxnrqabeyafNe6Zo5ltzNG211Vt/ODXtD/crx/9nGRY/D/jeRyFCeLtWJZu376gDzz41eMvDHx58A3XhDxR8LfiLPpdzNFMdmhYdXjdXUr8/wCH0Zq6S9/ak0TwN4Za4uvh1480rRtNhClm0LbHDGMKo+/06UnhH9qhvFHizR1k8G6hp3gvxBfSaXoniia4jaO8uE3/AHoR86I/lvsY9dvbNdL+1h8v7PHjcj/n0X/0alAFEftORMAR8NviEysuQy6Cf/i6d/w01F/0TT4if+CE/wDxdeh634s0rwL4Jk1zWrpbHTLK1WWe4YM21dvoOTXN3/x88FaXqlrYXGseXLcWS34l8h/JjjeJpU3ybdqOyI5CE5O2gDA/4aai/wCiafET/wAEJ/8Ai6P+Gmov+iafET/wQn/4uu08H/Fbw546uI4NJupJboxySSW8sDxywhHCMJVI+Q5bgHr2rrZJooVUyMsYYhRuOOT2oA8e/wCGmov+iafET/wQn/4uj/hpqL/omnxE/wDBCf8A4uvYnmijYB5FUt0VjVE6/pv9qx6b/aFv/aEkDXCW3nL5jRBtpcD0yQM0AeVf8NNR/wDRNPiH/wCCJv8A4uum+Gfxl074naprem2+j61oeo6Stu9xaa5ZfZpdkysUZRuOR8jV36SJMgdCHU9GWvJ/Av8Aych8Uf8AsG6L/wCg3NAHrq9KWiigAooooAa3UUq9KRuopV6UALRRRQAUUUUAFFFFABRRRQBleItFTxFod9pclzc2iXkDwtPZymKaMMMZRx90+9YXgv4T+Efh9Yw2ug+H7DTo45GlDxwhpPMb7z7zzk12VFADdtZniT/kB3v/AFyatWszxJ/yA77/AK4tQBdt/wDUp/uivz+/4LD/AA+8R+PPhP4ETw7omoa5Nba1I00Om2zzuim3b5iEBwPlr9Abf/Up/uikuDtt5G9FNAH4sf8ABJv4deKtF/ah0zXr/wAN6tZ6JLo99FHqU9lIluz8Lt3lcZypH4V+1i14l+xj837N/hIn/p8/9LJq9uXpQAm2l20tFAFOXTLS4k3yW0Lyf3pIwTWTqPgHw9q10lzeaTazzoAFkaPpiuiooA45vhT4UVdseh2sJ5YSIuGQ+orzKP4Z23/DQE9t/bevbV8Mxy+b/aT7/wDj8f5d3932r36vNE/5OPuP+xUj/wDSx6AOX+Mt9rnwc8Gf2roGvXs0kl0kTx6oRcryrfdZvudK84+D/wAd/HOueLU0MSWeotevI0K3xYLE3zOfnC5xhWCivSf2u/8AklcX/YRh/wDQXrwP9nFv+Lx6CvDNmZv/ACC9AH1D4i1LUNY0u48OeJPCA1WLUYHt54re6h8i5THzKiyMrt8p5+WvFpLjxD8PfGngSz8Vzy2/hbStYZ9Gn1KVrjUZw9ncRm3do9wkZGdMNnJXOfu1ynij9oLxg3i77R9otfN0q4uUtW+yqVClmRlb5uflWvoZvDUfx6+Bdhaa/J5V1qlpHcfarRNrwTAh0dPTDKv1GR3oA6ey+KXh66YrLcTacu3Ik1G3e1RvYM4AJ9qXxT4Q8I/Fnw/Fa6zaWWvaaW82FmIZQR/EjDpXLeC/G19HrMXgn4hw2qeJFBax1Daq2usIv/LSIdEkx96LqOSMrzXW3vwv8Laldy3NzottJPIdzt8wz+ANAHnsOpeJ/gQN+uate+NvAiJsF1FZNPqOmY4jV1jy9yh+6Xxv3bfl25I9I8J/ELw748037doOs2eo2yv5UhgmVmhfAJjdeqONy5RsEVVb4YaTby+Zpkl5om4YddNnaIP9a848Zfsa/Df4gaouoa7aX97fKvlif7a8TY3bv4NueT1oA73wdot7p/xF8e3s9q0VnfzWTW8p+7KEtgjY+jDFd5Xg+k/CT4hfDPw/rUvhv4h6h4t1CVQ1pY+Kv30SbeiI4+ZOP97OBVv4d/FLxVr1zdaPqOmw6f4mtx5s+la0RaTJG33XjMe9Jk5XLIflJ2nmgD22q9xaxXULxTRrLE4wyOMqfwrmP7Q8ZqVJ0jR2XPzKt/Jux7fuqlbxXqqsw/4RPVG2/wASy2+D/wCRaAOY1j4ST6Dfyat8PLuz8JX8rma8shaK1nqbfeCyqPuEngyoN+PWrfhn4meVcWeheNxY+GfF8xCR2v2r/Rr9vm+azd9plHHK7Q691wVJ0U+KWirdR29wt/Zys+x2uLCZYoyOu6XbsA/2t2KZ4k1DwL410mXTtW1DRtSs5gYjG91GW59DuyD06c0AUvhD/wAfXjz/ALGa6/8AQIq9Frxv7Zr/AMDbee71m+vfF3gzP+vgsmm1HTxwqb0iVnugfus+3fuwSMbmHoPhLxxoXjzTTf8Ah/VbTVbVH8qRrWYOYXwCUdeqONy5RsEUAdHRSZFLQAUUUUAFFFFACN0r8Df+Cmmuaiv7ZXju0+33K2sclo8cAnbYjfZouQucA1++TdK/AD/gpvGzftp/EEhTjfa9v+naKgD9Cv8Aglv4Z034k/sT3mjeKbRde0u+1u8iuLW+PmpIo8psNn3r6N0v9jv4J6NqVrqNh8M/D1re2syTwTxWmHjdGyrD3BrwX/gj6pX9kfBBH/E/vPvf7sVfcdAHBfFTEcPhUZXjX7MfN9WrulYMuRWL4quNG03SX1LXpbe20/TT9se4uWASEp82/J9Kd4V8RW/izw5p+sWiXEdrfQrNGt1A0Mu09NyP8yn2NAG3RRRQAUUUUAFFFFABVC/tY7xRbyjfFIro6+oIq/Vab/j4g+p/9BoA8c8UD4WaB4k0bQdVi2X+l6bHDB+7meKztEdZE86QfIg3W6kFzzsrlvgZ8CfCeg6xL4in8QaX4juNVWG50dbB3ihhtYxNsaJHlcn5bh/u4Qfwqtdd8Uvgzqvj7xlDfWt7p2mafJaNaXsqxyNc3MJVw0Lpu8uRPnyN6kp82PvV57pP7IerWqaRbvrGkWiWlpawNqGn2LxXsLQWz24S3fd8kMm7e6+rP13bqAPU2tfhzrlvY/DiSK1vdP02C3vreAyb4Y/JnCRDfu++HVflpNQ+Dvw0k8TXUt7a2x1a+lF19mnvn+V/NWYvFEX2pueIM2wDO3muD8M/sx6tpPjDwv4ie68P2r6HCLf+zdMsHitLsbh++lXdzNt5D87W9etM8bfA7WfiJ8eNb1UQ2FhpUdrpRj1Se1L3e+F53aO3lDfIp3IH/wBlqANrSfg78ILe61GC200QQWs9tfNqTajJ5Il/eoiJL5vy4V5UKDAw+K6OD4F/DW8ks7O0sU8/R3idVs7+VJIcRRoqPsfOxkhi+U8HYK87uv2S57K40G50u50N4tKgs0Okahp7Pp91JHDNG8kqBuWzNvB55HPrXSfBv9nWf4W/E3xV4pn1KPUDqxm8qQPN5mJbjztroW2YT7q7ew7UAe7qu2nUi9KWgBjSBerAVxmlTRzfFXXNjh9ul2oO09P3k1WfHHwx8PfEW3gg1+ykvI4c+XsupYSM9f8AVstc38NvBelfDnxdrXh/QopLXShZ21ysEszzMHZ5VJ3uzP0VepoA9QbpXivirSYviP8AH2w8P6uPtHh/w7pKa0NOk/1VzeSTPHE7r/GsSxOQp43Orfwiva68s+J3gnXf+Ek07xx4NWGXxNpdvJaS6dcvsh1S0dg7Qs/8Dqyhkfscg8NQB6csYVdoAwOK+WP2g/iZpX7Ivjaz8Z2Wi3N3pvia3uLfUdI0qH/XXkQR4bllHCfLuR3/ANpM16XF+0FMtttufhz43g1UDa1jHpay/P6LMH8sj33VH4R8D654+8SXHi74haTZ2W+xk07TfDu4XC2ttIQ0rTN90zPtVWC8Koxk80AfL+i/Gj/hONVi8R+O7jU/7Tk+a10iLTpkhs0LfKiZRs/7/U+1eqeHtesvEFtdW8FlrLW90Nkn9maJumkX/auJnd/++cYq5ffDbWf2d7qWTw3ctqXw+mk3/wBkap/pMWn5P3F38iP0+bjpXVaPB8MPHn2ePWfC+l6LqksvkoyxLCskgOdqTJtyf9lsN7UAecyaJJHrX2G+8IeJvEWoTxsY11x1+y6zbL/yymBfYJ0GMPt5HPPzY6vwr4ou/CuhXUuifD7U7/wjtaK60lhbLeaeR8rxbd+ZU/2W5x0JFdzqn7OXh+6sjbafrPiXRtkiyQta6zcP5LhtylVkZh1/wriNO8G+PLHXr2Oy8dTReM7ODmHVNNge21iBW+WTcio2f4d3VD6rQBZ0zXtQ1izi8Kz6BfT2citd+HtWj1GDzUVP4Fff/rIs9/vJ171heKtc122vZtW1nw3JpetaLGn9qXVvfxxM8P8ABfxeqp829f7uVOeKsyeFfHniS1vL7w3c+Hxq9vOkt7pksc1i8d0q7g+35xHJ93514cM2chq5Y/ErxH8b2+z614FuNLvfCkv/ABMUsL63m33AzlXR2TfaNtJPZv8AgFAFzSPFGqaXr934k8YeE7CHTrmBJfs9xeI1vbxyf8v0S7fvSttEijkfJ/tVz03gzxFfeK5fFGm6FJonhbTZxdXehx33nQxzvtIm8ko26RR88oXttA5Bpv8Awsay8dW8HhjXNL13SHt3V/Dlrfaa7ujlfnmcpu3xwq25Fbh0Zc/7Op4O+Plj4N8v4Oadrlj/AMJMfltPEGpPsiS2cMzzzeZtJnHOIz8zZXtk0Ab3jD4lfEzxRdp4XsdC0LxKIP32oT6PfPsKbdyK6MvK9GKK2X4Ve9dH4a+IEvhPw9dajGulzatcsrTpqEk0Wo3Vx0EXksgOc/KEXgVf8C6V4f8A2dtEH9l3y6p4KuGae71NZRcTQXbn5pHZfvJK3/fDHA4PFnWFtvEmprqHiHQv7b1uSNv7G8Msg32sbcedM3SN27v/AAL8q5OcgGBN8PvFmgm/8bXupeHvC/ie4TcbmBZXhgTqLfy/lWT3P3mP4Vk6n4w+JerW9q/jqXSPCHhO4Qn7XHa3Gy5Xb8v2lvNDWoP9zv0Ldq7J/hZL4Qt/+Em1XxbJbS2I8yOxvv8AS9Oss/wRI/z5/h37t3Py46UyC+8W+KNQt9Q8beFrg+G4X32ljpjCXzNvSa7hO1/cRrvA/i56AFHQvC3jr4k6XYXS6poGl+D4dp0/TItLl23iD7kkq+dwndU/E+lehP4T8cXUXl3PifSdnTy49G4/8elasXS49JbULp/A3iKLSL1CZbnRLpW+zt9YXw0P1TH0qhdftEJb2rRT6alhco7RSareT7NHXH8S3e3D/wC5jOeKALWpfB6WNXvNQ8Rafaogy8qaRDb7f+Bhq+CP22vgf4i+LXi7wTcfC251LWX0jVEsrzV4Lfyktp5nXypUcckJt5Pavumx8F638Ur8XWtX9z/Y0Z3RySJ5Xnf9cYf4E/6aPlz229a9c0Lw/p/h3TIrHT7aO0toR8saL+p9T70AeXfsszHTfhDpfha8ZV1zwtu0bU48/MbiM4MuOu2XiRT3D16prGrWmh6bdX99PHa2VtG0s08rbURAMkk1wnjb4Laf4m8QjxHpWp6h4R8UrGIX1fR5FUzoPupPEwKTAdt65HY1kL8Ap/EF1byeOvGuteNbK3kWWPSrlIrSzdx0MsUKr530fK+1AHB/BT4feN9W8Hv4o0nxdd6Bp/iDUbzWLbw/d2abLeCed5I/m27wSrK2Dn71egXWh+NrBd81zrN6g6tpl/bFvwWWJf5163HGkaBEAVF4Cr2qQjNAHiOk+JLDUGkjvfGPizw9cRvsaDWrVLZvwZosEe4aut0/wudWTfZfELVrxf70E9s/8oq7yW3jmj2yRq6/3WGa5fVPhf4X1R/Ok0eG3uP+e9nm3l/76j2mgCv/AMIDqH/Q6a9/33b/APxqj/hX+o/9Dpr352//AMapv/CA6jpu3+yPFmqWaj/llebLxP8Ax9d3/j1E03jzS48pBo3iBR/dd7Rz/wChigBs3w5vLqNop/F+tzxONrxyfZypH/fquXm+AM2nieXw3468R+HLqRfu2zwvbbvXyWi2flitm++NWneG43HimxuvDjouXaV45k/4DsYsf++ar2H7R3gPVrhbfTdXbUbpkVxBBayq5U/d++q0Ac/qEfirwdbxSeItU8RXlpuVJNT0IxzBP9t4fK3qvrt34rpNO8L2ni6wc2njvVNWs3G2RFmt3Uj0ZfK/Q1dk8ceJtSbbo/gm62N0n1a7jtUP4Lvb9K5/VPhh4g8TXq373Ol+FdUxxf6LHI10P95iVV/+BqRQBWk+D3iHwhsk8K+MNbm09ctJot3PFsOf+eMpi/d/7v3fpWN/wjdl40jfVrDxzrvhfxdZI0TrqRhimjw33JomRfMj3DhundTWtf8Agjx7o67tT8R6l4308N88djJFpl0if7qKEk/76Sq0Pw/+FnjC/itXtWs/FMQDwyamXXU4z6r52S49vmWgDz9fFll4b1bVrmz8Xaymq25Mus+HrGRLhrobV/0uzxE2/wBwvXodprt/D+sal8QNKivdA/4THUdNuBujudTa0tEdf9103/8Ajtamv6bLo9rFZ+KLURWVmPNsvFmiw+S9q4/imRP9X77cowzkLXnh+Ll/4Jv1k0YW9/pupzMp1C0j32N67f8ALazRfvz8HfCvB5YN1oA2/GHgmTwvpcuqazq1r4etYwXdvOWaZ8dlSOJNx/OvgD9lf4ReLPhr+1Zq/wATtZs74eBdOum1F9RvoHRby2uZnhS4Xt+7Z97ei5NffXgrwE/xo1ODxBq122paAwWUTtIr/bG/uLt4CL/FtwM8DPzGvohtMtJbBrJ7aF7No/KNuyAxlMbduOm3HagCa2uYryCOaGRZonUMro25SPUGvHv2hb6LXLjwZ4KssT69quvWd2iDk21tayieac/3V2ps3ergUsf7N9vobPD4S8Z+KvBulu5f+ytNvVe1jz2iSZH8tf8AZTAFdb8PfhDoXw7lvbuz+26lrGobftmr6vdPc3c2Oil2+6v+wuF9qAO5r5r+IWm/tHSfHbwzP4Q1Hw9D4IjsW/tZL6NvJdvObCqu7f5mzbyuBX0rt+WjbQBGu7y/nxuxztrxn9n21N94P8f2wO0zeK9aQN6ZmYV7U/3a+WvhN8VtW8IDxzp9n8O/FPiSBfFuqML7SordoWzN0XfKrcfSgDgvhlqWo+JNP+Fnwdfwvrun+IPBOurd6zeXNi6WUVvatNslSY/I/nb0wo55OelfQ37WP/Ju/jf/AK80/wDRqVSX4/a/uz/wpjx3u/642f8A8kV5x+0d8adb1z4HeLrOb4VeM9NhktFVri7itPLT96nJ2zE0AfRutaPL4g8BX2mwMqT3mmvbxtJ0UvEVBP5145ZfAPXl1HTtOvZtKvPDEn9mXOoh9/2gzWlr5JiQbcNG+1Dk4IG4c7vl2bL4969DZ28Y+D3jpwI1AZYbTB4/6+Kl/wCGgPEH/RG/HX/fm0/+SKAMTwJ8AvEvgXx5YarpesW1lpM0zXGpWcDvgj99tgVSvzjDw/Ozbso5/jrv/FXwyudc+IGgeKYtUdxpxCPpN+vm2hGf9dEoxsnH8LnP0rm/+GgPEH/RG/HX/fm0/wDkij/hoDxB/wBEb8df9+bT/wCSKAJ/it8Hb3x74kutbs2slvYfD8+m6c16rOILmR/ml29Publzz1rxKD9jHxPbaQEjvNJi1STTr/T0vo5XE1mk1yk0SRuqD5dqvEcBcByQP4a9m/4aA8Qf9Eb8df8Afm0/+SKP+GgPEH/RG/HX/fm0/wDkigDo/gb8Prr4Y/D+00O6+WdZppjGtz9oSLe+7YjbE+X2CgCsjwL/AMnIfFH/ALBui/8AoNzVP/hoDxB/0Rvx1/35tP8A5IrJ+CHii58WfHD4o6hd+HtU8NT/AGHR4/sOrLGs2Alx83yOwx+NAH0BRRRQAUUUUANbqKVelI3UUq9KAFooooAKKKKACiiigAooooAjkkWFGd2CqvJZu1VP7bsP+f63/wC/q1ceMSKVYBlPUGq/9l2v/PrD/wB+xQBH/bdh/wA/1v8A9/VrP1/WLGbRb5EvLdm8pvlWQVqf2Xa/8+sP/fsVna/p9tHot6620KsIm+bYKALUOtWCxoDeQKcD/loKjvNV0+4tZYhfW6+YhXd5i9xU0On2jQxZtoW4+95a1L/Zdr/z6w/9+xQB5d+zj4Yf4V/Bfw54W1a+tW1DTkmSZ1mUhszO+fyavTf7b08f8v1v/wB/BUn9l2n/AD6Qf9+xR/Zdr/z6w/8AfsUARf25p/8Az/W//fwUv9uafux9tt/+/gqQaZa/8+sP/fsUf2Xa/wDPrD/37FADP7b0/wD5/bf/AL+Ck/tzT9ufttv/AN/BUi6Xar/y6w/9+xR/Zdr/AM+sP/fsUAM/tvT/APn9t/8Av4K81j1Sz/4aKuJvtUPlf8IrGu/zFx/x+PXpv9l2v/PrD/37FebR2Nt/w0ZcJ9ni2/8ACKRnbsX/AJ/HoAvfFrwfp3xU8Kroz63DYKtwlx5yFX+6G46+9eT/AAN+DdhoviJPEja/Gz6bfXdoIGRV8xU3xZ3bv+BV9JPpdpIuDbQ/9+xVKx8M6XpccyW1jDAssjyvtQcuxyTQB+eHiRvM8Rauc7g17cfj87V9w/A/ULSP4S+F42uYVkFkmVZ1Br4d8T4j8Tavwuxb2ZQq/wC+1fU1v48i+Ff7P/g7VhpEGpvNDFCUlOz7ys2c7f8AZoA77xBa+F/jL4TnstRfyoFunSGeOcR3EE0MmBNE/wDCwZdwNYHgP4h6h4T1ZfCHjnUrKa6+VNK8QLKqRaruJ/dlWbKzr8u5ejZBU9QOF+BPxoj17xJb+GToVuiXs9xdfaWk3Mu9ml2bdvbdtrt/jZ8MdH+JmraH4a1CM29rf2V+n2i1AWW3fZFtkRv4XXsaAPWf7b0//n9t/wDv4Kb/AG5p3/P7b/8AfwV5d8JfGlxceINZ8A+KrWzj8V6BHFJ9qgjVIdStnB2TxK3OQFVXUZCFgNxrsvAd8PE/h1b28tbdZvtFzD8kWBtSZ0H/AI6tAD/EXxI8OeFZtIi1PV7e2fVb5NOs1znzZ3DMicdOEbk8cVjfEj4f+DPirZWtv4gaKaWyk86zu4LryprSTHEqOD94fKfmyMqOK/C/9uzxRrNp+1t8S7aHV72C3tNbdoIY7h1SIhFwUGfl6npX6w/8Ew5pda/Yz8FXmoSPfXkk9/vnuG3ucXkyrljz0UUAej+C/ihqHgnxBb+C/H9/b3MsnGk+KUKpbaiM/LDL83yXIXbx91+qkncB67/bWn/MPt1vlev7xaw/H3w90n4h+D9V8PapAn2W+haNpY0CyRHHyyIf4XU/MG7VyngrxddaHqsHg/xtBa2muDK6dqccIitdYRR1iyTtmVeXi7dQSM4APRZNU02aNo5Lu1dGGCrSKQaqeT4f2EeXp21uvCVpixtt2fs8WT1+QU77DB/z7x/98CgDim+HfgZutpbN8+7/AI+n+9/33WJqnwv0Kw8P6la+CNUh8E6pdo22804rtaY/deVP48H6H3r09rC2brbxN/wAU06baf8APtD6/wCrFAHzDZePvFPwz1WDQ/GfiG8hdtqJ44nMU2iT5+6Hi3q8Eh2n737sf3+a9h0vxN4hls7eS11Pw1rlpJGJY9Q+1tF5yEZDKqKy4/Gux1DwzpOrafLYX2l2d5ZTJslt57dHR1/ulSMEV5FqnwF1fwhqV5q/w014aOJPnPhfU4RNpLfxMkKjDW5dhy43hdxwlAHZ3Hj/AFnR1/03Q7fUWc/Iui3yPtX1fzNmPwzUf/C2rjfs/wCEQ1jp97z7T/49XLW3xgPhOO2tfij4VXwbcyMsX9qW3+l6S7sTtUXCruQAYy0yIK9U0mTRdc02DUNNNnf2FyivDc22x4pUPQqV4IoAwV+Lnhldqz3k1tKB88bWkxKn+7lUI/I1csfif4Wvs7dbt4Npx/pW63z9PMC7vwroRp9p/wA+8X/fC1WuvD+majt+1abaXG37vnQK+PzFABZeINM1SGaW01G0uoof9Y8E6uE+pHSvIvjp8LNM+LVn4eOnvoaXlj4i0/Vrqe7CZmt4JdzpkKxLMvygHivWI/CujW6OkOlWUSP99Ut0Ab68Vyeg/CvwtqGnrPcaPE8zyPubzH/vt/tUAdVp8miaPa+RYmwsrfOfLt9iJuPsKt/23p7cfbbfn/poK5S5+EPh9VP9mxzaNcEYNxZyHft9Pn3CvMfjnpj/AA28Ey3GneI9du/EupSppuj2zSROHupm2o7JsyY0Lb3x0VWNABe6tB8bPjo9hLNE3gXwO/79ZJP3V/qxGfKdSRzbrsdTyred7V9BWsiSwoyMHRhwynIrx7wL+z7a+EfDthp0Orapp03liW7WxnTy3uXGZXyUy3z5xntXrOk2P9l6bb2fnS3HkoE82Y7nf3OKAL9FFFABRRRQAUUUUANf7tfJF98YPiI3ir4hLpr6xrEGkSX9vDY2uh7YrYI8SwvDcbT50nzvlOThfu+v1wy7qzYNLt9NSU2dtHD5krXEixKB5jn7xPuaAPiuz8SfGHxFpCatrupeLvD9/Jp1rK9ppuh71xBqUqTPt8rIkaDyn2bRvDNhcLgdNoHj745eI/GF7pc5uNIsDrMNuZv7EdjBbbpssjvEqEGNIstl9rP+FfXUckci8HdT1UdqAPEvgnqXxD1C4nh8YX9zcLe6XFexTS6atr9jmM0qNCNv3jsEbfNz+BrxD4b6t8ZPhrqvh/Rls9SvtIuEvNSu57nTWaa9meW4aQO4TbG6hIdm8oDv79vt3y/ejy/egD4f8P8AxE+NmsW9nrd+viSyurdNVt7SzXRNy3jeTbzW/wBoXylx/wAtlD7UGUA6tz2Ov/FD4veI59Z1LwfYapa6dbWmoXdhZ6loZhe7ZPsywo29d6/fuHC9X2V9X+X70eX70AfLvwp+I/xT1bxd4BtvEUeoT6dqEF4175WivbqoR5PKkuXkiTYdqou1dh3YO1g3H1IvSm+X70+gBG6Vxunf8lW1r/sFWv8A6Nmrsm6Vxunf8lW1r/sFWv8A6NmoA7OkIzS0UAN20u2m+Z82KcvSgCvdWsV3A8M8ayxSKUdHXIYHsa8J8dfDf/hD5ri8g+fw/MR5nnR+clv/AAhLhOrw+jj54/Xb09/qKaFJo2R1DowwVboaAPFPDOu61ol99g0yYTTCMSHw5qdzuLp/fsbr+NPZs7e+2ujutQ0n4nRi3tLq40HxXpj+bBHcp5dzbSf7SH/WRt0OMqw71k+LvBUPhe1nDWkt54QklaaSG3L/AGvR3P8Ay2tmXkID8xRfu9uPlrC8deIINJ0W0t/EcMXiaG+KpoXiCy4mEh6GYpzGF6mRflx97FAB4lvtU8far/ZWgI3hz4hWcYTWJ1f9zHbdlY/xiXny2+8nzH+EhuQ8QaLp3xNt01TQ7f8A4RtfBMTW93HHPiW9Qcvb5/5aQZXcGbKyHj+9UeqeDfFMLPbaPeN4qFrufWPEkDbL25Lr81m2ziSMrtXcvMYVcZbNTapq1t+0Fd6fb+B3j8NaR4YjL6xq8kYT7PMn/MOaLuFxufdwPlI5oA5TxJ4u1TxhDFrNi0OlfEFxGmj6gh22gi/5ZWfP/Ly/zF42+ZNx7LivSfA/gPwr8Tvg7qFxr1uNP16Sd5tVuL4K1zpV/D/tv/zy7MeCDno1cz4dvtB0m6l1jxHo66b4bf8A0f8AsOcbngkf5f7RQdX+0f3l5G5WOCz1xvjDTfE1nrC+NrjS7vVPhFDLHbaxpfnM93f20bNtuLgK22RIf9nJcfK+QooAd8N9H0f44aw2h6h4X0rRpbEeSmoxQG3tNVQf8vlug272ZeidE+Y8/wAPpMvw00T4HXMsdx4k8TaTo186fY9Vt9TeaV5scW0qur7yf4Pl+bp169Z8Rm8L+Kvh/o3inQLqG4Xy1/spdMK5vUbpDFt6PxlWH3CvpmuN0HxdZ+K7yO38bwTeJfEF5C1vpmmWozDCv8SyfdSG6GPnJwfl+SgDotP+FvxF1iS01jVPGyyJbSLLYaRrWlxXCwqOjzNGybpvfoPrzV7/AIWN8RbPUf7K0/TvDvji9h2rcNpsk1lHD/eZ5X3oG/2M5puiaXr9/qVr4Y+IWqNDb483ToNPmZEv4l/5Z3E3DPIvdVwGHPPNdUviJ76T+wvAVlbJBCdlxqvlhbK0/vKgH+tk9hwO57UAeS+MrrUdWv7NfiR8PL3xDqMxxZaPoF3BNEnDbm+8khC93fC/N92sl76ytVQ+JZde0mBAiweGdc8P3N7pNou35V3ohy67fv78DstfSnhXwXZ+FY5ZFaS91K5bfdaldHfNM3uey+ijgVq6tqlhoti9zqN3b2Vqg+aW5kCIPxNAHzv4f+LUWiu02h6xp1xZqNz6YuorcQldv/LJH2zwn/Z2utdXefta/DrQ5vDlprmtw6PqGuXyafaWsp3ZlYf3x8uztubH3hTfFnxg8E3EbHTvDo8XPnCTJZItsW/uiaQBX+ibz7V8p/tI/sSeNP2qda8Eaxb6HovgHT7W/wDJurax+S4Fm/zNO3yrl124C4H3qAPqPSbTV/2hr291ifX7/RPh9DcyWunafo8rW9xqXluUe4mmHzCNmU7ETHC5J+ar2sfA3UPCcB1T4d+JNX03WIPnGnanfyXun3uP+WcqSMSm7++jBh79KX9mK8g8O+Bbf4dXkqReIfByf2Zc2rN+9khQ7Ybnb3SRNrbvXcO1el+LvFmleCvD95rWtXkdjp1rH5kksjfoPUnoB3oAy/hn8QrX4jeBtP8AEKRNpzThkuLO4P7y1nRiksT+6OrL+FWtY+JHhjQQBfa9YxP/AM8klV3P/AFya8Q+EH7OOj+IvDr+LPE1pq1hr2v311rD2H9ozIlqs8zSJEYd2wMEK5+Xrur062+B+nafIr2Wua7ZbeiwXe0f+g0AXbf4rW+rLnR9B17VF7SLYPbof+BTbKVte8cahJstfDNjpiH/AJa6jqO8j/gEa/8As1Sf8K91KNcReNNdj/3nif8AmlSr4P12Ff3fjPUP+2tvbv8A+yUAVT4b8b6of9N8WWunRN1j0rThu/77kZv/AEGnw/CmwkU/2lqutayW6/bL99v/AHym0VZj8M+KI1/5HGRz/wBNNOh/9lUUf2D4uVuPFMDL/dbS0/8Ai6ALWlfDrwzosyy2mh2MU46TNCHf/vo81p6p4e0zXLfytQsLe+i/uzxh/wCdYEmk+NVX914h01m/6baa39JaY1n4/jHyaloEp/6aWky/yegBZ/hXo0at/Z0l/oz9m027eLH/AAHlf0qJfDXi7SWxp/imO/iHSHV7IOf++42Q/pSxr8RY8738My/7Ki4T/wCKqpqGv+OdHt5Lq/tPDUFpCN0k738saqPxSgC6niHxfp+4X/heG9QD/X6VfAk/8AkVf/QjXHePPiv4DZ4dL8UaVef2q5zb6fdWD+du6blkHCD/AG9wA9ajPjf4neM42Xw/4a06w0vO1tXub9g8y9/s8Ri/8ffj0DVk6XrHibS5J9B8LeDoL/VN3/Ey1y51VbhYzn5t7uu6ST0ToO+KAMW9s9bmmhhs/EUHiH7SGe18Dtd/aLdE9bi7Hz7P97K9lVq4/wD4R+LWr54biMaDZM7Q3a3KN/Y124b5razlTi2Ct1k4Ln8a6a18Lv4yW60ux8CTf2X5zf2x4giu7dbvUpM/NHFKNpx/CzDG3oK7K10HU9DhW00fQ/FWnWCpsFjJcW15bKv91UkdsCgDkPDfj+b4M6o0Nzb3H/CMts8+1++bNT92ZCq4lT+86fVgD1+ldO1K11ewgvLOeO6tZ0EkcsR3K6t0INfOOueD9ameV4PC986yBlkgjtUhif8A2tnmsgP+0qg18LeMP2lviH8GP2qIfhboV3faD4bE32V9NndSsM13CFa4XrgIz71XOAQ3rQB+mPif9oPw7oniGfQNOs9Y8Wazb/8AHxa+HtPe7W29pZR+7Rv9ktu9queBfjp4b8c6zLooXUNB8QRrv/snXrN7K4df70avxIPdCa6HwJ4F0j4d+F7LQdFthb2lpGq7m5eZv4pHbq7seSx6muU/aG8L2etfDDW9RcLb6rols+q6bqK/LLazxLvVkbqM7dp9VYigD1Dd8tM85N+zcN+M7c81k+EdYk8ReFdG1SaLyJb6zhuXi/uM6KxH614H8Rv2WvF/jj47eFvG9l8Vdc0DTdHtGhltLMqJbljMz7Dxs8vadvzAmgD6UPzK1eQfs1/8gHxr/wBjhq3/AKPr1xVKx4JzgferyT9mv/kA+Nf+xw1b/wBHUAN+Hfx+X4g/F7xH4Mj8Mato8Ok2f2tNQ1WPyftn78wkxxH5tm5Dhz97txU37WHy/s7+Nz/05p/6NSqmm6HqUf7WWtau9jONKfwjbWyXmz90ZBduxTd67ecVb/ax/wCTdvG3/Xmn/o1KAOnuviR4d8N614b8N6lqS22s6xA72dsVY71ji3uxbGFAAPWuYtf2nvh7eWMt3DrEzxRXHkPmxmVgmN32jaUz5G3Leb9zA60/4ifC+8+IWp+Ary3uYbaz0o3TXbNnzSk9jLAuz/aDShvmrz7TP2a9f1TRbqPxINCk1JbPTtDsp7ZXcW9na+YpuRuVf3zrK/ychcL8xoA9+8O+JtO8Vac9/p05msxNLCJtrKjGN2RipP3hlT83Q1qR3UMkKypKjRNyHVsqfxry/wCB/wAPfEnw90XVtL1vUVvbcyKLG3a4e4REA2lhuUbFbj92vA/Go/DPwTuNJ8N69p91q6O2sTJM1nFaqdPsSOot4HzhT/FnqeeKAPV1mjZd4ddg/izVG+1zTrCznu7m+toLW3iaaWWSZVREH3nY9gPWuOuvhvcQ/CfWvCdncwNcXlpcwxzpAtqgaRWA+WP7v3uorwzxp+yh4k8ReMHisZNA0/weujz6ZHbxxYkZZbN4cSDZl1WVt/L8/wB3I3UAfVVnqVpqEMMltcxXEc0ayxtGwIdD0Ye1eX+Bf+TkPij/ANg3Rf8A0G5rzn4T/s3+LvBvxa0jxLqt5YppVrZIi2mn3bAQOLRbf7OqeUN8O4M4y4GW+5nmvRvAv/JyHxR/7Bui/wDoNzQB69RRRQAUUUUANbqKVelI3UUq9KAFooooAKKKKACiiigAooooAKKKKACszxJ/yA77/ri1adZniT/kB33/AFxagC7B/qU/3RU1RQ/6lP8AdFS0AFFFFABRRRQAUUUUAFeaJ/ycfcf9ipH/AOlj16XXmUf/ACcdcf8AYqRf+lktAHpi9KR/u0q9KG6UAfmv4owvibV9nyt9tuFP93/WtX2B4V+HOmfFD4F+D9M1SSdLeG3jmX7O4U7grD+tdJ4l8O6UPiB4Rj/syz2TfbTIPs6fP+6U88c13trbRWdvHBBGkMKLtSONdoA9hQB4f8F/gXo3h++TxEj38d/Z3d1BHFM42FA7op24/u11viTxJpP/AAs/wkf7Ss9sEF/5j/aE+RtsQweeK9J214h4m+CXhKb4kaDD/YG6zv47ya8ZXfY7jYy7vm45Y0AV/wBqI6bF4J0zX7S8Nh4ltbqOXRdas0Dsrj53j3Z/1cqK6HOR8+dpIWuY+F/7SFppvhOz04eGdZ1nxE7zXU2maLGk21pJGkKozsgIXd972rY/au0u20P4X6Dp9nEILO3vUijiX7oVY32ivIv2Yf8Aksekjnb5Nx8v/bJqAPyo/bM8SWPi79qD4j6zpxlNne6q8sfnxNE+No+8jgEGv1v/AOCXPizQ7f8AY58GabJrOnx6hDJfySWjXSCVFN5MdxTOR1r8of29P+Twvip/2GX/APQVr7Y/Zj0PTof2E/AWpx6dapqVxrd7FNeRwIs8qCV9qGRVyR/sk0AfqDY+INN1VillqFreMBkrbzo5/Q1S8XeD9N8a6O+nanBviJEkcsZ2yQSD7skbdUcdmFfE3wL8ZHwP47TUho2q69/osiNa6PAs1xtO3c+Cy5C7ea+3fCvijS/GWh2mr6PepfWFymY5k3L+BB5U+x5FAHmH/CVav8A7Ux+MtT1LxP4PBXyvEzWvnXdq5/5Z3aQrlwW+7IiDqqsv8R9c0nVbPXNNt7+wnju7K5RZIZ4jlHU9xVtlDds15VqHhi9+E2oXmveFLSW90C4la41Xw7Budw2B++shuwjfL80P3X427DneAesUVi+HPEmn+LNJt9T0m8jvrGddySwtx9PY+oPIraoAKKKKAKl7YwahayWtzBHcW8ylJIplDo6nqGB6ivI9Y/Zo0hdSnvPCXiDX/h2bli93F4aukihuW/hJSRHVNvzYCBRzXs9FAHh19Y/G3wLxo93oPxHsyfJhttU3aTcQIPuySzr5omc9DhE5+b2qxcftJad4WWFPHXh3X/Bc0h2QvcWLXcNw4+/se283Cjjl9nDfXHrWpaja6XEkt5cR2sbyRwq8rYUu7qiL9SzKB7mvmf8A4KNfGnxb8Bv2cbjxT4L1BdM1sara2one3SYeW+/eu11K/wANAFv9mH9obR/EXhHxPc+LPiBo8t7D4q1m2tftup28TLZpeOsAVdw+QJt2n0r3/wAKzJNocEkUiyxuXdJIzuUqWOCPUV+LH/BPD4s6/wDED9qbQPCniD+y9W0DWrjUdQvrOfR7QiSZ4ZZmbd5W5Rv+baDgdq/TvSf2Nfhfq1q91Lp2sI8k0jFYPEN/Eg+c/KqCbao/2RxQB9D7hXgfheU/F39obxBrNzKH0HwG66dpkKnfHLfSRq0t2jj+JUd4GTn8DXB/GT4J+F/gb4VTVvAF94o0bxtqU6aZpEdnrtxNLfTSf8sdty0qfdUueAf3X3hV7wf+z/F8K/CUdj/wvDxZbXBke6vlsZbTY95M7PM6x+S7qGkZjt5xQB9VbRSr0r5RXXPj3N8uieL/AA/rlknyJdx+ErhuR0V2Nynz4xkhdvzV9H+CV12PwrpSeJ5bSfxCLdPt0lijJAZtvzbFJJAoA6CiiigAooooAKKKKACkIzS0UAQyW8bHLIC397FNsf8Aj2j+lTP92obH/j2j+lAFiiiigAooooAKKKKAEbpXG6d/yVbWv+wVa/8Ao2auybpXG6d/yVbWv+wVa/8Ao2agDs6RulLSN0oA4bXIp2+J3hmVbXVngjtLtXube7VLGNjs2iaHOXc/wH+H5vWu5XpXE654UudQ+JnhvXY7CyltrC0uoZLyS4lW4haTZtVEHyOrbeS3I2jFdsvSgBaKKjeRVXk7RQBm+INcsvDek3epajMtvZ20ZeSRvT+p9q+dGsdbsvEN7faNayN9vjMmpaYo3f2DZSNu/cr93z5Blnj/AOBfw/N1viTxVc/EHxVb22kSxvZWche0WRN0LuOt9L/0ziO5UX+N+furmn2ENvqGgXiW9/Lpfgayzc6nr0jqk2sOq7pWV+0fHzPxnovFAHBahfXel6VYaF8G7kS+Fbzfb+VFJuZ227pXs3P3dnzM7NxngYLVzuqeE/D2vahp+nfCyzupb3w/AtrrENtMbd71g2/yLzPGUfc5VwS5bZ0ZiLfneJNSv57/AOHds2jJrUCb4ordd9npob5pbRDwkzL1ZuHduFOzNdTrFv4VsLOw0r4eak/h3UGVjeatZxma4kz1hlQ8zTOxOS3zJycrxQBy81jL8RoYJ/EdyupJapJa3XjPT7Vvs8LFdr2K2+5jCn8M3X7uNynp3Hhz406d4Btx4F1a1/tLV2g/4kWl6ZtuP7Sg27dkTdNg9XxhOv3a4/UPF2veEZEs7XSW8IXV4dknhe2dZbrUox968hflY/l5fq+M/wAWK6u3/Zc0PVNBTXfDmsR6b4puSL231qxDPCkvVCis2cDu27L5bfu6UAeY6L8N/Gfwp8Vf2hLb21lpVz512/hfTpNhjtX/ANatvN0SaL5d+wfvE53fLXqV5deBvD/hufXLIRWXgq5ZP7Z09VZJrOf5fKuk2/Pv9WGd3yuG+X5uX8RftKaZNbS+DPGNodP+LGlXSfYYNODTQ3M38FxE6btkbru3I+DhiMGue0PR/FOrXX/CUyaRa+EkW4+zveasVl/saT+ONbRPl+zO38Ur5G/PAbgA9MuLXVPiFFb+HvHU02leG5hv0q/V/Kur9gdyGZx/qXC/wD7/APe+8lafhb47aPouPBy258QeJ9NTy0tPDFv5kNzGvCyq3+rj/h3Kz8H8K4nw34J04+JP+EJ+LN1fa79sQvo/2ybytLkUc+XCibUyv3kV8uF/3c1sXWrNYtYeEdEhjupracLovi+KNYbO1l+75MzDh3/h2ou1x/dNAHca1qHxG8QQrcSXWk/DbQ4vnuZ7hhfXhT2biGP6/PXNW/w9tPGl/Fe6bZXXiO4hb914k8YM1xDH6vb2xwD7MqoP9o103gPSI/FV9cN4wmOpeKdPkAn02YbbS0/uPbxfxI33g7ZbtkYxXrCRhVUAAKtAHK+Hfh/p2i3wv5jLqmqrH5X268ILqvogHyxr7KBXWKtOooA4Tx38H/DfxBvLPUNRtp7fWLEMtrq2n3D2t5CD1VZUIbH+z0rI039nvwxDrljq2rTax4rvrB/Nsm8RajJeJbP/AH0RvkDf7WM16dJIsSM7kKqjJJ7VDZ3kOoWsVxbSxz28g3JJG+5WX1BoAsKtOoooAKKKKACiiigAprNtrJ17xJp3hqxa81K7jtIQcBpDyx9FHVj7CuRVvEnxEjbH2jwn4fL4Dfd1C6T/ANoqf++/pQBra/8AEC3sdS/sjSraTXdcPWztT8sI/vTP0jX/AHuT2U1m2fgeW/aPVvGl9FqVxbyG4js1+Sxs8fd2ofvsP77/AIBa2lt/D/w00F3SKDStPRsuyj5pHPr3d2P1JrnoNH1L4kPHea/byaf4eDFoNDf79yv8L3Pt38v3+bPSgBX1PUfiRKsGjSzaV4YRyJ9TVdst6P7lv/dTP/LXv/D61nQ2qeIs+FPCsQ0zwlYMYb+/tDt80/xW8JHfP35O3TrkrQ8afEbTdY1CXwvpOqfZLO3/AHV7cacvmztg821uic5/hZ+i9PvdNzRj4mvtMFpouj2vgrRoF8qBtQVZbjZ/eWFDsT/gbZ9VoA7OGPSfBuixQobXStLs4wiKxWKONBXJan8YLPyJ59IspdRs4x82pXD/AGSyX/tq/wB//gAauVWx06+1COHTYJviDrkcn7/VtXm3WNmw6n7uwMP7kS59cU2x0K78a6w/2a//ALXlhY+Zr0ka/ZLJh8vlWcPRnH/PRs49T0oA5++8ReN/iJePpdnfSQS3HzbLNGtooYf7zbv3mG7M+M9kqXS/2H/hwPE8HivVrGfVvFDabcabcXk03EizLtZtvYqCyq3Ubq9y8OeGbHwvY/ZrKNhuO+SaQ75Zn7u7HkmtoDigDxDR/EnxF+FNguia14Y1D4gWFo3lWmu6LND9plh/gW4hkZP3ijgujEHrhai1m38cfHRbfR77QLjwL4Klffqn9oTxvqGoRD/l3RI2ZYULY3sxJK5UAZ3V7ntoVaAI4YUhiSONQiKAFUdhU1FFADJPu14D4E/4WH8MZfFGnRfDt9ctLzX77Ure+g1mCJXjmk3r8j8g19A03aKAPKv+FlfETd/ySW6/8H9rXGfGK/8AiZ8Sfhjr/hi2+Fstrc6jAIklm1612Bt6nnH0r6I2ijaKAPJLX4ifES1tYIv+FTXLbEC7v7etOwqX/hZXxE/6JJdf+D60r1bbS7aAPKP+FlfET/okl1/4PrSj/hZXxE/6JJdf+D60r1RmEakk7VFeF/s9/tXeHP2hPGXxE0DR9qS+FdT+yRybv+Py3+75y+29XX/vn1oA6D/hZXxE/wCiSXX/AIPrSj/hZXxE/wCiSXX/AIPrSvVlWl20AeUf8LK+In/RJLr/AMH1pVb4U6P4on+JvjjxX4i8O/8ACNQ6ra6dbWtu17HcufIE29mKcD7617BtpNtADqKKKACiiigBrdRSr0pG6ilXpQAtFFFABRRRQAUUUUAFFFFABRRRQAVl+Jf+QHff9cWrUrM8Sf8AIDvv+uLUAXrf/Up/uipaht/9Sn+6KmoAKKKKACiiigAooooAK80T/k4+4/7FSP8A9LHr0uvMo/8Ak4+5/wCxUj/9LJaAPTF6UN0paKAOV13Sbu88aeF72KPNrZ/avPfP3N8YC/rXUr0pGpV6UALUbKNwOORUlNagDwv9rexuL7wHpv2eCSdkv1JEaMzD5H9K8Y/Zj0u8X4t6VcNbTfZ1huAZ/LbZ/qivXbjO6vtnaGXnFUtL0e00Wz+y2UQgh3u+1f7zsWJ/MmgD88v2uv8AgnT4I8VfFfRPGE3iDWoL/wAc+Lrewv4Q8XlwpJDKzGL5M5/cjGc/er6a8BfsbeFfh/8ABjRfhvZarqs+k6VeTXsNzK6ec7yMxYNhduPm9K9g8TeCtI8YzaNLq1p9rk0e/TU7JvMZfKuEVlV+Dzw7cHjmtyaSOGNnkkESL1ZjtAoA8N/Z/wDg7pGiraeLILu7fUD9pt/Jdl8vaJWT+7noorf8VeC9W8FeJJfFHgMWn2vUpf8AiZeHrycw2+puf+WiH7sM/q+1t43bgW2kdjoK6L4H8PC0TUofslu0kpklmXPzOXb/ANCr4u+K3jzUNU+ImsyWGuXk1kl872irMyrD7p6fxf8AfTUAfWXgf4yWfifUJ9H1fTb7wj4hhbadN1hVjNx820vbvnE0e7gMMH2r0f71fNvgJvDXxM+Fuj6d4m0K88YXtm8hacW7TTWcm5trLNkPHJtxgqdwq5ous+MPhat6mmabr/ijwZZwyTQaXf2Mr6tF93EMVwz7ZUX+EPlzub5+BQB3OueBdY8KaxdeIvAUdqbm6d5NR8P3cn2e0v5G/wCWyuFPkzZ5LhSH53Ddhhr+DPiVp/iy4fTZoZ9G8SW8ebvSb1WWSI5wSp+7InGQyn7pU8ZrnPA/xR1T4qaaL7QLnStJYM8U2k6pE731rKhw6SqrjaQ3BGOKd45+H/jTx1obWD67pelXSus9rqen20yXFrMvSRMuwP8AutlSPlIIoA9Y3Ubq+cda+IPxL+DLWNl4tNjqng21tIo7rx48TPMJj957i3i2hB/tqu3LD5a7/wAM6PeeMNFs9Y0b4l6pqOm3UYlhuIIbRkdT/wBsqAPT91G6uF/4QHX/APof9Z/78Wv/AMaoj8C64rr5njnWJUz80bQ2y719OIsigD5o/a6/bq+Ffww8RJ4B1m91L/hI9J1vR9QvIrewaREhS5huXKv0J8ofd/CuF/bK+Jml/tsfsU6jf/CS2v8AxGLbxBbI9qbfyrj92CXYRk5IHmpXwD/wUmsYtN/bI8e20TSGJGtQDLK8rf8AHtF1ZyTX29/wS3vtL8G/sbeMPGlzpEOo3eiateXa/KolYJbxHYrnpQB8wf8ABNv4O/ELQ/2kvD3jX/hCtXuNB0me9sbq8SHbFDc+TJFsdj02udrelfsHoM/jLUdNjjt4tM0hEkfMsrtc7/mOVAGzbj15qT4N/DHTvhb4Zv7PTbm5uodV1W81yQ3W3ckt1M0zouFHyqXwKw/iv8Srj4c/CvztMijuPEmqXH9laNbSBmSW8mdki34OVTd1btQBwWmeGZ/i/wDHS/W81i913wh4P32xafaEfVm6+W6BTG9uFKnr/rq980zwP4f0e6jurTR7OG7jXat0sC+d/wB99TWN8IPhzb/C3wFpehRyyXdxFEHvLyfa091cNy8sjAfO5Pf2pvxM+Jtr8PbO2ihtpta8Q6g5h0zRLX/XXcmP/HEGMs54AzQBu+LPF+jeB9CuNX1zUYdL063Xc8854+gHUn2HNT+GfEVj4u0Gw1nTJjcaffQrNBKUKb1PQ4PIrzPwZ8Pdd8a69aeMfiRbQwX9sQ2l+F45hcWmlMp/1xf/AJbTn+/91eNqg5J9iRQo4oAdRRRQAUUUUAFFFFABRRRQA1/u1DY/8e0f0qZ/u1DY/wDHtH9KALFFFFABRRRQAUUUUAI3SuN07/kq2tf9gq1/9GzV2TdK43Tv+Sra1/2CrX/0bNQB2dFFNZttADqKrteRLJ5bSKH/ALmeam3UAKTXjvxg8ePcXR8I6RKDdTLuv5ASvlRbd2wv/Bkcs3ZPdlre+MPxRg+GugxmFY7nXNQf7Pp9mz43v/E7eiJ1JrxHw3p8jb5714ryeR2mu5b6Rkty+7cr3Lf3N3zCAcnq+OAADqNLs7H+xYPt8ky+Gr10CW0ULLd6/Jt2oiRdUtlUYVP4l64Xrk/ELxFb6hIt340kFh4U0S5SKLw1ZtmK9vTt+zWjhP8AXMvDlF+QfL12mrmv+MH8K2H22yu7S01nUC0MfirxVthCp/G9vb8EQovzdgfl+9muO8C65DY6zZXPhfw1rXjjxHNHJb6VqWrD7PDCCzNLeTPJt++3zN5ScLhRy1AHdWdx40vrltH0TbpHiHWES61HWLyDzZbaHoNkPSNEG5I1fljuO371ZfjI+FP2YbmO78NXdvN4w1XMt3Y6lcF7jVfm+e4dvmcPuP8ACPm+5j7uJ28J+NZLz+wX8Ur/AMJHqB+0aimgRsiRZX/W3Ny+X2/wpGmw46cZNd74f8D+CfgTo0dwYTeazN8r6hcK11qN/J/dDHLnn+EcL7UAcRomoXnxA8O3VzofhqXVru+k/wBO8Q+KpW0/7K6dkiH75AnZV2f3t3zZrzl9L8V6O7HV/Fep6t8LbmRvtmvaRG1lDZPn53WJPnkhZusm7YDztPNe1638N/EPjDUl8UyRx6ay7Wfwusn7nUEH3Vu3XrIP4ccD7p3Cunj+KXhufQfJa2mN6B5Enh2OHfdo/wB3yjEvT6/cx3xQBy2pfAPwto/gW3j8F6XawiFfP/0YK7agh+Zi0v8AG7feVznn2JryaXx1N8P9Y8/QnbxKt/Bs1HQZ9y2k9su4edvKn7NMm7a8b5+VWwvy1v8A/CG+LPhreKkM82lfD/Up90fhuG4y9u558n7T/wAsVbsq/Jn5dw3A11sdn4Vt7O5ays0tfCd/KE1bTpEaK40q6PC3BX7ybj8rt9Gz1oA4620qw8XMPA3xGvpodLvMf2VpW5oVspT/AKpPO6z7f+WcgbH8O3pXYaD4gufCulX/AIL+IUUOraTYxpE+prHt/cFtsUsyL0Q7f9Yv3WVt2OtV9J0nT7OSL4WfEoWut2lyHfw5rE42m6hVtyxF/wCC5i7bT8yqpHes7VtP8Y+BdSh0y5uD4uvLLfNod9dBYru8t/47N24SU7eCrYY/K4yVNAHSa9Z6l4ZvtMl/tJWkjKxaJr8z70nV/wDlyvD/ABB/4ZPXH8X3vT/B/i638Xaa9xGklreW8rW93ZTDEttMPvI39G6EYNeU+F76z1zw/PqHhCEalosy7dV8E3x2TWUm350jU/6p/wDpm3yHquOtZlvq1x4Vv7fxDot1NqVgv+ju07bZdn8Nndg/dkT7scjf7rt826gD6NorG8N+IrLxVo9vqFi5aCYfdddro3dHXsy9CK2aAMvX9v8AYuob/I2fZ5M/av8AVY2H7/8As+vtXNfBfyP+FV+Fvsg0hbb7BF5a+HyfsOMf8sc/weldZqVq15p9zAjiJ5I2QSFN20kYzjvWX4H8P3PhXwlpOk3l4mo3VnbpBJdR2yW6ysP4hEnyp9BQB0NFFFABRSE1zvijxppfhOFWvZZHuJP9RZ2ymW4nPoiDk/yoA6Fm21wWpfEC51a8utK8IWy6xqNu/lT3khIsrV++9x99x/cT8cVCfD+u+PikviCR9F0U/MNFtZP3sw/6eJR/6An4sa2b7xN4Z8B2cNj59tZCNcR6faJuk+ixJz+lAEGh+AYrXUU1jWbt9e1tV+W6uVASD1WGPpGP/HvVjVzxd4zsfCNpEZ9013cFlt7WJl3yEDnrwFHdjwK4PWPib4t8SXQ0nwhocdhqUvWfWsn7In/PSSJD8vsjMGPpWj4X+BulafqE2s+I7u58X+I7kDz9Q1M/IP8AYihHyRoP7oH1JoAwND1yTxZqX9sixl8YazbsfssVqPK0zT3/ALqTPw7+sign0xTb6z8ZfE/Vn0SXX20iwt5ManPoDGIR9/syTH53c/xMuzavvW9408bW1w1xoOmalDpFhb/utS1ZPl8hf+feD1mYf3fuD320ug2eta5o0GneH7R/BfhyMbEuJY/9OmT1RGz5eeu58t7UAWrUeD/g9b2mgaBpEaX0ysYNM0uDfcTd2d/bPJdzVfVNLv8AV9LudV8eajFo+hwq0r6PZzssWz/p4l4aT/cXA7fNWpJ/wj3wr08LbWrzaldthYY8zXt/J/vHlvqflHtT9J8IX2vajFrPipo5Z4m3Wmlxvut7T/aP/PST/aPTtQBm6dod144tYrf7M/h3wZEgWDT4B5M14n+2Fx5Uf+x9498dK9CsrGDTrWK2tolgt4kCRxRrtVVHYCrKrTqACiiigAooooAKKKazbaAHUVBHdRzKxjdX2nB2nNSq26gB1FMZgq5JAWhWDLkHigB9FFRNKqsoJAZui+tADnUMp3dK84+F2k2Fn4k8dvbWNrauusbN0ESodv2eE44969I3bhXKeEdBn8Pap4qu7qSERajqX2uLaeieUic++UNAHW0UgNLQAUUUUAFFJupaACiimeZ7UAK3UUq9KY1PXpQAtFFFABRRRQAUUUUAFFFFAEMzOsbFF3vjhc4yazvtuqf9A1P/AAIFa9FAGR9t1T/oGp/4ECqGuXmo/wBj3nmaeqp5TZZZxXTVmeJP+QHff9cWoAhhvNS8tMacu3auP9IH/wATT/tmqf8AQOj/APAkf/E1ow/6lP8AdFS0AZP2zVP+gdH/AOBI/wDiaPtmqf8AQOj/APAkf/E1rUUAZP2zVP8AoHR/+BI/+Jo+2ap/0Do//Akf/E1rU1sUAZf2zVP+gdH/AOBI/wDiaPtmqf8AQOj/APAkf/E1qbqa8ixqS5Chf4moAzftmqf9A6P/AMCR/wDE15xHeX//AA0NcH7CrS/8IrH+789f+fx67zVvHGg6HIiX+r2tq8n3FklHz/SvJv8AhZGlyftBXV1bW1/qdqvhhIxPYWbzB/8AS3zhh2oA9k+2ap/0Do//AAJH/wATR9s1T/oHR/8AgSP/AImueXx1q+oKsmk+Er67tj957qZLRwf9x+aQ+KPFszCNPBrQF+BLJqULKnuQOSKAOi+2ap/0Do//AAJH/wATR9s1T/oHR/8AgSP/AImubaH4hMo/0nw6vzc/uZvu/wDfXWnSeB9Xvl8m+8XahcWjY82KKGKFnHoHRQV7dKAOha/1Jck6fGqj+JrgVVfxBcRpuaC1UL1ZrxKx/wDhVOkMwMt3q1yv8UUupTPG/qrIWwV9quf8Kt8I7lP/AAjemfL0/wBFSgDGk+MOmfctBFqVwx2pa2M3mTSHvtXbzVm38beI9chY6Z4VmgUfKX1Ob7OyN67CuSK7dLeJVGyNRjptFSNQB57Dp/j6/X/iYahb2BTp/ZSJ8/8AveYrfpRF8O4ZLo3N/Z3mryOMyx3upM8Eh/2oT8n6V6JRQBxtt4N0i1kjmi8I6ZBPGdyMiRhge2Dtr4n+MWW+KHijfGImN8+U+Vsf7rV+hDV+ffxk+b4r+K8f8/0lAH1J+zjNeW/wk0RYbFJUJm/eeaqZ/et2r037Zqf/AEDFb/t5X/4mvOv2dLqDT/groctzMlvEvmfPKVRf9Y1R/Bv40H4ka98QdPvZNNtxoPiOTR7EQS/PPEkMTh2yeTl2+7/doAx/i54Uv017Sta8K6bZ6B42vZ3hXV4Y4TNdhLeR0t5nK/NGzIoOeg5HzKK6r4W/FS5+IGi4m00WniDTytrrGmySeXLbXIUb/kPOxvvI38SsDWp46bb4o8Df7WqP/wCk0tc38YPh3qV5e6f418GxrB400uSPftZUOpWQbMto5PytuG7YXyEY5FAHoUl5qEibTpaurdVacfr8teQa18Gdb8N65qPij4cOvhzxBeNmbTb68ebR59x+YtbBgI3J+bfFgk/ezuavTPAHxA0r4k6Guo6a0iOjeVdWNyuy4tZh96KVDyjiutoA8M0n9oG/0DXLXw58RtDg8Ha/dFEs5TfCSy1Ek4/dS7cB88lDyAy816tDrF/cQJLFp8c0TruEkdypVvpVjxB4d0zxTpN1per2FvqWnXUZintrmMOkiHqrA9q8gm/ZC8DeY32S88U6RaBsxWGleI7y1toF/uRRI4CJ6AUAfjb/AMFMHkk/bO8etLGInJtcopzj/Roq+v8A/gns1y3/AATx+KsUVuHRrrUsyNJjH+iRdq+LP+Cgfh+48J/tY+NtJuNSutV+zm2SO6vR+9Mf2aLYrN/HtXC7z8zbcnmvqX9hf4T23jX9h34javqet61HDp8+om0sNOv5bSFHFtEzu/lsvnbvkG18gbePvNQB+pNv4gm0vRrKS7t4LWJo0UST3aIp+UetfKPhf4+eEfEXjWHxr4h1JLyTSp7qLwboFi++7nidmhmlMQXLzO6Oiox24RWGM5qDxJ+zV4S+InjzTPh1bHXNWs7O3ttX1fVdb1WfU4rPZKkkVpEkrMsckoC5Y9YWcfxV9R/Cf4c+FfAPhxrbwx4e03QbWa4ed4dPtVhUvnbuwO+FWgDgf+Ei+MHxU8mXRdIt/h34an+b7ZqJEurSRt91khddkDjHKSq/Wui+G/wftfhteXmowW1/rOuXn/HzrGs6m11cN3ZU3f6pGPPlphM9q9Z20tAGR9u1Ln/iWL/4Ej/4mtC3aR4UMkflORymd2PxqeigAooooAKKKKACiiigAooooAa/3ahsf+PaP6VM/wB2obH/AI9o/pQBYooooAKKKKACiiigBG6Vxunf8lW1r/sFWv8A6Nmrsm6Vxunf8lW1r/sFWv8A6NmoA7JuleR/ErXtd8SeO9N+H/hi+fRnms21PWNYiAaa1tQ+xEhzwJJXz8x+6qMeuK9cbpXivxEurj4YfFax8fTwTXHhe+05dH1ea3Te1hskaSG5ZRz5fzujkfd3K3TNAELfsd/C6VDJdaLeXupSHe+q3Gp3LXhf+/5vm5z9KzL74jf8M06re6d458TTah4NbTJtR0rVdRYG5hMG3zbR3/5aNtdWRm+Y/MDnG6vX7f4heFrzSf7Th8R6VLpzJv8Ata3sZi2+u7divC/Fng/wz+2F40sRfWMurfD/AMMxzNBf/PEl7qEm1Q9u/BdIkDfOPlJfjO2gDxf4e+NvEf7QHiq88exeHNW1Ke6/dabBFAy29hahvkRZX2je33nZe/8Auivd9L+FfiW1tV1bxBqlh4V07T4S6LbIt3cQr1ZlZ18uM+6oW96B8EvGHw/j2aH4m1LxLpsf3LW+uMXKJ/dDblV/0rzXxx4wvPEGrN4Yt9R1ZHtSr39tHqr29z5v8ESRTKwkO7bhQ5BO3+FTQBtaN4N0jUPElz4k1PTJfEF1JcCGBNVl+13d1MnzRWiM/wAsYXHmzMFAB2j+CvVLJ9Th1a70/SJYL/xpebP7Y1U/NbaXHjKxIvcqrfJH3++3WvG9Lkl02aWI+JfEHg+5to/KutV1K3imttEg+XdbxTbGR52+877uvXsK6/w/p+r6xoLw+GviQuh+DYtzy6rcW9s15et/E4b5Sit3dvnPbbQB6NZ3Vv4TFx4c8G2x1rxCZfNv7y5cmOOZ+WkuZP7/AKIOfuj5RV6HTdG+H8v/AAkHifVBe67cgW/264HzH/pjbxDO0f7K8nvmuL8OW/juazbT/BM+j2OjDdjVdR0p4g7N954ot+6Rs/xvgH3rpNB8AeKNBuE1C4m0bXta2bX1K+WZZT67fmZUX2UCgDW+2eKvGs22zjfwpov8VzcIGvph/sJ92Ie7Zb2FUrr4SweHZm1rwkFtPE4H727ui0h1JfveVcP1Zf7rfwfw+lbD6h47t140jRLpv9m+ki/mhpreIvGke3zPCVpL6+Rqq/8AsyCgCTRte034iaPf6Zf2jQXSL9n1HSrofPCWHf8AvKezjg1wl54SFnr0Wi6pdSW+oyRm30bxCyK/2qLbzZ3KniQhV+633xyMNmrnir/hItWvLfVLHwhqWl67Z/6i+t7mB0lTdzDKu/54z+nUVWj+JkHxA0m80LxB4I8QadqtuAbqxjRHlgfqk0Lo+SM8h1oA5q4s5datT8NPG1mun6pERP4Z1Vm3W9yyLuCRSnnenPyfe2euM1vaHr58RaGmg+LhcW6Ncrb22qthJrW8RuIpCPuSK23Y/wB1wy/3sHI1LxdpfjbQZ/A3jzR9aTUows1hqEmmyb7nZtKXMWzdskRsbh+P3WrirX4lW/hPU5j4jjudV0N2Wx8Qx3VlNvgj/wCWN26snzxjb9/723rnZuoA6/WNFfTfFr6pqEE1j4ms4/8AiY32mfupb21HC3sWOHKfdkjbPH4Vq+ItJ1WB21W8SO7E9vsHiLTLfzIbq3PKpeW3dcH76Zx7Vkah468LLZ2dlc+MLSfTCfO0DxMshZ7Nm+5HM3R4yvy72+Vhw3PzVp/DT4saJoOrS6Bf6nYWdnJNtji+2o6Ws5+bYjbv9TLy8Z7fMnG0UAcx4U8eN8NdVj1KCMy+HrxA13bwS+cFQfL9ohf/AJaIvHzfe2/K4yAa+mNL1S11jT7e+s50uLW4jWWOaM5V1PQivM/F3w98K+LY5ZtJ1Wz0q/d2dpLaVGikYjad6bsZ9xg18tePP2ktV/YHvk0rxTp8eveGtdl36StndrtgcMvmsu7kR7Tu29j0+9QB9+s1G72r5/8ACOjyftJx/wDCY61qt9/wgd0CNE8P2cz20dzDu/4+7hkIdy/VU3bVXHGTWvq37O1hoVs9/wDD2+vPB3iC3/eQSRXcstpcMP8AlncQuzK6N0PQjqDQB7UDVe4uorOF5p5FihRSzyOcBQO5NeM6L+054en8F6bfaj+48U3DSW03hq3fzbqO6jcxSptHOwOp+c9sUyLTfGnxMvGu9T02O103/lhZ6kzJbJ/ttCPnmP8Av7B/s0AdXdeONQ8WRsvhMRwadjM3iS+H+jIvcwof9affhPc1j+HdW0HSbi6uvDdrfeOPEFy2y41WPa4dh/C1wcIiD+6n/fNdDp/wqtpY1bxFf3Hieb/nndYS2T2SBMIB/vZrq55tP8PaW8khg0+wtkyW4SONR/KgDlE0Dxb4k3PrWrx6Hat/zD9F+/j/AG7h1z/3wq1zui6fazSzaX4BtI9PtvMK6j4mdPOYsG+dI3bmST/aPyr7nitjbqXxQdWP2jSPCQbOxgUudSHv3jh/8eb2FaOoeMNL8KrDoWj2J1DUo0VIdI01R+7X+EuekSe7fhmgDZ8PeHNN8H6T9mskEUYLSyzSNueRzyzu5+8T6mvNvEnxK1b4iXlx4b+GUsNzLDP5Gp+I5lJtLAfxJEf+W02P4V+Vd2Se1UddsfEHxE1+XQ7q+BZUX7bDYsfsenIezN1mnYdA3yr97HTPrPhrwzpng3Q4dN0y3js7C3XhR+rMe596AOd8G/CfSfCcdtPKW1TUbePZHcXCjEOeW2J0Tcep+8e5q14i8aSRXz6J4fgXVNfwN8bNiG0VujzMPu+y9T+tZ914j1Dx5NPYeGJ2tNLB8u51/b+aW2fvt/t/dHbJrq/DvhvT/C9j9lsIPJQne8jHc8j93djyze5oAzPDPgmPRbibUr24bVdduFUT6hMvOP7iD+BP9kfjmurAxS0UAFFFFABRRRQAUUUUAFcR8ZNOu9W+F/iaxsdZi8PXt1YyQQanPJ5SW7uNoZn/AIfTPvXb1m65odj4k0m60zVLSHUNPuozFPbXCb0kQ/wkUAfDHiaxt/hj4B8a+F9N8Pt8NvHM2lWV8b7S9YfUbS5hW8iRpl3spR9x/iUMQ33jXqHjr40+OvD/AIi8YTWGtaTHpPgabS7S60+6s/32sNcrEXcOH/d/63aihTkq1ey6T8A/h9omi6vpVl4T02Cw1aHyL6Hyt3nx9kYnnb7VNffBHwLqWs6Vq934V0641LS44orS5lhy8ax/6se+3tuzigDwjWfG/jj4kfDf4oeIpPE2l6X4ctk1zR4/D0dpi6H2ZJY1k+0b8iQsm/bt27WH1rd/Zl+MOpePtB1KKWRtPs9C0HThY6XeWzJd3KG23/b2LfMY5GBRR/0zOeTXq958DvAV94kv9en8KabNq97G8dzdND80iumx93bLLwT1xWwfh/ocMn2my063tL9NN/siG6iTDx23aP8A3QecUAfJsX7SXxK0b4d2niG+1nRtWuNc8H3+v2kUVj5S2Fxbyxqqth28xCJcHO35lru/Gvjfxd8M7nwpqPiG907xXqDaZrerRvFp/wBlaFYbFJRCmHP8W7LdcV3Xwu/Zh8F/DnwLDoUujWOp3Umn/wBn3988GGvI87mDD+EMeSor0i/8J6Pql3p9xdadb3M9hHLFaySLnykkTY6j2ZQAaAPnDxV8VfiV8P8A4a6Nq9x4i03xRqfiq7061sRpulIraf8AaNzOVQy4mXA2puYZPU1c8T/Efxh/wyr4t1nxXpMP9s6fdfZo/t0CIt1GLmMJLLCjsEO1uU3dVr1nT/2ffh5pmj6ppNt4R02LT9T2fa7cQ/LLsbcn02nkY6VsWvws8K2fg0+EodCsk8OHrp3l/um+ffk+p3fNQB8+2fxs+Iltrk2vXWpaXceFYfGzeFP7Fjsds5hbaqS+du++rt0xgiqDfG/4lj4A658WE8TaB5dzp1xdad4daw5s5FuPLRTLvzJtX7+R1/u19MR/Djw1HCYRoloIjqP9rbPL4+2Zz53+/wADmsBP2ffh2t5q11/wh2lrPqiOl63kf65XYO4K9PmZVJx1NAHj/wARviF8VvA954e8M22rDXNb1O0vNWfUNN0OM4jj8oJAsTzKuMuSX3Zx271nfEP9ozxv4cXS9RtLe4WXTbDTJ/EGjxabHLbQTXTJuR7lpQy/KzFdin7vOa+iPG3wt8LfEaztLbxLodpq8Fm2YFuUz5XG04PXkVma98B/APirULS/1bwnpt5dWkMVvE8kPIjj/wBWvuF7Z6UAeVzfGTxj4Z+I2sHxLLPZ6P8AaL+30nT4dMR7W98mF5IlF2spYSkRsxVk9qn/AGbfit8Q/H2srJ4nsHbQ9S0ZNXtrqSyithBI7jEKbJXMibW++wBynvXrFj8HPBemeLJfEtt4bsY9cmZ5HvFi+be64dvQFh1PepfB3wn8JfD2+v7zw74fs9Iub7/j4ktY9pcZ3bfYZJOBQB89+FfjJ4/vPg3J481nxZaxnWL2TSdI0zTdCE0qXP2x4YtuZV8x2VNu1sLnmuf8NfFLxt8RPiR8MbfVdSuNEvdH8W6tpN9C1qkT38UenNMnmxB2CPtbbtVjzyK+pbr4U+EbzwY3hObQLGTw4zlzpzR/ut5ffux2O87s+tUdL+BXgLRbKytLLwtp9rDY6h/atuI4uY7vbt85W679vGaAPGvgT8cfiN8SPG2n3l7pb/8ACJarNfxGJrSOFLAQSskTJN5peYnbtdSg2lq+pV6VxWifCHwd4b8WXniXTPDtjZa5db/NvoosO29tz/TPf1rtV6UALRRRQAUUUUAFFFFABRRRQAUVT1D7ULOY2YjN1sPlCYnZv7bsdq5DzviN/wA+vhv/AL/3H/xNAHd1meJP+QHff9cWrl/O+I3/AD6+G/8Av/cf/E1T1pvH0ml3S3K6DbQNGyvLA8zunuFZcH8aAPQoflhQf7IqTdXF2/hvxU0KE+MjuI/6BkNRv8NpL5mm1LxJrU92/DyWt01snttROBQB2zyBVyxCj3qtcalbW8LyyXESRoCztvHArlU+Fth5iGfVNavol6wXWoO8Tj0Ze9W4fhb4Tt5I5I9AsUeM7kby+lAFQ/FzwvM3l2WqJq11/wA+umj7RMfU7E5wKin8e3958ujeFdUv9n+s+0oLTH93Hm43d+nSuxisbe3IKQRow/iVAKs7aAOJjuPGesx7xbadosUh27Lh2luIh6/L8hPcU23+HLXTFtd1u/1t933WfyYSno0ScHv1ruaTbQBiaL4N0Pw6so0zSbWy8z7/AJMQXNcZBGkf7RtwiAKv/CKR/Ko/6fHr0+vNE/5OPuP+xUj/APSx6APSttG2helLQAm2loooAKTbS0UAFFFFABRRRQA1q+Ufi1+z5qF948Oo/wBq2ir4g1MxQJJExMZZGbn/AL5r6uauH+IWn3V9rXguS3haVbbVllmZf4E8p1yfzoA+Vf28vB918O/+Ce3iPRZrtbi4tZ7ZvOhyq/NeK3H/AH1X5bfsM3U0n7XvwnUzSFW8QW5YbzzzX66/8FTP+TK/Gn/Xez/9KUr8hv2Ff+TwPhN/2H7f+dAH6u/EuH4kR/ELUltm19rdtRmbS9pl2d2/df8AAN34V9K/BH+2f+Fa6T/b4u11b955327/AFv+sbbn/gOK3vEXh6bWNX8PXkc4iTTbtriRW/jBidNv/j9c18dfGGqeA/h3c6ro7LHepPFEhaPeMM2OlAHlPiBtc8A+IfGHxa0OJ9Ve2vDp2t6HFF815ZwsqxPF/cki3ud3Rl3Z+6K+h/D3iDT/ABVoljq+lXcN9p17Es0FzA+9HRhkMDXyh8GvjL4p1Dx9p2kzz27WGrX7S3Y+zr87uDu2+n3a9A1DTfEvwD8aavd+FdAbxZ4a8SzrNDoFneQ2kun3QT96YUlYIUfl22/NvbpigD6EpG6V4daftZeEmkjj1XRvFfh/DKLm41Pw/dxWloe7S3BTywgP8e7Hepr79pCx8QTvp/w20a8+IN/syt5poC6ZH2y122I22krlEYvz92gD8b/+CnjS3H7afj92Q7E+xorY4wLSKvor9hfX/FQ/Yh+I2g+HNDTyZJtSuNR1rU1aO1t4fskQ2RY5kmb5sfwrt+fqK+ufi3+zb43+MGk6PeeNrvT9Zul1zTri68M6REsWnpbpcp57PK+2S5XyVY7H6Mflr234yaLYaR8BfG9hY2kNlaRaFdhILdAiKBC2OB9KALXwj+HNl8P/AA0hS4uNU1jUljuNS1a9O64vJtm3c391R0VB8qDgcV1Xhb/kEp/10k/9DapPD8hk8P6ax6taxt8v+4Kj8L/8ghP+uj/+htQBsUUUUAFFFFABRRRQAUUUUAFFFFABRRRQA1/u1DY/8e0f0qZ/u1DY/wDHtH9KALFFFFABRRRQAUUUUAI3SuN07/kq2tf9gq1/9GzV2TdK43Tv+Sra1/2CrX/0bNQB2dRSRLIhR1Dq3BDDrUtecfE74jX/AIb1LSfDXhrT49X8XayJHtYLh2FvbQpjfczsOfLUsq4XliwAoAluPgH8OLzU/wC0Z/A+gPel95lbT4uW/vfdrubW1hs7eOC3jjhiQbUjRNqgewrxtfhT8UZ7f7ZcfGC5h1U/P5FpolsLEN/d8tgXI/4HmtLwr8VNS0K91Tw/8RlsdL1jS7FtU/tS1fbZ39mjYeZFbmMocb0YnbuU7iDQBp/G74oD4a+F1e1WOfXdRf7Jptsx+/Iern/YQfMa8F0Xw/bXltFaajG2ozzPI0kqw77mSQr87Qsej/eUydIxwrZzXCeDfHWq/tQfEzUPF2kafeX1kpNpo4ZCsNla5++z/dV3Zd7c56L/AA19K+G/hPpekxvL4o1O1mL7QbNZRFDtHRXO7L/7vCf7NAHAeHvCevta28fhKeLW4rN2+z6LqDtLolk27vc/6yaRfX5xnd0rom8N+Fbq+iu/ilo2zWflRLi8hUadC3/TGVPlXn+J8PXqjfELwhotqsCa3pyRRrtSK1kV9o9AqZrMu/izpGoRyW9nouua0HBBSLSZVR/xkVRQBNYeAW0+NZfDvinVLS2cbo4ZZxe2+328zdx9DS3EnxA0eXMcej+I7X+7l7Kb/wBnQ/pXJL4d1iF3uPB/h3U/CkrnfskvYVtmb/bt9zr/AN84NJqHjr4o+HbWJtZ8N6RHAFVZdS0x571Ub+8bdVVwPpuoA7T/AIWM1iu7WPD2s6bj78i2/wBpiH/Aoy38q1NF8e+HvEbiPT9YtLifvB5oWUf8APNcloMev+MIRcQ+P7KaH+KPSbGNSnsd7OR9CKs6n8E9K8RQvHr2o6nrKuNp82ZYv/RarQB6DJMka5dgi/3mOK89+IF94Xuvs91L4l0/Q9csSz2d956b42/iRlz86HulZs37NfhDEUlr/adq8cflIrX8txEF/wCuUpdD+VWdN8Aa94Rl/wCJZ/wj+q269I7rTxbTf99x/L/45QBy7fFPw78Q7BNG1B7u28WWJW4hl0q0mm2up4nt3C/PG33T/vFTVObx1qviRvNi8H6pJ410ePZd2LRpDDqVq7bW/wBYeUfbuXcPkPHrXU+M7i9vrOJtV8K6nYXVq/mWuqaG6XL2z/3go2uR6rtORXPW/wAQNM8cTQWNzqlr4e+Imkp5llcXUb2yXSnrtWTaSj4+ePqv5NQBw/hf/hLtPkuNN8KeGdJXR72J7iy0jXL/ADDdwD5ZIdgQ+XNG3yn+Fhtz3aqmqeGdU1bTZ47u50e3t7NGhHn6azXOmM//AC7XO9smBvl2SdtqnpXda00epW0uuaeDpdxHdq19Ep3nStQX5VufeF1+VyvBRs/3q7e30zTfixoUWrR79H8R2we0mniUebbSDh4nU8SRk/NtbhgVNAHBfDrwxaeOI59I13UZk1ex+WSJLG0geRP725E+964/2SPlYVk/FL/gn78Nfi94i8Lajr/9oz22iPK8tm1yxW9342q7dQAV/hxmszxdpOv/AA31mzvkjjsNUsdrWk8C/wCjToNymEH+FGVvuNym7gkdPoL4Y/ETTPif4Vg1nTmwWJiubZv9ZbTLw8TjsV/UYNAHnHwP8Uaf8K9PtPhT4ju49K1bRd1rpLXX7qPUrFW/cvC7cOwTarqOVYe4rv8Ax98XvDfw809ZdQv45r6clLPSrVhJd3sv8MUUQ+Zyf071c+Imk+DdW0Fo/G0Ojy6UpyW1nyxErf7z8A1y3wt8N/B7T9QluPAMHhZ7/bteXSZIZZgPqrFgKAGfs/8AwxPgvwo+qa1pVna+Mtdup9W1aWJAXSaeVpfK39SE3BP+A16zxR/tVyXjDxxbeGRHaRJ9u1aYbo7VXCKi95JX6Rxj+8fwyaANTxJ4osfCtiLm9kf538uGCJN8sznoiIOSa4PUvLma117x7PHZ26TbtN0DO/D/AMBdR/rpvYZVe3rWXoKax4i1CfUtPEWpayd0R8RX0RWztQeqWkXVwP7/AN092PSvQNA8C2Wi3n9o3Ekura0y7X1G9be/0QdEX2XFAGP5fiTxyZFYzeFNCI2qseP7QnH97PSEfTLfSqE8aabcHwh4Gjis7sbW1DUShkWzQ9yx+/O3ZWPu3vr+K/El9qGrf8Ix4cO3VHj33d/gNFp8Z/ib1kP8KfieOqS3Wj/CvR4NPs4Jr2/uZD5VrD+8ur2Y/ec/zZ24FAGlZ2uifDPwsVMiWdhbDfLPK255XPV2bq7sfxJrDGm6n8TJC2qRT6V4XyGj05spcXv+1N/cT/pn3/i9Ku6R4PvNW1KLWvFLR3d7E2+00+Lm3svp/fk/2z+GK7dVoAgtbWKzt0ggjWGJBhEQYAFWaKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigBrdRSr0pG6ilXpQAtFFFABRRRQAUUUUAFFFFABRSE0nmD1oAdWZ4k/5Ad9/1xatHzB61neIvm0S9A+95ZoAu2/+pT/dFTVDbsPJTP8AdFSbhQA6im7hR5g9aAHUU3zB60eYPWgB1FN8wetG4UAOrzNf+Tjrj/sVI/8A0skr0rcK80Rh/wANHXH/AGKkf/pY9AHptFNVqN1ADqKbu96PMHrQA6imswo3e9ADqKbu96N3vQA6ik3Um73oAdTWWjdSbx/eFAHlf7S/wLtf2kPhBq/gC91WbR7bUHhdry3jEjp5civwpPP3a+MP2aP+CVH/AAq34wQeNdT8U30b+GPEHnaTbNbxkajaoqFJHZX+QszOMf7NfpJuFN3CgB69Kimt47iNklRZEP8AC4yKk3CjcKAOc8M+DbPw2+ovHHG73V9Lehigym/Hyj/vmuV8ceKtC/4S/wAIb9ZsVNrfTGf/AEuNfL/cuPn+bjmvTGYba/On4kMG+IXiUY2r/aM3zf8AA2oA9O/4KefEjV/CP7Jsuu+D9en064n1W1iW+0242742LZXcvUHFfnv+wf8AtHfFDxR+1V8OPD+q+Otcv9DutQdZ9Plu2MMg8lzynQ8qK+w/20vCur+Mv+Ceug6doOlXusX/APaVs/2axgaaXaJJdx2jnFfD3/BPv4WeMrD9rf4e6rc+F9Yt9MsdVmhu7ySylWK3dIXVldtuFILLwaAP3wXpXC/HT/ki/jvj/mB3n/ol67lWHrXCfHVv+LL+Ov8AsB3n/ol6AOo8N/8AIt6X/wBekP8A6AKZ4X+XSE/66P8A+hmn+G2/4pzS/wDr0h/9AFM8L/LpCZG395J/6GaANiim7hRuFADqKbuFLuoAWiiigAooooAKKKKACiiigBr/AHahsf8Aj2j+lTP92obH/j2j+lAFiiiigAooooAKKKKAEbpXm+oeJtL8J/E/UZdYvYtMhutLtkhnuW2I7CWXcAx4yNy8f7Vek1XubOG7jMc8SzI3VXGRQBS0PxHpfia3efStStdShRtjyWkqyKG9MivLNX1ODwf+0pp97q+IbLxDoa6VYXsn3BdRzvKYM9mdH3D18qvWtP0mz0uN0s7SG0RzuZYIwgY+vFZnjDwZpHjzQZ9H12wj1HT58M0Ug6MDlXVuqsp5DDkUAb+75a+Xv2jvhfY/tM/EbQ/AcOsXmm2+j2dzd65eaZIu9Ek2CG0kzkMsjLvKN2QV6L/woO6j/wBGt/iR45t9L/5811KNyB/dEzxNLj/gea7TwN8O9A+G+ly2Og2ItUnka4uZ3YvNcynrJLIfmd/cmgDxqw+APi3wbo8Gn6dr8eradbJtjtI1FiNo7BE/d/yqzZ+H5dJbOo6QdLnXrPdeHYr1P+/sbsf++q+hdtJtoA8m8O6xJqD/AGfSvFvhme4X/lh/Z3lOP+AebmunWz8cRj/kIaHL6L9llT/2dq19c8G6J4m2/wBraRZ35Xo1xArsP91uorH/AOFZW1qv/Eq1jWdG9Ftr1njH/AJN4oATzPHsbf6vw/Kv/XSZf/ZabJfePlbH9j6DKnfbfzD/ANpVF/Z/j3R3zb6ppWvwL0ivLdrWU/8AA0Zl/wDHKmbxzq2loW1bwnqMCjrJp7pej8kw/wD47QByfiLw14h1i4W8/wCENs7XVB01LStZ+zzL/vHYu8f7LZFQw+Kfiv4XZBc+EI/FVginfJFqMEN4Mf7O0I//AI5XdaR8UfC+tTtBFq0dvdKdptr5Wtpf++ZAprqYpo7hN0bq6/3lOaAPK9F+O0mqXKWc3hDVrDUmG7+z7iaBLj/vhnGf+A5roo/iRd/8tvBniKH/ALYwv/KWt3xB4W0rxRaLbarYQ30aHenmplkb1U9VPuK5ubwn4k8Mssnh3WzfWo66ZrjNKp9kmHzp/wAC30AWF+JsZOH8N+I4v9o6fu/9BY1g+NNU8MeOtM+w6pousgqfNguf7HlZ4JF6OjbDhhW3D8ULXT7mK08S2Nx4aupTtR7r57Z29FuB8n/fWK7O3miuIlkjdXRujIcg0AfKOqQ6BDrCI0V/4f8AFsaNFDLZ2txaWmv2+1i0Toq4D/ebG3g8jIY1m+H/AImn4ea1FfWHjSS80O5C280Guaa++EIv3JXRVbzIfunPWPnnZX1b4i8N2HinTXsr+NmiyHR0O143HR0bqGHrXh/jHS9V0bUPsmrF7qe5+UXUSbU1JEHyPt6JdoO33ZV3D2ABr6p8ZNOvrN7HxJpWn6ppsyfPLY3aupX12SKh/KvjH9oD9pSD9jbxNb+Ivhrqy69Z+KgYbnQdUifFqYmXbNv/AIiqnYP5tivtf4J+N4byOTwrfmKee2j87T5tp2z2391Vbn5PT046q1dN46+A/gP4la9ouseJvDFhrd7o4f7D9sj3pFvxuOzox470AcB8GfDOn/HDSdN+LHi+y/tO41qBbnR9M1D97b6ZZtzFsiPyeYw5d9u75tucCu58efA/wv4ysV8vT4dD1qD57HW9MjFvd2cg+66OnOPVTww4NcL4D8Z6f+zrbx+APGUkmkaJaTOmga/cL/oU1qXLRW7y9I5Igdm18blVSK6XxP8AtIeErQJp/hjUYfG/ia7G2y0jQZRcs79jI6ZSFPV3IFAHIeD/AI/eIvGHhuDw7pWkvqvxCsZ5tM1qaGMpY2U0LmN5Xc8fPt3qi5PzV3vhX4Ow28n23xHdf21fyN5skbD9y0n95/8AnofTdwv8KitH4J+A7r4e+A7Ww1SeK71y5mm1DVLmEYSS7ncySsvtubaPZRXffdoAjSNIVCIoVVHCqOlcX4r8VXlxqn/COeGmjl1pwGurmT5o9Oib/lo47uf4E/i+lJ4x8XXa339gaB5cmuyR+bJcyjMGnxn/AJbS+/8AdTOTj0zXO+G7OfVrN9M8KTy2uktMz6j4mnG64v5OjtD6k9PN6LtwAf4QC9a3MXhNf+EY8JW39qa4X33t5cszJC78ma5fqXbsg5+grpfC/guDw7LPf3M7anrVzzcajOPnf/YQfwIOyCtDw/4b0/wvpwstOthBBuLsfvNK5+87nqzHuTW1QAirtpaKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAa3UUq9KRuopV6UALRRRQAUUUUAFFFFABRRRQBFNCtxG0bjcjDBWs7/hG9N/59V/M1rUUAZP/CN6b/z6r+Zqjr3h/T4dFvSLYf6tu5rpKy/Ev/IDvv8Ari1ADI/D+nyRxZthwo7ml/4RnTf+fRf++jWhb/6lP90VNQBk/wDCN6b/AM+q/maP+EZ03/n0X/vo1rUUAZP/AAjOm/8APov/AH0aP+Eb03/n1X8zWtRQBknw1p3/AD6r/wB9Gj/hG9N/59V/M1rUUAZX/CN6d/z6j8zXAJ4QX/he095/Z8n9mnw3HALjDbPM+1O2zPrjmvVKaMNQBl/8Izpv/Pov/fRpf+Eb0/8A59h/301atFAGT/wjem/8+q/maP8AhGdN/wCfRf8Avo1rUUAZP/CM6b/z6L/30aP+EZ03/n0X/vo1rUUAZP8Awjem/wDPqv5mj/hGdN/59F/76Na1FAGV/wAI3p3/AD6j8zSf8Izpv/Pov/fRrWooAyf+EZ03/n0X/vo0f8I3pv8Az6r+ZrWooAyf+Eb07dn7Kv8A30aP+Eb03/n1X8zWtRQBk/8ACN6b/wA+q/maP+Eb03/n1X8zWtRQBlf8I3py8/Zh+Zr4t+IHwb8XN47v/L0WVYtU1GdrRvOTEn3n/vf3Vr7nrhvHP/I5eBeD/wAf83/pO9AFD4OeCH0H4d6LZ6tp7WupQxt50bPyG3t6HHTbXWWvhPSbFJktrGKFZZGmkCZG92+8x9z61tr0paAMn/hGtN3M32Vct15NcV8bvDtjH8G/HRS2G/8AsO+xyx/5YPXpdMZRIpBwVPWgDmfCmiWUnhXRXe3Bc2cLHr18taPD/h+wuNLR3tgW3v3P99q6ZVG3AqO3tYrWPy4kEaddq0AUf+Eb07/n1X82o/4RvT/+fYf99NWrRQBk/wDCN6b/AM+q/ma0YLdbeNI4xtRBgLUtFABRRRQAUUUUAFFFFABRRRQA1/u1DY/8e0f0qZ/u1DY/8e0f0oAsUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFACbaNtLRQBQ1HRrHVY/LvLK3vE/uzxB/wCdcrefCXQpHMunC70K56rLpV08H/jgOw/itdzRQBw6+HfGGkoBY+JYdTRf+Wer2i7j/wADj2/+g0w+LvFOj8ap4Se8jHWfR7pJf/HH2Gu7pNtAHD/8LM8M38ZtdSeTT2b5XttXtXh/9DXafzqnD4JsDKdR8Ga22kO33obOQT2Un+9Dnav/AADaa764t47iMxyxrIjdVcZFcjefCTwvcXn2yHS10y9/5+NMc2z/AJpigCp/wmWueG38vxLosktqP+Yro4aaL6vF99Pw3Cth5vD3xG0Sa2We31XT5Bh1jflG/wDQkYfgRWfceC/EViudH8XXa7ekWpwJcofq3yv/AOPVy+qeE9c+3f2leeHbWfVkG1dW8OXjWlwV/wBpX4P+6zEUAeX/ABQ8H678M7621OzuZC1rMtxYaqxzlx96Kb3deD2fr97dn3/4Y/ETT/id4Pstc09tvmDZcW7H57eZeHjb3Brzy88ealpNtcaX4q0u51fRp0ZH/tCy8mYL/ErMN0Mn13JXy/4p/aE8P/sT+OLrxNYan/wkngDxVuR/D6Sqmo2V6i8Ptb7ybflL/wC71xQB+hF9Y22oW7QXVvFcwv1jmQOp/A1V0rw3pOgqw03TbPT9/X7LAkef++RXifhC81v9pixXxTB4i1Lw58PLpF/sqz0eQ293fp/FcSzdVRm4RExwuSfmrY1T4Ka14Qs5dQ8A+L9di1aD94mna5qMl9Y3eP8Alk6ybmTd/fRgRQB7R9K8/wDiJ8Q4/C6nTrCSM6vL8zPJ80dqh/5aOP4j/dQcufxrj2/aOtfEHhXQ18PadJdeNdbSVIvD7HdLZSRuUmNxj7iRupGTjd2rpPh/8KRo8i6xr8q6p4iklNw8jL8kchG3cPV9vy7j24GBQBm+D/h7c6xYxnVlubbSZZPtEtrdHN3qcn/PS7YfdHpEOAMA+les2tvHawpFEixxIMIiDAAqYDFLQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUANbqKVelI3UUq9KAFooooAKKKKACiiigAooooAKKKKACsvxL/yA77/AK4tWpWZ4k/5Ad9/1xagC9b/AOpT/dFS1Fb/AOpT/dFS0AFFFFABRRRQAUUUUAcn8UPGtv8ADn4eeIvE11IkcWl2M1yPMOAzqvyL+LbR+NfPv7GfjiW017xl8ONT8Ux+LtRsfs/iG31SO6W4Dx3Y/wBIi3A/8s7lZVC9ldK9f+Nnww1r4pabodhpmv2+iWljqcGo3cVxYfalvPJcSJEfnTau9VY+uBWV4s+Deu6x8a/CvjzRvEdloUOjWsthcaeulb3vYZXVpUeXeuB8ilPl+Vuec4oA9kopF6UtABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAVj6voMGralpN7KSJdNmeaNV7lkZP/AGatiigBF6UtFFAEM8ZkhdA7RMwIDr1HvXwfcftD634b+G/xE1W++MV0fGmga9qGm6VodxY2bxXfk3Hl28UqLCrfPwrOHGN27jFfeE7usLmNQ74OFJwCfrXyt4Z+EfxS0/4b+PPB114Y8Izp4q1TU737ZearLNDbJduzYaH7P+8Kbv7wyfSgD6Z8M3V3feHtLur+JYL+4to5Z4ozlUkZAXUH0zmtaub+HvhY+BvAfhzw4bt7/wDsfTbbT/tUv3pvKiVN5+u3NdJQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQA1/u1FZ/8e0f0qVqakKxqoUYC0AP3Ubqb5fvR5fvQA7dRupvl+9Hl+9ADt1G6m+X70eX70AO3Ubqb5fvR5fvQA7dRupvl+9Hl+9ADt1G6m+X70eX70AO3Ubqb5fvR5fvQA7dRupvl+9Hl+9ADt1G6m+X70eX70AO3Ubqb5fvR5fvQA7dRupvl+9Hl+9ADt1G6m+X70eX70AO4pOKTy/ejy/egBrRrIuGAZT/AAtXiPxs/Y5+Gf7QWvaLqnjPSJL5tKSRIbeCZoYn343M4TGTxXuHl+9Hl+9AHiH7NurWvg3wva/CzVJ4rHxF4VU2MVq52Nd2aH9xcR5++rJtzjowINeleOfHmi/DvRJtV1u+jtbdB8ked0sz9kiT7zu3QAVD42+GHhn4jW8EXiLRrfUTbtvglcYlhb1Rx8yfgayvD/wH8E+G9bi1m20RZ9WhG2G8vpnuZYf9wyM2z8KAOf8A2c/h2fCfhO817VNHj0vxV4nvLjWNRjCgywmeVpEty3+wrAemc17CuKQR0eX70AO3Ubqb5fvR5fvQA7dRupvl+9Hl+9ADt1G6m+X70eX70AO3Ubqb5fvR5fvQA7dRupvl+9Hl+9ADt1G6m+X70eX70AO3Ubqb5fvR5fvQA7dRupvl+9Hl+9ADt1G6m+X70eX70AO3Ubqb5fvR5fvQA7dRupvl+9Hl+9ADt1G6m+X70eX70AO3Ubqb5fvR5fvQA7dRupvl+9Hl+9ADt1G6m+X70eX70AO3Ubqb5fvR5fvQA7dRupvl+9Hl+9ACtSr0pNtKvSgBaKKKACiiigAooooAKKKKACioppPJjZsM20Z2qMk1m/2+P+fC+/78UAa9ZniT/kB33/XFqj/t7/pxvv8AvxVHXNaE2j3Y+xXnMbfegagDfg/1Kf7oqasWPXAsSZsrw4H8MNP/ALdX/nxvv+/FAGvRWR/wkA/58r7/AL80f28P+fK+/wC/FAGvRWR/bx/58b7/AL80f278v/Hhff8AfigDXorI/t7/AKcb7/vxR/b3/Tjff9+KANeisj+3v+nG+/78Uf28P+fK+/78UAa9FZH9vf8ATjff9+KX+3v+nK+/780Aa1FZH9vH/nxvv+/NH9vj/nwvv+/FAGvRWQuvhv8Alxvv+/FL/bv/AE5X3/figDWorI/t4f8APlff9+KP7e/6cb7/AL8UAa9FZH9vf9ON9/34o/t7/pxvv+/FAGvRWR/b3/Tjff8Afij+3v8Apxvv+/FAGvRWR/b3/Tjff9+KP+Eg/wCnC+/780Aa9FZH9vf9ON9/34o/4SAf8+V9/wB+aANeisj+3v8Apxvv+/FH9ur/AM+N9/34oA16KyP7e/6cb7/vxR/wkH/Thff9+aANeisj+3j/AM+N9/35o/t4/wDPjff9+aANVm2rUFnex30QliO5CSN30qgNc3MoFleru/vQVneH9YFvpaJ9ju22u/zLD/ttQB1NFZH9vf8ATjff9+KP7eH/AD5X3/figDXorI/t7/pxvv8AvxWhbzfaIUk2Mm4Z2uMEUAT0UUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAJto20tFACbazfEn/ACA77/ri1adZniT/AJAd9/1xagC7Av7lP91al21Hb/6lP90VLQAm2jbS0UAN20u2looATbRtpaKAE20m2nUUAJto20tFACbaTbTqKAG7aXbS0UAJtpNtOooATbRtpaKAE20baWigBNtG2looATbRtpaKAE20baWigBu2l20tFACbaNtLRQA1l4rJ8LqP7IQdP3kn/obVrP8AdrJ8Lf8AIJT/AK6Sf+htQBr7aNtLRQA3bS7aWigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACq95apfW0sEmdki7TtNWKKAGIuxQPSn0UUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAI3SqtlYx2ECxRZ2As3J9TmrdFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAN/Oj86KKVgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosAfnR+dFFFgD86PzooosB//9k="}}, "cell_type": "markdown", "id": "ad6d67e8", "metadata": {"papermill": {"duration": 0.007171, "end_time": "2023-01-23T21:40:33.479342", "exception": false, "start_time": "2023-01-23T21:40:33.472171", "status": "completed"}, "tags": []}, "source": ["![FM236jJX0AovEh_.jpg](attachment:4446566d-5335-4234-af2c-7da1aac6359b.jpg)"]}, {"cell_type": "markdown", "id": "b05ddd82", "metadata": {"papermill": {"duration": 0.008613, "end_time": "2023-01-23T21:40:33.496654", "exception": false, "start_time": "2023-01-23T21:40:33.488041", "status": "completed"}, "tags": []}, "source": ["### Getting some historical data."]}, {"cell_type": "markdown", "id": "aa5a4ae2", "metadata": {"papermill": {"duration": 0.007779, "end_time": "2023-01-23T21:40:33.514571", "exception": false, "start_time": "2023-01-23T21:40:33.506792", "status": "completed"}, "tags": []}, "source": ["Stock Price Prediction using machine learning is the process of predicting the future value of a stock traded on a stock exchange for reaping profits. With multiple factors involved in predicting stock prices, it is challenging to predict stock prices with high accuracy, and this is where machine learning plays a vital role. "]}, {"cell_type": "markdown", "id": "6c4c0296", "metadata": {"papermill": {"duration": 0.008722, "end_time": "2023-01-23T21:40:33.531802", "exception": false, "start_time": "2023-01-23T21:40:33.523080", "status": "completed"}, "tags": []}, "source": ["We will be building our LSTM models using Tensorflow Keras and preprocessing our data using scikit-learn. These imports are used in different steps of the entire process, but it is good to club these statements together. Whenever we wish to import something new, just add the statement arbitrarily to the below group."]}, {"cell_type": "markdown", "id": "167e5bf1", "metadata": {"papermill": {"duration": 0.007354, "end_time": "2023-01-23T21:40:33.550114", "exception": false, "start_time": "2023-01-23T21:40:33.542760", "status": "completed"}, "tags": []}, "source": ["<a id=\"table\"></a>\n", "<h1 style=\"background-color:red;font-family:newtimeroman;font-size:250%;text-align:center;border-radius: 15px 50px;\">Table Of Content</h1>\n", "\n", "* [1. IMPORTING LIBRARIES](#1)\n", "    \n", "* [2. LOAD THE DATASET](#2)\n", "  \n", "* [3. TECHNICAL ANALYSIS INDICATORS](#3)\n", "\n", "* [4. NEURAL NETWORK](#4)\n", "\n", "* [5. BACKTESTING MODEL ](#5)\n", "\n", "* [6. <PERSON><PERSON><PERSON> MESSAGE ](#6)"]}, {"cell_type": "markdown", "id": "e6f67839", "metadata": {"papermill": {"duration": 0.006945, "end_time": "2023-01-23T21:40:33.564336", "exception": false, "start_time": "2023-01-23T21:40:33.557391", "status": "completed"}, "tags": []}, "source": ["<a id=\"1\"></a>\n", "<h1 style=\"background-color:red;font-family:newtimeroman;font-size:250%;text-align:center;border-radius: 10px 10px;\">Import Libraries</h1>"]}, {"cell_type": "code", "execution_count": 1, "id": "1fbcd93c", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:40:33.581142Z", "iopub.status.busy": "2023-01-23T21:40:33.580384Z", "iopub.status.idle": "2023-01-23T21:41:02.777818Z", "shell.execute_reply": "2023-01-23T21:41:02.776285Z"}, "papermill": {"duration": 29.209352, "end_time": "2023-01-23T21:41:02.780916", "exception": false, "start_time": "2023-01-23T21:40:33.571564", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\r\n", "beatrix-jupyterlab 3.1.7 requires google-cloud-bigquery-storage, which is not installed.\r\n", "pandas-profiling 3.1.0 requires markupsafe~=2.0.1, but you have markupsafe 2.1.1 which is incompatible.\r\n", "apache-beam 2.40.0 requires dill<0.3.2,>=*******, but you have dill 0.3.6 which is incompatible.\r\n", "apache-beam 2.40.0 requires pyarrow<8.0.0,>=0.15.1, but you have pyarrow 8.0.0 which is incompatible.\u001b[0m\u001b[31m\r\n", "\u001b[0m\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\r\n", "\u001b[0m\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\r\n", "\u001b[0m"]}], "source": ["import pandas as pd \n", "import matplotlib.pyplot as plt \n", "import datetime\n", "from datetime import date, time\n", "\n", "#Install Yahoo Finance Libraries\n", "try:\n", "  import yfinance\n", "except:\n", "  !pip install -q yfinance \n", "  import yfinance \n", "\n", "try:\n", "  import yahoofinancials\n", "except:\n", "  !pip install -q yahoofinancials \n", "  import yahoofinancials \n", "\n", "import yfinance as yf\n", "from yahoofinancials import YahooFinancials \n", "\n", "import warnings \n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "markdown", "id": "f6b7fa88", "metadata": {"papermill": {"duration": 0.007415, "end_time": "2023-01-23T21:41:02.796210", "exception": false, "start_time": "2023-01-23T21:41:02.788795", "status": "completed"}, "tags": []}, "source": ["Treating stock data as time-series, one can use past stock prices (and other parameters) to predict the stock prices for the next day or week. Machine learning models such as Recurrent Neural Networks (RNNs) or LSTMs are popular models applied to predicting time series data such as weather forecasting, election results, house prices, and, of course, stock prices. The idea is to weigh out the importance of recent and older data and determine which parameters affect the “current” or “next” day prices the most. The machine learning model assigns weights to each market feature and determines how much history the model should look at to predict future stock prices."]}, {"cell_type": "markdown", "id": "0abcc590", "metadata": {"papermill": {"duration": 0.007181, "end_time": "2023-01-23T21:41:02.811078", "exception": false, "start_time": "2023-01-23T21:41:02.803897", "status": "completed"}, "tags": []}, "source": ["Go to finance.yahoo.com/ and search the company you wish to predict the stock of. For our example, we will look at the Tesla (TSLA) stock over 3 years.\n", "\n", "Going to finance.yahoo.com/ in the “Historical Data” section, we see the stock data listed each day. We can filter out the time for which we wish to analyse and download the CSV file using the download button on the right."]}, {"cell_type": "markdown", "id": "31c6c05a", "metadata": {"papermill": {"duration": 0.007316, "end_time": "2023-01-23T21:41:02.825981", "exception": false, "start_time": "2023-01-23T21:41:02.818665", "status": "completed"}, "tags": []}, "source": ["<a id=\"2\"></a>\n", "<h1 style=\"background-color:red;font-family:newtimeroman;font-size:250%;text-align:center;border-radius: 10px 10px;\">Load Dataset</h1>"]}, {"cell_type": "code", "execution_count": 2, "id": "9edc76b2", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:41:02.844477Z", "iopub.status.busy": "2023-01-23T21:41:02.842733Z", "iopub.status.idle": "2023-01-23T21:41:03.992986Z", "shell.execute_reply": "2023-01-23T21:41:03.992022Z"}, "papermill": {"duration": 1.161959, "end_time": "2023-01-23T21:41:03.995539", "exception": false, "start_time": "2023-01-23T21:41:02.833580", "status": "completed"}, "tags": []}, "outputs": [], "source": ["#Download our Historical Data\n", "df = yf.download('TSLA',\n", "                 start='2000-01-01',\n", "                 end=date.today(),\n", "                 progress=False,)\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "1fe595e0", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:41:04.013066Z", "iopub.status.busy": "2023-01-23T21:41:04.012113Z", "iopub.status.idle": "2023-01-23T21:41:04.034560Z", "shell.execute_reply": "2023-01-23T21:41:04.033346Z"}, "papermill": {"duration": 0.033985, "end_time": "2023-01-23T21:41:04.037174", "exception": false, "start_time": "2023-01-23T21:41:04.003189", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2010-06-29 00:00:00-04:00</th>\n", "      <td>1.266667</td>\n", "      <td>1.666667</td>\n", "      <td>1.169333</td>\n", "      <td>1.592667</td>\n", "      <td>1.592667</td>\n", "      <td>281494500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-06-30 00:00:00-04:00</th>\n", "      <td>1.719333</td>\n", "      <td>2.028000</td>\n", "      <td>1.553333</td>\n", "      <td>1.588667</td>\n", "      <td>1.588667</td>\n", "      <td>257806500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-07-01 00:00:00-04:00</th>\n", "      <td>1.666667</td>\n", "      <td>1.728000</td>\n", "      <td>1.351333</td>\n", "      <td>1.464000</td>\n", "      <td>1.464000</td>\n", "      <td>123282000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-07-02 00:00:00-04:00</th>\n", "      <td>1.533333</td>\n", "      <td>1.540000</td>\n", "      <td>1.247333</td>\n", "      <td>1.280000</td>\n", "      <td>1.280000</td>\n", "      <td>77097000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-07-06 00:00:00-04:00</th>\n", "      <td>1.333333</td>\n", "      <td>1.333333</td>\n", "      <td>1.055333</td>\n", "      <td>1.074000</td>\n", "      <td>1.074000</td>\n", "      <td>103003500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               Open      High       Low     Close  Adj Close  \\\n", "Date                                                                           \n", "2010-06-29 00:00:00-04:00  1.266667  1.666667  1.169333  1.592667   1.592667   \n", "2010-06-30 00:00:00-04:00  1.719333  2.028000  1.553333  1.588667   1.588667   \n", "2010-07-01 00:00:00-04:00  1.666667  1.728000  1.351333  1.464000   1.464000   \n", "2010-07-02 00:00:00-04:00  1.533333  1.540000  1.247333  1.280000   1.280000   \n", "2010-07-06 00:00:00-04:00  1.333333  1.333333  1.055333  1.074000   1.074000   \n", "\n", "                              Volume  \n", "Date                                  \n", "2010-06-29 00:00:00-04:00  281494500  \n", "2010-06-30 00:00:00-04:00  257806500  \n", "2010-07-01 00:00:00-04:00  123282000  \n", "2010-07-02 00:00:00-04:00   77097000  \n", "2010-07-06 00:00:00-04:00  103003500  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "78d978bb", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:41:04.055680Z", "iopub.status.busy": "2023-01-23T21:41:04.054839Z", "iopub.status.idle": "2023-01-23T21:41:04.596592Z", "shell.execute_reply": "2023-01-23T21:41:04.595412Z"}, "papermill": {"duration": 0.55394, "end_time": "2023-01-23T21:41:04.599139", "exception": false, "start_time": "2023-01-23T21:41:04.045199", "status": "completed"}, "tags": []}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#Create a simple chart\n", "df.plot(y='Close', title='APPLE STOCK')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "06f49035", "metadata": {"papermill": {"duration": 0.008133, "end_time": "2023-01-23T21:41:04.615999", "exception": false, "start_time": "2023-01-23T21:41:04.607866", "status": "completed"}, "tags": []}, "source": ["<a id=\"3\"></a>\n", "<h1 style=\"background-color:red;font-family:newtimeroman;font-size:250%;text-align:center;border-radius: 10px 10px;\">Creating Technical Analysis Indicators</h1> "]}, {"cell_type": "code", "execution_count": 5, "id": "7be1cd94", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:41:04.635169Z", "iopub.status.busy": "2023-01-23T21:41:04.634344Z", "iopub.status.idle": "2023-01-23T21:41:19.061824Z", "shell.execute_reply": "2023-01-23T21:41:19.059830Z"}, "papermill": {"duration": 14.440728, "end_time": "2023-01-23T21:41:19.065071", "exception": false, "start_time": "2023-01-23T21:41:04.624343", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pandas-ta\r\n", "  Downloading pandas_ta-0.3.14b.tar.gz (115 kB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m115.1/115.1 kB\u001b[0m \u001b[31m628.3 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l-\b \bdone\r\n", "\u001b[?25hRequirement already satisfied: pandas in /opt/conda/lib/python3.7/site-packages (from pandas-ta) (1.3.5)\r\n", "Requirement already satisfied: python-dateutil>=2.7.3 in /opt/conda/lib/python3.7/site-packages (from pandas->pandas-ta) (2.8.2)\r\n", "Requirement already satisfied: pytz>=2017.3 in /opt/conda/lib/python3.7/site-packages (from pandas->pandas-ta) (2022.7.1)\r\n", "Requirement already satisfied: numpy>=1.17.3 in /opt/conda/lib/python3.7/site-packages (from pandas->pandas-ta) (1.21.6)\r\n", "Requirement already satisfied: six>=1.5 in /opt/conda/lib/python3.7/site-packages (from python-dateutil>=2.7.3->pandas->pandas-ta) (1.15.0)\r\n", "Building wheels for collected packages: pandas-ta\r\n", "  Building wheel for pandas-ta (setup.py) ... \u001b[?25l-\b \b\\\b \b|\b \bdone\r\n", "\u001b[?25h  Created wheel for pandas-ta: filename=pandas_ta-0.3.14b0-py3-none-any.whl size=218923 sha256=cbf30fc7732f4bd7910dca5dadacbabcf141c957e333338f05fcf2599c2dd2b9\r\n", "  Stored in directory: /root/.cache/pip/wheels/0b/81/f0/cca85757840e4616a2c6b9fe12569d97d324c27cac60724c58\r\n", "Successfully built pandas-ta\r\n", "Installing collected packages: pandas-ta\r\n", "Successfully installed pandas-ta-0.3.14b0\r\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\r\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install pandas-ta"]}, {"cell_type": "markdown", "id": "a655809f", "metadata": {"papermill": {"duration": 0.009, "end_time": "2023-01-23T21:41:19.083654", "exception": false, "start_time": "2023-01-23T21:41:19.074654", "status": "completed"}, "tags": []}, "source": ["As with any other machine learning model, it is always good to normalize or rescale the data within a fixed range when dealing with real data. This will avoid features with larger numeric values to unjustly interfere and bias the model and help achieve rapid convergence.\n", "\n", "First, we define the features and the target as discussed above."]}, {"cell_type": "code", "execution_count": 6, "id": "68dae084", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:41:19.105107Z", "iopub.status.busy": "2023-01-23T21:41:19.104314Z", "iopub.status.idle": "2023-01-23T21:41:31.667478Z", "shell.execute_reply": "2023-01-23T21:41:31.666084Z"}, "papermill": {"duration": 12.576857, "end_time": "2023-01-23T21:41:31.670130", "exception": false, "start_time": "2023-01-23T21:41:19.093273", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\r\n", "\u001b[0m"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>RSI(2)</th>\n", "      <th>RSI(7)</th>\n", "      <th>RSI(14)</th>\n", "      <th>CCI(30)</th>\n", "      <th>CCI(50)</th>\n", "      <th>CCI(100)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2010-11-17 00:00:00-05:00</th>\n", "      <td>2.013333</td>\n", "      <td>2.050000</td>\n", "      <td>1.907333</td>\n", "      <td>1.966000</td>\n", "      <td>1.966000</td>\n", "      <td>11250000</td>\n", "      <td>42.760531</td>\n", "      <td>69.700236</td>\n", "      <td>70.783665</td>\n", "      <td>150.212834</td>\n", "      <td>236.167196</td>\n", "      <td>362.534702</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-11-18 00:00:00-05:00</th>\n", "      <td>2.044667</td>\n", "      <td>2.049333</td>\n", "      <td>1.928000</td>\n", "      <td>1.992667</td>\n", "      <td>1.992667</td>\n", "      <td>14341500</td>\n", "      <td>63.034012</td>\n", "      <td>71.443507</td>\n", "      <td>71.753149</td>\n", "      <td>137.781371</td>\n", "      <td>214.555090</td>\n", "      <td>345.275945</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-11-19 00:00:00-05:00</th>\n", "      <td>2.010667</td>\n", "      <td>2.091333</td>\n", "      <td>1.980000</td>\n", "      <td>2.066000</td>\n", "      <td>2.066000</td>\n", "      <td>17257500</td>\n", "      <td>87.460641</td>\n", "      <td>75.893265</td>\n", "      <td>74.280617</td>\n", "      <td>138.690153</td>\n", "      <td>211.252585</td>\n", "      <td>361.454088</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-11-22 00:00:00-05:00</th>\n", "      <td>2.104667</td>\n", "      <td>2.230000</td>\n", "      <td>2.100000</td>\n", "      <td>2.226667</td>\n", "      <td>2.226667</td>\n", "      <td>22945500</td>\n", "      <td>96.781036</td>\n", "      <td>82.759912</td>\n", "      <td>78.763949</td>\n", "      <td>160.222337</td>\n", "      <td>233.454931</td>\n", "      <td>403.225178</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-11-23 00:00:00-05:00</th>\n", "      <td>2.219333</td>\n", "      <td>2.378667</td>\n", "      <td>2.146000</td>\n", "      <td>2.304667</td>\n", "      <td>2.304667</td>\n", "      <td>23667000</td>\n", "      <td>98.130358</td>\n", "      <td>85.154907</td>\n", "      <td>80.537681</td>\n", "      <td>164.201763</td>\n", "      <td>233.880206</td>\n", "      <td>405.588261</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               Open      High       Low     Close  Adj Close  \\\n", "Date                                                                           \n", "2010-11-17 00:00:00-05:00  2.013333  2.050000  1.907333  1.966000   1.966000   \n", "2010-11-18 00:00:00-05:00  2.044667  2.049333  1.928000  1.992667   1.992667   \n", "2010-11-19 00:00:00-05:00  2.010667  2.091333  1.980000  2.066000   2.066000   \n", "2010-11-22 00:00:00-05:00  2.104667  2.230000  2.100000  2.226667   2.226667   \n", "2010-11-23 00:00:00-05:00  2.219333  2.378667  2.146000  2.304667   2.304667   \n", "\n", "                             Volume     RSI(2)     RSI(7)    RSI(14)  \\\n", "Date                                                                   \n", "2010-11-17 00:00:00-05:00  11250000  42.760531  69.700236  70.783665   \n", "2010-11-18 00:00:00-05:00  14341500  63.034012  71.443507  71.753149   \n", "2010-11-19 00:00:00-05:00  17257500  87.460641  75.893265  74.280617   \n", "2010-11-22 00:00:00-05:00  22945500  96.781036  82.759912  78.763949   \n", "2010-11-23 00:00:00-05:00  23667000  98.130358  85.154907  80.537681   \n", "\n", "                              CCI(30)     CCI(50)    CCI(100)  \n", "Date                                                           \n", "2010-11-17 00:00:00-05:00  150.212834  236.167196  362.534702  \n", "2010-11-18 00:00:00-05:00  137.781371  214.555090  345.275945  \n", "2010-11-19 00:00:00-05:00  138.690153  211.252585  361.454088  \n", "2010-11-22 00:00:00-05:00  160.222337  233.454931  403.225178  \n", "2010-11-23 00:00:00-05:00  164.201763  233.880206  405.588261  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["try:\n", "  import pandas_ta as ta\n", "except:\n", "  !pip install -q pandas-ta\n", "  import pandas_ta as ta \n", "\n", "df['RSI(2)'] = ta.rsi(df['Close'],length=2)\n", "df['RSI(7)'] = ta.rsi(df['Close'],length=7)\n", "df['RSI(14)'] = ta.rsi(df['Close'],length=14)\n", "df['CCI(30)'] = ta.cci(close=df['Close'],length=30,high=df['High'],low=df['Low'])\n", "df['CCI(50)'] = ta.cci(close=df['Close'],length=50,high=df['High'],low=df['Low'])\n", "df['CCI(100)'] = ta.cci(close=df['Close'],length=100,high=df['High'],low=df['Low'])\n", "\n", "#Drop NaN Values\n", "df = df.dropna()\n", "\n", "#Create a plot showing some of our indicators\n", "df.plot(y='RSI(2)')\n", "df.plot(y='CCI(100)')\n", "\n", "#How's our current dataframe going\n", "df.head()"]}, {"cell_type": "markdown", "id": "a36ce36d", "metadata": {"papermill": {"duration": 0.010185, "end_time": "2023-01-23T21:41:31.691310", "exception": false, "start_time": "2023-01-23T21:41:31.681125", "status": "completed"}, "tags": []}, "source": ["Prepare our data labelling\n", "\n", "What do we want to do over here?\n", "\n", "When we place our trade: Buy at Open (over the next day). We want to close our trade: At the next day at open.\n", "\n", "Formula:\n", "\n", "Handling the returns this ways: Buy: Open(-1) - Closing: Open(-2)\n", "\n", "We want to label our data in the following day.\n", "\n", "BUY SIGNAL: Open(-2) > Open(-1)\n", "\n", "SELL SIGNAL: Open(-2) < Open(-1)\n", "\n", "Indicators the current situations, and our labels take a look ahead of time to determine if we have a pattern."]}, {"cell_type": "code", "execution_count": 7, "id": "c3fdc0f7", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:41:31.714569Z", "iopub.status.busy": "2023-01-23T21:41:31.714136Z", "iopub.status.idle": "2023-01-23T21:41:31.738222Z", "shell.execute_reply": "2023-01-23T21:41:31.736981Z"}, "papermill": {"duration": 0.039019, "end_time": "2023-01-23T21:41:31.740882", "exception": false, "start_time": "2023-01-23T21:41:31.701863", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th><PERSON><PERSON> <PERSON></th>\n", "      <th>Volume</th>\n", "      <th>RSI(2)</th>\n", "      <th>RSI(7)</th>\n", "      <th>RSI(14)</th>\n", "      <th>CCI(30)</th>\n", "      <th>CCI(50)</th>\n", "      <th>CCI(100)</th>\n", "      <th>LABEL</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2010-11-17 00:00:00-05:00</th>\n", "      <td>2.013333</td>\n", "      <td>2.050000</td>\n", "      <td>1.907333</td>\n", "      <td>1.966000</td>\n", "      <td>1.966000</td>\n", "      <td>11250000</td>\n", "      <td>42.760531</td>\n", "      <td>69.700236</td>\n", "      <td>70.783665</td>\n", "      <td>150.212834</td>\n", "      <td>236.167196</td>\n", "      <td>362.534702</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-11-18 00:00:00-05:00</th>\n", "      <td>2.044667</td>\n", "      <td>2.049333</td>\n", "      <td>1.928000</td>\n", "      <td>1.992667</td>\n", "      <td>1.992667</td>\n", "      <td>14341500</td>\n", "      <td>63.034012</td>\n", "      <td>71.443507</td>\n", "      <td>71.753149</td>\n", "      <td>137.781371</td>\n", "      <td>214.555090</td>\n", "      <td>345.275945</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-11-19 00:00:00-05:00</th>\n", "      <td>2.010667</td>\n", "      <td>2.091333</td>\n", "      <td>1.980000</td>\n", "      <td>2.066000</td>\n", "      <td>2.066000</td>\n", "      <td>17257500</td>\n", "      <td>87.460641</td>\n", "      <td>75.893265</td>\n", "      <td>74.280617</td>\n", "      <td>138.690153</td>\n", "      <td>211.252585</td>\n", "      <td>361.454088</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-11-22 00:00:00-05:00</th>\n", "      <td>2.104667</td>\n", "      <td>2.230000</td>\n", "      <td>2.100000</td>\n", "      <td>2.226667</td>\n", "      <td>2.226667</td>\n", "      <td>22945500</td>\n", "      <td>96.781036</td>\n", "      <td>82.759912</td>\n", "      <td>78.763949</td>\n", "      <td>160.222337</td>\n", "      <td>233.454931</td>\n", "      <td>403.225178</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-11-23 00:00:00-05:00</th>\n", "      <td>2.219333</td>\n", "      <td>2.378667</td>\n", "      <td>2.146000</td>\n", "      <td>2.304667</td>\n", "      <td>2.304667</td>\n", "      <td>23667000</td>\n", "      <td>98.130358</td>\n", "      <td>85.154907</td>\n", "      <td>80.537681</td>\n", "      <td>164.201763</td>\n", "      <td>233.880206</td>\n", "      <td>405.588261</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               Open      High       Low     Close  Adj Close  \\\n", "Date                                                                           \n", "2010-11-17 00:00:00-05:00  2.013333  2.050000  1.907333  1.966000   1.966000   \n", "2010-11-18 00:00:00-05:00  2.044667  2.049333  1.928000  1.992667   1.992667   \n", "2010-11-19 00:00:00-05:00  2.010667  2.091333  1.980000  2.066000   2.066000   \n", "2010-11-22 00:00:00-05:00  2.104667  2.230000  2.100000  2.226667   2.226667   \n", "2010-11-23 00:00:00-05:00  2.219333  2.378667  2.146000  2.304667   2.304667   \n", "\n", "                             Volume     RSI(2)     RSI(7)    RSI(14)  \\\n", "Date                                                                   \n", "2010-11-17 00:00:00-05:00  11250000  42.760531  69.700236  70.783665   \n", "2010-11-18 00:00:00-05:00  14341500  63.034012  71.443507  71.753149   \n", "2010-11-19 00:00:00-05:00  17257500  87.460641  75.893265  74.280617   \n", "2010-11-22 00:00:00-05:00  22945500  96.781036  82.759912  78.763949   \n", "2010-11-23 00:00:00-05:00  23667000  98.130358  85.154907  80.537681   \n", "\n", "                              CCI(30)     CCI(50)    CCI(100) LABEL  \n", "Date                                                                 \n", "2010-11-17 00:00:00-05:00  150.212834  236.167196  362.534702     0  \n", "2010-11-18 00:00:00-05:00  137.781371  214.555090  345.275945     1  \n", "2010-11-19 00:00:00-05:00  138.690153  211.252585  361.454088     1  \n", "2010-11-22 00:00:00-05:00  160.222337  233.454931  403.225178     1  \n", "2010-11-23 00:00:00-05:00  164.201763  233.880206  405.588261     1  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "df['LABEL'] = np.where( df['Open'].shift(-2).gt(df['Open'].shift(-1)),\"1\",\"0\")\n", "df = df.dropna()\n", "df.head()"]}, {"cell_type": "markdown", "id": "1d07e852", "metadata": {"papermill": {"duration": 0.010708, "end_time": "2023-01-23T21:41:31.762585", "exception": false, "start_time": "2023-01-23T21:41:31.751877", "status": "completed"}, "tags": []}, "source": ["<a id=\"4\"></a>\n", "<h1 style=\"background-color:red;font-family:newtimeroman;font-size:250%;text-align:center;border-radius: 10px 10px;\">Creating a Neural Network</h1> "]}, {"cell_type": "markdown", "id": "cf87d246", "metadata": {"papermill": {"duration": 0.010617, "end_time": "2023-01-23T21:41:31.784189", "exception": false, "start_time": "2023-01-23T21:41:31.773572", "status": "completed"}, "tags": []}, "source": ["Train and Test Sets for Stock Price Prediction\n", "We split our data into training and testing sets. Shuffling is not permitted in time-series datasets. In the beginning, we take two steps worth of past data to predict the current value. Thus, the model will look at yesterday’s and today’s values to predict today’s closing price."]}, {"cell_type": "code", "execution_count": 8, "id": "80f631f1", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:41:31.807865Z", "iopub.status.busy": "2023-01-23T21:41:31.807467Z", "iopub.status.idle": "2023-01-23T21:41:34.206388Z", "shell.execute_reply": "2023-01-23T21:41:34.204510Z"}, "papermill": {"duration": 2.413946, "end_time": "2023-01-23T21:41:34.209044", "exception": false, "start_time": "2023-01-23T21:41:31.795098", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Train Data Accuracy \n", "              precision    recall  f1-score   support\n", "\n", "           0       0.55      0.31      0.39      1022\n", "           1       0.55      0.77      0.64      1122\n", "\n", "    accuracy                           0.55      2144\n", "   macro avg       0.55      0.54      0.52      2144\n", "weighted avg       0.55      0.55      0.52      2144\n", "\n", " Testing Data Accuracy \n", "              precision    recall  f1-score   support\n", "\n", "           0       0.48      0.26      0.33       454\n", "           1       0.50      0.73      0.59       466\n", "\n", "    accuracy                           0.49       920\n", "   macro avg       0.49      0.49      0.46       920\n", "weighted avg       0.49      0.49      0.46       920\n", "\n"]}], "source": ["import sklearn\n", "from sklearn.neural_network import MLPClassifier\n", "from sklearn.neural_network import MLPRegressor \n", "from sklearn.model_selection import train_test_split \n", "from sklearn.metrics import mean_squared_error \n", "from math import sqrt  \n", "from sklearn.metrics import r2_score \n", "\n", "#Fitting - Indicators and our Labelling\n", "X = df[df.columns[6:-1]].values \n", "y = df['LABEL'].values\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3) \n", "\n", "mlp = MLPClassifier(hidden_layer_sizes=(8,8,8), activation='relu', solver='adam', max_iter=1000) \n", "mlp.fit(X_train,y_train) \n", "\n", "predict_train = mlp.predict(X_train) \n", "predict_test = mlp.predict(X_test)\n", "\n", "from sklearn.metrics import classification_report,confusion_matrix \n", "\n", "print(' Train Data Accuracy ')\n", "print(classification_report(y_train,predict_train)) \n", "\n", "print( ' Testing Data Accuracy ' )\n", "print( classification_report(y_test,predict_test) )"]}, {"cell_type": "markdown", "id": "8bc4ce02", "metadata": {"papermill": {"duration": 0.011238, "end_time": "2023-01-23T21:41:34.231557", "exception": false, "start_time": "2023-01-23T21:41:34.220319", "status": "completed"}, "tags": []}, "source": ["Given the simplicity of the model and the data, we note that the loss reduction stagnates after only 20 epochs. You can observe this by plotting the training loss against the number of epochs, and LSTM does not learn much after 10-20 epochs."]}, {"cell_type": "markdown", "id": "d7c03e83", "metadata": {"papermill": {"duration": 0.010918, "end_time": "2023-01-23T21:41:34.253862", "exception": false, "start_time": "2023-01-23T21:41:34.242944", "status": "completed"}, "tags": []}, "source": ["<a id=\"5\"></a>\n", "<h1 style=\"background-color:red;font-family:newtimeroman;font-size:250%;text-align:center;border-radius: 10px 10px;\">Backtesting our Model</h1> "]}, {"cell_type": "code", "execution_count": 9, "id": "ddb5d14d", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:41:34.279835Z", "iopub.status.busy": "2023-01-23T21:41:34.279107Z", "iopub.status.idle": "2023-01-23T21:41:34.645796Z", "shell.execute_reply": "2023-01-23T21:41:34.644745Z"}, "papermill": {"duration": 0.382516, "end_time": "2023-01-23T21:41:34.648847", "exception": false, "start_time": "2023-01-23T21:41:34.266331", "status": "completed"}, "tags": []}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["df['Prediction'] = np.append(predict_train,predict_test)\n", "df['Strategy Returns'] = np.where( df['Prediction'].eq(\"1\"),df['Open'].shift(-2)-df['Open'].shift(-1),0)\n", "\n", "df['Strategy Returns'] = df['Strategy Returns'].cumsum() \n", "\n", "df.plot(y='Strategy Returns')\n", "plt.plot()"]}, {"cell_type": "code", "execution_count": 10, "id": "5bde3637", "metadata": {"execution": {"iopub.execute_input": "2023-01-23T21:41:34.674590Z", "iopub.status.busy": "2023-01-23T21:41:34.674125Z", "iopub.status.idle": "2023-01-23T21:41:34.681225Z", "shell.execute_reply": "2023-01-23T21:41:34.680003Z"}, "papermill": {"duration": 0.023146, "end_time": "2023-01-23T21:41:34.683939", "exception": false, "start_time": "2023-01-23T21:41:34.660793", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Today's return forecast: UP\n"]}], "source": ["prediction = df.iloc[-1]['Prediction'] \n", "\n", "if prediction==\"1\":\n", "  print(\"Today's return forecast: UP\")\n", "else: \n", "  print(\"Today's return forecast: DOWN\")"]}, {"cell_type": "markdown", "id": "5e715b9d", "metadata": {"papermill": {"duration": 0.011418, "end_time": "2023-01-23T21:41:34.707424", "exception": false, "start_time": "2023-01-23T21:41:34.696006", "status": "completed"}, "tags": []}, "source": ["<a id=\"6\"></a>\n", "<h1 style=\"background-color:red;font-family:newtimeroman;font-size:250%;text-align:center;border-radius: 10px 10px;\">Author Message</h1>"]}, {"cell_type": "markdown", "id": "23d1cfd5", "metadata": {"papermill": {"duration": 0.011644, "end_time": "2023-01-23T21:41:34.730961", "exception": false, "start_time": "2023-01-23T21:41:34.719317", "status": "completed"}, "tags": []}, "source": ["We looked at how we can address the problem of predicting stock market prices by considering stock market data as a time series. Further, we looked into the theory of three existing methods used frequently for time series forecasting: simple moving average (SMA), exponential moving average (EMA), and LSTMs. We also used real-life stock data to predict prices of the Tesla stock using these three methods and conducted a comparative performance analysis using RMSE and MAPE error metrics.\n", "\n", "However, a consensus of experts from the financial domain and the AI field says that ML techniques perform poorly in the real world to predict the market.  \n", "\n", "Even if hundreds of variables and real-world market drivers are quantized and incorporated in the data and optimized using the best machine learning methods available, the model would still fall short of making valuable predictions when they matter. Experts note that AI models could not follow the trends disrupted by the COVID-19 pandemic – not during or even towards the end of it. Similarly, in general cases also, AI falls short of substituting human intelligence and intuition about the market trends: Effectiveness of Artificial Intelligence in Stock Market Prediction based on Machine Learning. Nevertheless, these shortcomings are only learning curves for developing robust stock price prediction models and analyzing the capabilities of existing models in further detail."]}, {"cell_type": "code", "execution_count": null, "id": "225d62e2", "metadata": {"papermill": {"duration": 0.011412, "end_time": "2023-01-23T21:41:34.754209", "exception": false, "start_time": "2023-01-23T21:41:34.742797", "status": "completed"}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.12"}, "papermill": {"default_parameters": {}, "duration": 70.983686, "end_time": "2023-01-23T21:41:35.587860", "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2023-01-23T21:40:24.604174", "version": "2.3.4"}}, "nbformat": 4, "nbformat_minor": 5}