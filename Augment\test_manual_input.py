"""
Test Manual Input Fix

This script tests if the manual input is working properly.
"""

def test_input_loop():
    """Test the input loop functionality"""
    print("🧪 Testing Manual Input Loop")
    print("="*40)
    
    print("Testing mode selection...")
    
    while True:
        try:
            print("📈 Available Modes:")
            print("   1. Historical Backtest")
            print("   2. Live Market Monitor")
            mode = input("📈 Choose mode (1 or 2): ").strip()
            if mode in ['1', '2']:
                print(f"✅ Selected: {'Historical Backtest' if mode == '1' else 'Live Market Monitor'}")
                break
            elif mode == '':
                mode = '1'
                print("✅ Using default: Historical Backtest")
                break
            else:
                print("❌ Please enter 1 or 2")
                continue
        except (EOFError, KeyboardInterrupt):
            print("\n⚠️ Input interrupted, using default: Historical Backtest")
            mode = '1'
            break
    
    print(f"\n✅ Mode selection test completed. Selected: {mode}")
    
    # Test exchange selection
    print("\nTesting exchange selection...")
    
    exchange_map = {'1': 'NSE', '2': 'BSE', '3': 'MCX', '4': 'NFO'}
    
    print("🏛️ Available Exchanges:")
    print("   1. NSE (National Stock Exchange)")
    print("   2. BSE (Bombay Stock Exchange)")
    print("   3. MCX (Multi Commodity Exchange)")
    print("   4. NFO (NSE Futures & Options)")
    print("   5. Custom (Enter manually)")
    
    while True:
        try:
            exchange_choice = input("🏛️ Select exchange (1-5): ").strip()
            if exchange_choice in ['1', '2', '3', '4', '5']:
                print(f"✅ Selected: {exchange_map.get(exchange_choice, 'Custom')}")
                break
            elif exchange_choice == '':
                exchange_choice = '1'
                print("✅ Using default: NSE")
                break
            else:
                print("❌ Please enter 1, 2, 3, 4, or 5")
                continue
        except (EOFError, KeyboardInterrupt):
            print("\n⚠️ Input interrupted, using default: NSE")
            exchange_choice = '1'
            break
    
    print(f"\n✅ Exchange selection test completed. Selected: {exchange_choice}")
    
    # Test ticker input
    print("\nTesting ticker input...")
    
    while True:
        try:
            tickers_input = input("🏢 Enter tickers (e.g., BATAINDIA): ").strip()
            if tickers_input:
                print(f"✅ Selected: {tickers_input}")
                break
            else:
                print("❌ Please enter at least one ticker")
                continue
        except (EOFError, KeyboardInterrupt):
            print("\n⚠️ Input interrupted, using default: BATAINDIA")
            tickers_input = "BATAINDIA"
            break
    
    print(f"\n✅ Ticker input test completed. Selected: {tickers_input}")
    
    print(f"\n🎉 All input tests completed successfully!")
    print(f"📋 Results:")
    print(f"   Mode: {mode}")
    print(f"   Exchange: {exchange_choice}")
    print(f"   Tickers: {tickers_input}")

def main():
    """Run the manual input test"""
    print("🚀 MANUAL INPUT TEST")
    print("="*30)
    
    print("This test will check if the input loops are working properly.")
    print("You should be able to enter your choices manually.")
    print("Press Ctrl+C to test interrupt handling.")
    print()
    
    try:
        test_input_loop()
        print("\n✅ Manual input is working correctly!")
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")

if __name__ == "__main__":
    main()
