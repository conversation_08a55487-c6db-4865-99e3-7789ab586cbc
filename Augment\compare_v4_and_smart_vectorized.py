import subprocess
import json
import os
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import math

# Import the necessary functions and classes
from smart_vectorized_backtester import SmartVectorizedBacktester
from enhanced_nadarya_watson_signal import check_vander_enhanced
from shared_sideways_signal_helper import check_sideways
from shared_api_manager import get_api

def run_v4_logic_minute_by_minute(ticker, date, start_time, end_time, tokenid):
    """
    Simulates the V4 logic on a minute-by-minute basis to generate a comparable signal stream.
    """
    print(f"Running v4 logic minute-by-minute for {ticker} on {date}...")
    
    from enhanced_nadarya_watson_signal import get_start_end_timestamps, live_data
    
    api = get_api()
    start_timestamp, end_timestamp = get_start_end_timestamps(date, start_time, end_time)
    
    data = api.get_time_price_series(
        exchange='NSE',
        token=tokenid,
        starttime=start_timestamp,
        endtime=end_timestamp,
        interval=1
    )

    if data is None:
        raise ValueError(f"No data available for {ticker}")

    full_data = live_data(data)
    if full_data is None or full_data.empty:
        raise ValueError(f"No valid data available for {ticker}")

    full_data = full_data.sort_values(by='time')

    market_start = datetime.strptime(f"{date} {start_time}", '%d-%m-%Y %H:%M')
    market_end = datetime.strptime(f"{date} {end_time}", '%d-%m-%Y %H:%M')
    
    current_time = market_start
    all_minutes = []
    while current_time <= market_end:
        all_minutes.append(current_time)
        current_time += timedelta(minutes=1)
        
    signals = []
    
    for minute_time in all_minutes:
        time_str = minute_time.strftime('%H:%M')
        
        # This is the crucial part: for each minute, we call the v4 functions
        # with the data available up to that point in time.
        
        is_sideways, sideways_text = check_sideways(
            tokenid=tokenid,
            exchange='NSE',
            date_input=date,
            starttime_input=start_time,
            endtime_input=time_str 
        )
        
        is_vander, vander_text = check_vander_enhanced(
            tokenid=tokenid,
            exchange='NSE',
            date_input=date,
            starttime_input=start_time,
            endtime_input=time_str,
            enable_momentum_validation=True,
            enable_realtime_detection=True
        )
        
        if is_sideways and is_vander:
            # The v4 logic doesn't distinguish between CALL and PUT, it just gives a signal.
            # We will infer the signal type from the Nadarya Watson text.
            signal_type = "UNKNOWN"
            if "Upper band" in vander_text:
                signal_type = "PUT"
            elif "Lower band" in vander_text:
                signal_type = "CALL"

            signals.append({
                'time': time_str,
                'signal_type': signal_type,
                'reason': f"Sideways: {sideways_text}, Nadarya: {vander_text}"
            })

    return signals

def run_smart_vectorized_backtester(ticker, date, start_time, end_time, tokenid):
    """
    Runs the smart_vectorized_backtester.py and returns the generated signals.
    """
    print(f"Running smart_vectorized_backtester for {ticker} on {date}...")
    try:
        backtester = SmartVectorizedBacktester(
            ticker=ticker,
            exchange='NSE',
            start=start_time,
            end=end_time,
            date=date,
            tokenid=tokenid,
            enable_momentum_validation=True,
            enable_realtime_detection=True
        )
        signals = backtester.run_smart_vectorized_backtest()
        return signals
    except Exception as e:
        print(f"Error running smart vectorized backtester: {e}")
        return []

def compare_results(v4_signals, smart_signals):
    """
    Compares the results from both backtesters.
    """
    print("\n" + "="*100)
    print("COMPARISON REPORT")
    print("="*100)

    if not v4_signals:
        print("No signals from V4 logic to compare.")
        return

    if not smart_signals:
        print("No signals from smart_vectorized_backtester to compare.")
        return

    v4_signals_map = {s['time']: s['signal_type'] for s in v4_signals}
    smart_signals_map = {s['time']: s['signal_type'] for s in smart_signals}

    all_times = sorted(list(set(v4_signals_map.keys()) | set(smart_signals_map.keys())))
    
    comparison_data = []
    for t in all_times:
        v4_signal = v4_signals_map.get(t, "NO SIGNAL")
        smart_signal = smart_signals_map.get(t, "NO SIGNAL")
        match = v4_signal == smart_signal
        
        comparison_data.append({
            "Time": t,
            "V4_Signal": v4_signal,
            "Smart_Vectorized_Signal": smart_signal,
            "Match": "✅" if match else "❌"
        })

    df = pd.DataFrame(comparison_data)
    
    print("Signal Comparison (Minute-by-Minute):")
    print(df.to_string())

    print("\n" + "="*100)
    print("ANALYSIS")
    print("="*100)

    num_mismatches = len(df[df['Match'] == "❌"])
    if num_mismatches == 0:
        print("✅ The signals from both backtesters are identical on a minute-by-minute basis.")
    else:
        print(f"❌ Found {num_mismatches} mismatches in signals.")
        print("Mismatched signals:")
        print(df[df['Match'] == "❌"].to_string())

    print("\n" + "="*100)
    print("SIGNAL GENERATION LOGIC")
    print("="*100)
    print("Backtester V4 (run_backtester_v4_standalone.py):")
    print("- **How it's calculated:** At each minute, it looks at all the data from the start of the day up to that minute.")
    print("- It checks for two conditions: 'sideways' market and a 'Nadarya-Watson' signal.")
    print("- A signal is generated ONLY if BOTH conditions are met.")
    print("- **Sideways Check:** Looks for low volatility, stable price oscillation, and no strong trend in the recent past (last 20 minutes).")
    print("- **Nadarya-Watson Check:** Uses a statistical method to create upper and lower price bands. A signal occurs if the price touches or crosses one of these bands.")
    print("- **Accuracy:** This method is robust as it requires multiple conditions to be met, but it might miss signals if one of the conditions is not met, even if the other is strong.")

    print("\nSmart Vectorized Backtester (smart_vectorized_backtester.py):")
    print("- **How it's calculated:** It's designed to be a more efficient and 'smarter' version of the V4 logic.")
    print("- It also checks for 'sideways' and 'Nadarya-Watson' signals at each minute, using the data from the start of the day up to that minute.")
    print("- **Key Difference:** The 'smart' version includes additional 'momentum validation'. Even if the price touches the Nadarya-Watson band, it checks if there's enough momentum in the price movement to confirm the signal. This is to avoid false signals when the price just briefly touches the band and then reverses.")
    print("- **Accuracy:** The momentum validation is designed to make the signals more reliable and accurate, filtering out weak or false signals. This means it might generate fewer signals than the V4 logic, but the ones it does generate are expected to be of higher quality.")

    print("\n" + "="*100)
    print("CONCLUSION")
    print("="*100)
    if num_mismatches == 0:
        print("Both backtesters are perfectly in sync. The 'smart_vectorized_backtester' is likely a more refined version with the same core logic but added momentum validation for higher accuracy.")
    else:
        print("The backtesters are NOT in sync. The differences are likely due to the 'momentum validation' in the 'smart_vectorized_backtester'.")
        print("If the 'smart_vectorized_backtester' shows 'NO SIGNAL' when V4 shows a signal, it's likely because the momentum validation filtered out a weak signal.")
        print("The 'smart_vectorized_backtester' is likely the more accurate and reliable of the two, as it's designed to avoid false signals.")

if __name__ == "__main__":
    TICKER = "BATAINDIA"
    DATE = "20-06-2025"
    START_TIME = "09:15"
    END_TIME = "15:30"

    # Get token for the ticker
    api = get_api()
    search_result = api.searchscrip(exchange='NSE', searchtext=TICKER + '-EQ')
    if not (search_result and 'values' in search_result and search_result['values']):
        print(f"Could not find token for {TICKER}")
        exit()
    token = search_result['values'][0]['token']

    # Run both backtesters
    v4_signals = run_v4_logic_minute_by_minute(TICKER, DATE, START_TIME, END_TIME, token)
    smart_signals = run_smart_vectorized_backtester(TICKER, DATE, START_TIME, END_TIME, token)

    # Compare the results
    compare_results(v4_signals, smart_signals)
