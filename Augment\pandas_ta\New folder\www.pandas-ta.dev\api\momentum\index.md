---
author: Pandas TA
description: Pandas TA list of Momentum Indicators
generator: mkdocs-1.6.1, mkdocs-material-9.6.14
lang: en
title: Momentum - Pandas TA
viewport: width=device-width,initial-scale=1
---

::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: {.md-main role="main" md-component="main"}
:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: {.md-main__inner .md-grid}
:::::: {.md-sidebar .md-sidebar--primary md-component="sidebar" md-type="navigation"}
::::: md-sidebar__scrollwrap
:::: md-sidebar__inner
[![](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdib3g9IjAgMCAyNCAyNCI+PHBhdGggZD0ibTE2IDYgMi4yOSAyLjI5LTQuODggNC44OC00LTRMMiAxNi41OSAzLjQxIDE4bDYtNiA0IDQgNi4zLTYuMjlMMjIgMTJWNnoiIC8+PC9zdmc+)](../../index.html "Pandas TA"){.md-nav__button
.md-logo aria-label="Pandas TA" md-component="logo"} Pandas TA

[[ Home ]{.md-ellipsis} []{.md-nav__icon
.md-icon}](../../index.html){.md-nav__link}

[[ Support ]{.md-ellipsis} []{.md-nav__icon
.md-icon}](../../support/index.html){.md-nav__link}

::: {.md-nav__link .md-nav__container}
[[ Documentation ]{.md-ellipsis}](../index.html){.md-nav__link}
[]{.md-nav__icon .md-icon}
:::

[]{.md-nav__icon .md-icon} Documentation

[[ DataFrame Extension
]{.md-ellipsis}](../ta-extension/index.html){.md-nav__link}

[[ Studies ]{.md-ellipsis}](../studies/index.html){.md-nav__link}

[[ Events ]{.md-ellipsis}](../events/index.html){.md-nav__link}

[[ Custom Directory
]{.md-ellipsis}](../custom/index.html){.md-nav__link}

[ Indicators ]{.md-ellipsis} []{.md-nav__icon .md-icon}

[]{.md-nav__icon .md-icon} Indicators

[[ Candle ]{.md-ellipsis}](../candle/index.html){.md-nav__link}

[[ Cycle ]{.md-ellipsis}](../cycle/index.html){.md-nav__link}

[ Momentum ]{.md-ellipsis} []{.md-nav__icon .md-icon} [[ Momentum
]{.md-ellipsis}](index.html){.md-nav__link .md-nav__link--active}

[]{.md-nav__icon .md-icon} Table of contents

- [[  ao
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.ao.ao){.md-nav__link}
- [[  apo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.apo.apo){.md-nav__link}
- [[  bias
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.bias.bias){.md-nav__link}
- [[  bop
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.bop.bop){.md-nav__link}
- [[  brar
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.brar.brar){.md-nav__link}
- [[  cci
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cci.cci){.md-nav__link}
- [[  cfo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cfo.cfo){.md-nav__link}
- [[  cg
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cg.cg){.md-nav__link}
- [[  cmo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cmo.cmo){.md-nav__link}
- [[  coppock
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.coppock.coppock){.md-nav__link}
- [[  crsi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.crsi.crsi){.md-nav__link}
- [[  cti
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cti.cti){.md-nav__link}
- [[  dm
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.dm.dm){.md-nav__link}
- [[  er
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.er.er){.md-nav__link}
- [[  eri
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.eri.eri){.md-nav__link}
- [[  exhc
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.exhc.exhc){.md-nav__link}
- [[  fisher
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.fisher.fisher){.md-nav__link}
- [[  inertia
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.inertia.inertia){.md-nav__link}
- [[  kdj
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.kdj.kdj){.md-nav__link}
- [[  kst
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.kst.kst){.md-nav__link}
- [[  macd
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.macd.macd){.md-nav__link}
- [[  mom
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.mom.mom){.md-nav__link}
- [[  pgo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.pgo.pgo){.md-nav__link}
- [[  ppo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.ppo.ppo){.md-nav__link}
- [[  psl
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.psl.psl){.md-nav__link}
- [[  qqe
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.qqe.qqe){.md-nav__link}
- [[  roc
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.roc.roc){.md-nav__link}
- [[  rsi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.rsi.rsi){.md-nav__link}
- [[  rsx
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.rsx.rsx){.md-nav__link}
- [[  rvgi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.rvgi.rvgi){.md-nav__link}
- [[  slope
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.slope.slope){.md-nav__link}
- [[  smc
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.smc.smc){.md-nav__link}
- [[  smi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.smi.smi){.md-nav__link}
- [[  squeeze
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.squeeze.squeeze){.md-nav__link}
- [[  squeeze_pro
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.squeeze_pro.squeeze_pro){.md-nav__link}
- [[  stc
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.stc.stc){.md-nav__link}
- [[  stoch
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.stoch.stoch){.md-nav__link}
- [[  stochf
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.stochf.stochf){.md-nav__link}
- [[  stochrsi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.stochrsi.stochrsi){.md-nav__link}
- [[  tmo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.tmo.tmo){.md-nav__link}
- [[  trix
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.trix.trix){.md-nav__link}
- [[  tsi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.tsi.tsi){.md-nav__link}
- [[  uo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.uo.uo){.md-nav__link}
- [[  willr
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.willr.willr){.md-nav__link}

[[ Overlap ]{.md-ellipsis}](../overlap/index.html){.md-nav__link}

[[ Performance
]{.md-ellipsis}](../performance/index.html){.md-nav__link}

[[ Statistics ]{.md-ellipsis}](../statistics/index.html){.md-nav__link}

[[ Trend ]{.md-ellipsis}](../trend/index.html){.md-nav__link}

[[ Volatility ]{.md-ellipsis}](../volatility/index.html){.md-nav__link}

[[ Volume ]{.md-ellipsis}](../volume/index.html){.md-nav__link}

[[ Utilities ]{.md-ellipsis}](../utilities/index.html){.md-nav__link}

[[ Legal ]{.md-ellipsis} []{.md-nav__icon
.md-icon}](../../legal/index.html){.md-nav__link}
::::
:::::
::::::

::::: {.md-sidebar .md-sidebar--secondary md-component="sidebar" md-type="toc"}
:::: md-sidebar__scrollwrap
::: md-sidebar__inner
[]{.md-nav__icon .md-icon} Table of contents

- [[  ao
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.ao.ao){.md-nav__link}
- [[  apo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.apo.apo){.md-nav__link}
- [[  bias
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.bias.bias){.md-nav__link}
- [[  bop
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.bop.bop){.md-nav__link}
- [[  brar
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.brar.brar){.md-nav__link}
- [[  cci
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cci.cci){.md-nav__link}
- [[  cfo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cfo.cfo){.md-nav__link}
- [[  cg
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cg.cg){.md-nav__link}
- [[  cmo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cmo.cmo){.md-nav__link}
- [[  coppock
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.coppock.coppock){.md-nav__link}
- [[  crsi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.crsi.crsi){.md-nav__link}
- [[  cti
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.cti.cti){.md-nav__link}
- [[  dm
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.dm.dm){.md-nav__link}
- [[  er
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.er.er){.md-nav__link}
- [[  eri
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.eri.eri){.md-nav__link}
- [[  exhc
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.exhc.exhc){.md-nav__link}
- [[  fisher
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.fisher.fisher){.md-nav__link}
- [[  inertia
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.inertia.inertia){.md-nav__link}
- [[  kdj
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.kdj.kdj){.md-nav__link}
- [[  kst
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.kst.kst){.md-nav__link}
- [[  macd
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.macd.macd){.md-nav__link}
- [[  mom
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.mom.mom){.md-nav__link}
- [[  pgo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.pgo.pgo){.md-nav__link}
- [[  ppo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.ppo.ppo){.md-nav__link}
- [[  psl
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.psl.psl){.md-nav__link}
- [[  qqe
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.qqe.qqe){.md-nav__link}
- [[  roc
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.roc.roc){.md-nav__link}
- [[  rsi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.rsi.rsi){.md-nav__link}
- [[  rsx
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.rsx.rsx){.md-nav__link}
- [[  rvgi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.rvgi.rvgi){.md-nav__link}
- [[  slope
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.slope.slope){.md-nav__link}
- [[  smc
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.smc.smc){.md-nav__link}
- [[  smi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.smi.smi){.md-nav__link}
- [[  squeeze
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.squeeze.squeeze){.md-nav__link}
- [[  squeeze_pro
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.squeeze_pro.squeeze_pro){.md-nav__link}
- [[  stc
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.stc.stc){.md-nav__link}
- [[  stoch
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.stoch.stoch){.md-nav__link}
- [[  stochf
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.stochf.stochf){.md-nav__link}
- [[  stochrsi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.stochrsi.stochrsi){.md-nav__link}
- [[  tmo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.tmo.tmo){.md-nav__link}
- [[  trix
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.trix.trix){.md-nav__link}
- [[  tsi
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.tsi.tsi){.md-nav__link}
- [[  uo
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.uo.uo){.md-nav__link}
- [[  willr
  ]{.md-ellipsis}](index.html#src.pandas_ta.momentum.willr.willr){.md-nav__link}
:::
::::
:::::

:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::: {.md-content md-component="content"}
# Momentum

------------------------------------------------------------------------

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [ao]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.ao.ao "Permanent link"){.headerlink} {#src.pandas_ta.momentum.ao.ao .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    ao(
        high: Series,
        low: Series,
        fast: Int = None,
        slow: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Awesome Oscillator

This indicator attempts to identify momentum with the intention to
affirm trends or anticipate possible reversals.

Sources

- [ifcm](https://www.ifcm.co.uk/ntx-indicators/awesome-oscillator)
- [tradingview](https://www.tradingview.com/wiki/Awesome_Oscillator_(AO))

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Fast period. Default:  |                 |
|                 |                                   | `5`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Slow period. Default:  |                 |
|                 |                                   | `34`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [apo]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.apo.apo "Permanent link"){.headerlink} {#src.pandas_ta.momentum.apo.apo .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    apo(
        close: Series,
        fast: Int = None,
        slow: Int = None,
        mamode: str = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Absolute Price Oscillator

This indicator attempts to quantify momentum.

Sources

- [tradingtechnologies](https://www.tradingtechnologies.com/xtrader-help/x-study/technical-indicator-definitions/absolute-price-oscillator-apo/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Fast period. Default:  |                 |
|                 |                                   | `12`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Slow period. Default:  |                 |
|                 |                                   | `26`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"sma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

- Simply the difference of two different EMAs.
- APO and MACD lines are equivalent.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [bias]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.bias.bias "Permanent link"){.headerlink} {#src.pandas_ta.momentum.bias.bias .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    bias(
        close: Series,
        length: Int = None,
        mamode: str = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Bias

This indicator computes the Rate of Change between the source and a
moving average.

Sources

- Few internet resources on definitive definition.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `26`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"sma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [bop]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.bop.bop "Permanent link"){.headerlink} {#src.pandas_ta.momentum.bop.bop .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    bop(
        open_: Series,
        high: Series,
        low: Series,
        close: Series,
        scalar: IntFloat = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Balance of Power

This indicator attempts to quantify the market strength of buyers versus
sellers.

Sources

- [worden](http://www.worden.com/TeleChartHelp/Content/Indicators/Balance_of_Power.htm)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `open_`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `open` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `1`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [brar]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.brar.brar "Permanent link"){.headerlink} {#src.pandas_ta.momentum.brar.brar .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    brar(
        open_: Series,
        high: Series,
        low: Series,
        close: Series,
        length: Int = None,
        scalar: IntFloat = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
BRAR

BR and AR

Sources

- No internet resources on definitive definition.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `open_`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `open` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `26`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [cci]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.cci.cci "Permanent link"){.headerlink} {#src.pandas_ta.momentum.cci.cci .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    cci(
        high: Series,
        low: Series,
        close: Series,
        length: Int = None,
        c: IntFloat = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Commodity Channel Index

This indicator attempts to identify \"overbought\" and \"oversold\"
levels relative to a mean.

Sources

- [tradingview](https://www.tradingview.com/wiki/Commodity_Channel_Index_(CCI))

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `c`             | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scaling Constant.      |                 |
|                 |                                   | Default: `0.015`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [cfo]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.cfo.cfo "Permanent link"){.headerlink} {#src.pandas_ta.momentum.cfo.cfo .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    cfo(
        close: Series,
        length: Int = None,
        scalar: IntFloat = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Chande Forcast Oscillator

This indicator attempts to calculate the percentage difference between
the actual price and the Time Series Forecast (the endpoint of a linear
regression line).

Sources

- [fmlabs](https://www.fmlabs.com/reference/default.htm?url=ForecastOscillator.htm)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `9`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [cg]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.cg.cg "Permanent link"){.headerlink} {#src.pandas_ta.momentum.cg.cg .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    cg(
        close: Series,
        length: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Center of Gravity

This indicator, by John Ehlers, attempts to identify turning points with
minimal to zero lag and smoothing.

Sources

- [MESA
  Software](http://www.mesasoftware.com/papers/TheCGOscillator.pdf)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [cmo]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.cmo.cmo "Permanent link"){.headerlink} {#src.pandas_ta.momentum.cmo.cmo .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    cmo(
        close: Series,
        length: Int = None,
        scalar: IntFloat = None,
        talib: bool = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Chande Momentum Oscillator

This indicator attempts to capture momentum.

Sources

- [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/chande-momentum-oscillator-cmo/)
- [tradingview](https://www.tradingview.com/script/hdrf0fXV-Variable-Index-Dynamic-Average-VIDYA/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Uses EMA if       |                 |
|                 |                                   | `False`. Default:      |                 |
|                 |                                   | `True`                 |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

- Overbought around 50
- Oversold around -50.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [coppock]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.coppock.coppock "Permanent link"){.headerlink} {#src.pandas_ta.momentum.coppock.coppock .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    coppock(
        close: Series,
        length: Int = None,
        fast: Int = None,
        slow: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Coppock Curve

This indicator, by Edwin Coppock 1962, was originally called the
\"Trendex Model\", attempts to identify major upturns and downturns.

Sources

- [wikipedia](https://en.wikipedia.org/wiki/Coppock_curve)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | WMA period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Fast ROC period.       |                 |
|                 |                                   | Default: `11`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Slow ROC period.       |                 |
|                 |                                   | Default: `14`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

Although designed for monthly use, a daily calculation over the same
period length can be made, converting the periods to 294-day and 231-day
rate of changes, and a 210-day WMA.
:::
:::::
::::::
:::::::
::::::::

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [crsi]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.crsi.crsi "Permanent link"){.headerlink} {#src.pandas_ta.momentum.crsi.crsi .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    crsi(
        close: Series,
        rsi_length: Int = None,
        streak_length: Int = None,
        rank_length: Int = None,
        scalar: IntFloat = None,
        talib: bool = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Connors Relative Strength Index

This indicator attempts to identify momentum and potential reversals at
\"overbought\" or \"oversold\" conditions.

Sources

- [alvarezquanttrading](https://alvarezquanttrading.com/blog/connorsrsi-analysis/)
- [tradingview](https://www.tradingview.com/support/solutions/43000502017-connors-rsi-crsi/)
- An Introduction to ConnorsRSI. Connors Research Trading Strategy
  Series. Connors, L., Alvarez, C., & Radtke, M. (2012). ISBN
  978-0-9853072-9-5.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `rsi_length`    | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The RSI period.        |                 |
|                 |                                   | Default: `3`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `streak_length` | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Streak RSI period.     |                 |
|                 |                                   | Default: `2`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `rank_length`   | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Percent Rank length.   |                 |
|                 |                                   | Default: `100`         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [cti]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.cti.cti "Permanent link"){.headerlink} {#src.pandas_ta.momentum.cti.cti .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    cti(
        close: Series,
        length: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Correlation Trend Indicator

This oscillator, by John Ehlers\' in 2020, attempts to identify the
magnitude and direction of a trend using linear regession.

Note

This is a wrapper for `ta.linreg(close, r=True)`.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `12`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [dm]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.dm.dm "Permanent link"){.headerlink} {#src.pandas_ta.momentum.dm.dm .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    dm(
        high: Series,
        low: Series,
        length: Int = None,
        mamode: str = None,
        talib: bool = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Directional Movement

This indicator, by J. Welles Wilder in 1978, attempts to determine
direction.

Sources

- [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=24&Name=Directional_Movement_Index)
- [tradingview](https://www.tradingview.com/pine-script-reference/#fun_dmi)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"rma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [er]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.er.er "Permanent link"){.headerlink} {#src.pandas_ta.momentum.er.er .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    er(
        close: Series,
        length: Int = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Efficiency Ratio

This indicator, by Perry J. Kaufman, attempts to identify market noise
or volatility.

Sources

- \"New Trading Systems and Methods\", Perry J. Kaufman
- [tc2000](https://help.tc2000.com/m/69404/l/749623-kaufman-efficiency-ratio)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `1`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

It is calculated by dividing the net change in price movement over `n`
periods by the sum of the absolute net changes over the same `n`
periods.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [eri]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.eri.eri "Permanent link"){.headerlink} {#src.pandas_ta.momentum.eri.eri .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    eri(
        high: Series,
        low: Series,
        close: Series,
        length: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Elder Ray Index

This indicator, by Dr Alexander Elder, attempts to identify market
strength.

Sources

- [admiralmarkets](https://admiralmarkets.com/education/articles/forex-indicators/bears-and-bulls-power-indicator)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Note

- Possible entry signals when used in combination with a trend,
- Bear Power attempts to quantify lower value appeal.
- Bull Power attempts the to quantify higher value appeal.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [exhc]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.exhc.exhc "Permanent link"){.headerlink} {#src.pandas_ta.momentum.exhc.exhc .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    exhc(
        close: Series,
        length: Int = None,
        cap: Int = None,
        asint: bool = None,
        show_all: bool = None,
        nozeros: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Exhaustion Count

This indicator attempts to identify rising/falling exhaustion.

Sources

- [demark](https://demark.com)
- [practicaltechnicalanalysis](http://practicaltechnicalanalysis.blogspot.com/2013/01/tom-demark-sequential.html)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | Series of close\'s     |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `4`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `cap`           | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Count cap. For no cap, |                 |
|                 |                                   | set to `0`. Default:   |                 |
|                 |                                   | `13`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `show_all`      | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Counts 1 - 13. For 6 - |                 |
|                 |                                   | 9, set to `False`.     |                 |
|                 |                                   | Default: `True`        |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `asint`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Returns as `Int`.      |                 |
|                 |                                   | Default: `False`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `nozeros`       | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Replace zeros with     |                 |
|                 |                                   | `np.nan`. Default:     |                 |
|                 |                                   | `False`                |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Note

Similar to TD Sequential
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [fisher]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.fisher.fisher "Permanent link"){.headerlink} {#src.pandas_ta.momentum.fisher.fisher .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    fisher(
        high: Series,
        low: Series,
        length: Int = None,
        signal: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Fisher Transform

This indicator attempts to identify significant reversals through
normalization.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `9`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `signal`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Signal period.         |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Reversal Signal

When the two lines cross.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [inertia]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.inertia.inertia "Permanent link"){.headerlink} {#src.pandas_ta.momentum.inertia.inertia .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    inertia(
        close: Series,
        high: Series = None,
        low: Series = None,
        length: Int = None,
        rvi_length: Int = None,
        scalar: IntFloat = None,
        refined: bool = None,
        thirds: bool = None,
        drift: Int = None,
        mamode: str = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Inertia

This indicator, by Donald Dorsey, is the *rvi* smoothed by the Least
Squares MA.

Sources

- Donald Dorsey, some article in September, 1995.
- [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=285&Name=Inertia)
- [tradingview](https://www.tradingview.com/script/mLZJqxKn-Relative-Volatility-Index/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | `None`          |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | `None`          |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `20`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `rvi_length`    | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | RVI period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `refined`       | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Use \'refined\'        |                 |
|                 |                                   | calculation. Default:  |                 |
|                 |                                   | `False`                |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `thirds`        | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Use \'thirds\'         |                 |
|                 |                                   | calculation. Default:  |                 |
|                 |                                   | `False`                |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"ema"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Note

- Negative Inertia when less than 50.
- Positive Inertia when greater than 50.
:::
:::::
::::::
:::::::
::::::::

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [kdj]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.kdj.kdj "Permanent link"){.headerlink} {#src.pandas_ta.momentum.kdj.kdj .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    kdj(
        high: Series,
        low: Series,
        close: Series,
        length: Int = None,
        signal: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
KDJ

This indicator, derived from the Slow Stochastic, includes an extra
signal named the J line. The J line represents the divergence of the %D
value from the %K.

Sources

- [anychart](https://docs.anychart.com/Stock_Charts/Technical_Indicators/Mathematical_Description#kdj)
- [prorealcode](https://www.prorealcode.com/prorealtime-indicators/kdj/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `9`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `signal`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Signal period.         |                 |
|                 |                                   | Default: `3`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 3 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Note

The J can go beyond `[0, 100]` for %K and %D lines when charted.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [kst]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.kst.kst "Permanent link"){.headerlink} {#src.pandas_ta.momentum.kst.kst .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    kst(
        close: Series,
        signal: Int = None,
        roc1: Int = None,
        roc2: Int = None,
        roc3: Int = None,
        roc4: Int = None,
        sma1: Int = None,
        sma2: Int = None,
        sma3: Int = None,
        sma4: Int = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
\'Know Sure Thing\'

This indicator, by Martin Pring, attempts to capture trends using a
smoothed indicator of four different smoothed ROCs.

Sources

- [incrediblecharts](https://www.incrediblecharts.com/indicators/kst.php)
- [tradingview](https://www.tradingview.com/wiki/Know_Sure_Thing_(KST))

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `roc1`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | ROC 1 period. Default: |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `roc2`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | ROC 2 period. Default: |                 |
|                 |                                   | `15`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `roc3`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | ROC 3 period. Default: |                 |
|                 |                                   | `20`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `roc4`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | ROC 4 period. Default: |                 |
|                 |                                   | `30`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `sma1`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | SMA 1 period. Default: |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `sma2`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | SMA 2 period. Default: |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `sma3`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | SMA 3 period. Default: |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `sma4`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | SMA 4 period. Default: |                 |
|                 |                                   | `15`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `signal`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Signal period.         |                 |
|                 |                                   | Default: `9`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [macd]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.macd.macd "Permanent link"){.headerlink} {#src.pandas_ta.momentum.macd.macd .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    macd(
        close: Series,
        fast: Int = None,
        slow: Int = None,
        signal: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Moving Average Convergence Divergence

This indicator attempts to identify trends.

Sources

- [tradingview](https://www.tradingview.com/wiki/MACD_(Moving_Average_Convergence/Divergence))
- [tradingview (AS Mode)](https://tr.tradingview.com/script/YFlKXHnP/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Fast MA period.        |                 |
|                 |                                   | Default: `12`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Slow MA period.        |                 |
|                 |                                   | Default: `26`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `signal`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Signal period.         |                 |
|                 |                                   | Default: `9`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `asmode`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | Enable AS version of MACD.   |
|                       |                          | Default: `False`             |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 3 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [mom]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.mom.mom "Permanent link"){.headerlink} {#src.pandas_ta.momentum.mom.mom .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    mom(
        close: Series,
        length: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Momentum

This indicator attempts to quantify speed by using the differences over
a bar length.

Sources

- [onlinetradingconcepts](http://www.onlinetradingconcepts.com/TechnicalAnalysis/Momentum.html)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `1`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [pgo]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.pgo.pgo "Permanent link"){.headerlink} {#src.pandas_ta.momentum.pgo.pgo .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    pgo(
        high: Series,
        low: Series,
        close: Series,
        length: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Pretty Good Oscillator

This indicator, by Mark Johnson, attempts to identify breakouts for
longer time periods based on the distance of the current bar to its
N-day SMA, expressed in terms of an ATR over a similar length.

Sources

- [tradingtechnologies](https://library.tradingtechnologies.com/trade/chrt-ti-pretty-good-oscillator.html)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Entry

- Long when greater than 3.
- Short when less than -3.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [ppo]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.ppo.ppo "Permanent link"){.headerlink} {#src.pandas_ta.momentum.ppo.ppo .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    ppo(
        close: Series,
        fast: Int = None,
        slow: Int = None,
        signal: Int = None,
        scalar: IntFloat = None,
        mamode: str = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Percentage Price Oscillator

Similar to MACD.

Sources

- [investopedia](https://www.investopedia.com/terms/p/ppo.asp)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Fast MA period.        |                 |
|                 |                                   | Default: `12`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Slow MA period.        |                 |
|                 |                                   | Default: `26`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `signal`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Signal period.         |                 |
|                 |                                   | Default: `9`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"sma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 3 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [psl]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.psl.psl "Permanent link"){.headerlink} {#src.pandas_ta.momentum.psl.psl .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    psl(
        close: Series,
        open_: Series = None,
        length: Int = None,
        scalar: IntFloat = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Psychological Line

This indicator compares the number of the rising bars to the total
number of bars. In other words, it is the percentage of bars that are
above the previous bar over a given length.

Sources

- [quantshare](https://www.quantshare.com/item-851-psychological-line)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `open_`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | `None`          |
|                 |                                   | `open` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `12`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [qqe]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.qqe.qqe "Permanent link"){.headerlink} {#src.pandas_ta.momentum.qqe.qqe .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    qqe(
        close: Series,
        length: Int = None,
        smooth: Int = None,
        factor: IntFloat = None,
        mamode: str = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Quantitative Qualitative Estimation

This indicator is similar to SuperTrend but uses a Smoothed `rsi` with
upper and lower bands.

Sources

- [prorealcode](https://www.prorealcode.com/prorealtime-indicators/qqe-quantitative-qualitative-estimation/)
- [tradingpedia](https://www.tradingpedia.com/forex-trading-indicators/quantitative-qualitative-estimation)
- [tradingview](https://www.tradingview.com/script/IYfA9R2k-QQE-MT4/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | RSI period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `smooth`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | RSI smoothing period.  |                 |
|                 |                                   | Default: `5`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `factor`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | QQE Factor. Default:   |                 |
|                 |                                   | `4.236`                |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"ema"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 4 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Trend

- Long: When the Smoothed RSI crosses the previous upperband.
- Short: When the Smoothed RSI crosses the previous lowerband.

See also

- QQE.mq5 by EarnForex Copyright © 2010
- Tim Hyder (2008) version
- Roman Ignatov (2006) version
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [roc]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.roc.roc "Permanent link"){.headerlink} {#src.pandas_ta.momentum.roc.roc .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    roc(
        close: Series,
        length: Int = None,
        scalar: IntFloat = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Rate of Change

This indicator, also (confusingly) known as Momentum, is a pure
oscillator that quantifies the percent change.

Sources

- [tradingview](https://www.tradingview.com/wiki/Rate_of_Change_(ROC))

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `10`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [rsi]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.rsi.rsi "Permanent link"){.headerlink} {#src.pandas_ta.momentum.rsi.rsi .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    rsi(
        close: Series,
        length: Int = None,
        scalar: IntFloat = None,
        mamode: str = None,
        talib: bool = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Relative Strength Index

This oscillator used to attempts to quantify \"velocity\" and
\"magnitude\".

Sources

- [tradingview](https://www.tradingview.com/wiki/Relative_Strength_Index_(RSI))

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"rma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+

Warning

TA-Lib Correlation: `np.float64(0.9289853267851295)`

Tip

Corrective contributions welcome!
:::
:::::
::::::
:::::::
::::::::

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [rsx]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.rsx.rsx "Permanent link"){.headerlink} {#src.pandas_ta.momentum.rsx.rsx .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    rsx(
        close: Series,
        length: Int = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Relative Strength Xtra

This indicator, by Jurik Research, is an enhanced version of the RSI
which attemps to reduce noise and provide a clearer, though slightly
delayed, signal.

Sources

- [jurikres](http://www.jurikres.com/catalog1/ms_rsx.htm)
- [prorealcode](https://www.prorealcode.com/prorealtime-indicators/jurik-rsx/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [rvgi]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.rvgi.rvgi "Permanent link"){.headerlink} {#src.pandas_ta.momentum.rvgi.rvgi .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    rvgi(
        open_: Series,
        high: Series,
        low: Series,
        close: Series,
        length: Int = None,
        swma_length: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Relative Vigor Index

This indicator attempts to quantify the strength of a trend relative to
its trading range.

Sources

- [investopedia](https://www.investopedia.com/terms/r/relative_vigor_index.asp)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `open_`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `open` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `swma_length`   | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | SWMA period. Default:  |                 |
|                 |                                   | `4`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [slope]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.slope.slope "Permanent link"){.headerlink} {#src.pandas_ta.momentum.slope.slope .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    slope(
        close: Series,
        length: Int = None,
        as_angle: bool = None,
        to_degrees: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Slope

Calculates a rolling slope.

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `1`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `as_angle`      | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Converts slope to an   |                 |
|                 |                                   | angle in radians per   |                 |
|                 |                                   | `np.arctan()`.         |                 |
|                 |                                   | Default: `False`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `to_degrees`    | [`value`]{title="value"}          | ::: doc-md-description | `None`          |
|                 |                                   | If `as_angle=True`,    |                 |
|                 |                                   | converts radians to    |                 |
|                 |                                   | degrees. Default:      |                 |
|                 |                                   | `False`                |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [smc]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.smc.smc "Permanent link"){.headerlink} {#src.pandas_ta.momentum.smc.smc .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    smc(
        open_: Series,
        high: Series,
        low: Series,
        close: Series,
        abr_length: Int = None,
        close_length: Int = None,
        vol_length: Int = None,
        percent: Int = None,
        vol_ratio: IntFloat = None,
        asint: bool = None,
        mamode: str = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DictLike
:::

::: {.doc .doc-contents}
Smart Money Concept

This indicator combines several techniques in an attempt to identify
significant movements that might indicate \"smart money\" actions. It
uses candlestick patterns, moving averages, and imbalance calculations.

Sources

- [tradingview](https://www.tradingview.com/script/CnB3fSph-Smart-Money-Concepts-LuxAlgo/)

[Parameters:]{.doc-section-title}

+-----------------+--------------------------+------------------------+-----------------+
| Name            | Type                     | Description            | Default         |
+=================+==========================+========================+=================+
| `abr_length`    | [`int`]{title="int"}     | ::: doc-md-description | `None`          |
|                 |                          | ABR length. Default:   |                 |
|                 |                          | `14`                   |                 |
|                 |                          | :::                    |                 |
+-----------------+--------------------------+------------------------+-----------------+
| `close_length`  | [`int`]{title="int"}     | ::: doc-md-description | `None`          |
|                 |                          | The `close` MA period. |                 |
|                 |                          | Default: `50`          |                 |
|                 |                          | :::                    |                 |
+-----------------+--------------------------+------------------------+-----------------+
| `vol_length`    | [`int`]{title="int"}     | ::: doc-md-description | `None`          |
|                 |                          | Volatility period.     |                 |
|                 |                          | Default: `20`          |                 |
|                 |                          | :::                    |                 |
+-----------------+--------------------------+------------------------+-----------------+
| `percent`       | [`int`]{title="int"}     | ::: doc-md-description | `None`          |
|                 |                          | Percent of wick that   |                 |
|                 |                          | exceeds the body.      |                 |
|                 |                          | Default: `5`           |                 |
|                 |                          | :::                    |                 |
+-----------------+--------------------------+------------------------+-----------------+
| `vol_ratio`     | [`float`]{title="float"} | ::: doc-md-description | `None`          |
|                 |                          | Volatility ratio       |                 |
|                 |                          | (high) limit. Default: |                 |
|                 |                          | `1.5`                  |                 |
|                 |                          | :::                    |                 |
+-----------------+--------------------------+------------------------+-----------------+
| `asint`         | [`bool`]{title="bool"}   | ::: doc-md-description | `None`          |
|                 |                          | Returns as `Int`.      |                 |
|                 |                          | Default: `True`        |                 |
|                 |                          | :::                    |                 |
+-----------------+--------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}     | ::: doc-md-description | `None`          |
|                 |                          | See `help(ta.ma)`.     |                 |
|                 |                          | Default: `"sma"`       |                 |
|                 |                          | :::                    |                 |
+-----------------+--------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}   | ::: doc-md-description | `None`          |
|                 |                          | If installed, use TA   |                 |
|                 |                          | Lib. Default: `True`   |                 |
|                 |                          | :::                    |                 |
+-----------------+--------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}     | ::: doc-md-description | `None`          |
|                 |                          | Post shift. Default:   |                 |
|                 |                          | `0`                    |                 |
|                 |                          | :::                    |                 |
+-----------------+--------------------------+------------------------+-----------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 7 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [smi]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.smi.smi "Permanent link"){.headerlink} {#src.pandas_ta.momentum.smi.smi .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    smi(
        close: Series,
        fast: Int = None,
        slow: Int = None,
        signal: Int = None,
        scalar: IntFloat = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
SMI Ergodic Indicator

This indicator, by William Blau, is the same as the TSI except the SMI
includes a signal line. A trend is considered bullish when crossing
above zero and bearish when crossing below zero. This implementation
includes both the SMI Ergodic Indicator and SMI Ergodic Oscillator.

Sources

- [motivewave](https://www.motivewave.com/studies/smi_ergodic_indicator.htm)
- [tradingview
  A](https://www.tradingview.com/script/Xh5Q0une-SMI-Ergodic-Oscillator/)
- [tradingview B](https://www.tradingview.com/script/cwrgy4fw-SMIIO/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The short period.      |                 |
|                 |                                   | Default: `5`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The long period.       |                 |
|                 |                                   | Default: `20`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `signal`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Signal period.         |                 |
|                 |                                   | Default: `5`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `1`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 3 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [squeeze]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.squeeze.squeeze "Permanent link"){.headerlink} {#src.pandas_ta.momentum.squeeze.squeeze .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    squeeze(
        high: Series,
        low: Series,
        close: Series,
        bb_length: Int = None,
        bb_std: IntFloat = None,
        kc_length: Int = None,
        kc_scalar: IntFloat = None,
        mom_length: Int = None,
        mom_smooth: Int = None,
        use_tr: bool = None,
        mamode: str = None,
        prenan: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Squeeze

This indicator, based on John Carter\'s \"TTM Squeeze\" indicator,
attempts identify momentum using volatility.

Sources

- \"Mastering the Trade\" (chapter 11), John Carter
- [thinkorswim](https://tlc.thinkorswim.com/center/reference/Tech-Indicators/studies-library/T-U/TTM-Squeeze)
- [tradestation](https://tradestation.tradingappstore.com/products/TTMSqueeze)
- [tradingview](https://www.tradingview.com/scripts/lazybear/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `bb_length`     | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | BB period. Default:    |                 |
|                 |                                   | `20`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `bb_std`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | BB Std. Dev. Default:  |                 |
|                 |                                   | `2`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `kc_length`     | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | KC period. Default:    |                 |
|                 |                                   | `20`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `kc_scalar`     | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | KC scalar. Default:    |                 |
|                 |                                   | `1.5`                  |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mom_length`    | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Momentum Period.       |                 |
|                 |                                   | Default: `12`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mom_smooth`    | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Momentum Smoothing     |                 |
|                 |                                   | period. Default: `6`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | One of: \"ema\" or     |                 |
|                 |                                   | \"sma\". Default:      |                 |
|                 |                                   | `"sma"`                |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `prenan`        | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Apply prenans.         |                 |
|                 |                                   | Default: `False`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `tr`                  | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | Use True Range for Keltner   |
|                       |                          | Channels. Default: `True`    |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `asint`               | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | Returns as `Int`. Default:   |
|                       |                          | `True`                       |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `lazybear`            | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | LazyBear\'s TradingView.     |
|                       |                          | Default: `False`             |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `detailed`            | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | Extra detailed. Default:     |
|                       |                          | `False`                      |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | - Default: 4 columns              |
|                                         | - Detailed: 10 columns            |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Volatility

- Increasing: `kc` and `bbands` difference increases
- Decreasing: `kc` and `bbands` difference decreases
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [squeeze_pro]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.squeeze_pro.squeeze_pro "Permanent link"){.headerlink} {#src.pandas_ta.momentum.squeeze_pro.squeeze_pro .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    squeeze_pro(
        high: Series,
        low: Series,
        close: Series,
        bb_length: Int = None,
        bb_std: IntFloat = None,
        kc_length: Int = None,
        kc_scalar_narrow: IntFloat = None,
        kc_scalar_normal: IntFloat = None,
        kc_scalar_wide: IntFloat = None,
        mom_length: Int = None,
        mom_smooth: Int = None,
        use_tr: bool = None,
        mamode: str = None,
        prenan: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Squeeze Pro

This indicator, based on John Carter\'s \"TTM Squeeze\" indicator,
attempts identify momentum using volatility with additional details.

Sources

- [usethinkscript](https://usethinkscript.com/threads/john-carters-squeeze-pro-indicator-for-thinkorswim-free.4021/)
- [tradingview](https://www.tradingview.com/script/TAAt6eRX-Squeeze-PRO-Indicator-Makit0/)

[Parameters:]{.doc-section-title}

+--------------------+-----------------------------------+------------------------+-----------------+
| Name               | Type                              | Description            | Default         |
+====================+===================================+========================+=================+
| `high`             | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                    |                                   | `high` Series          |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `low`              | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                    |                                   | `low` Series           |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `close`            | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                    |                                   | `close` Series         |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `bb_length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                    |                                   | BB period. Default:    |                 |
|                    |                                   | `20`                   |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `bb_std`           | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                    |                                   | BB Std. Dev. Default:  |                 |
|                    |                                   | `2`                    |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `kc_length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                    |                                   | KC period. Default:    |                 |
|                    |                                   | `20`                   |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `kc_scalar_normal` | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                    |                                   | Keltner Channel scalar |                 |
|                    |                                   | for normal channel.    |                 |
|                    |                                   | Default: `1.5`         |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `kc_scalar_narrow` | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                    |                                   | Narrow channel KC      |                 |
|                    |                                   | scalar. Default: `1`   |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `kc_scalar_wide`   | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                    |                                   | Wide channel KC        |                 |
|                    |                                   | scalar. Default: `2`   |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `mom_length`       | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                    |                                   | Momentum Period.       |                 |
|                    |                                   | Default: `12`          |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `mom_smooth`       | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                    |                                   | Momentum Smoothing     |                 |
|                    |                                   | period. Default: `6`   |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `mamode`           | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                    |                                   | One of: \"ema\" or     |                 |
|                    |                                   | \"sma\". Default:      |                 |
|                    |                                   | `"sma"`                |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `prenan`           | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                    |                                   | Apply prenans.         |                 |
|                    |                                   | Default: `False`       |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+
| `offset`           | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                    |                                   | Post shift. Default:   |                 |
|                    |                                   | `0`                    |                 |
|                    |                                   | :::                    |                 |
+--------------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `tr`                  | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | Use True Range for Keltner   |
|                       |                          | Channels. Default: `True`    |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `asint`               | [`bool`]{title="bool"}   | ::: doc-md-description       |
|                       |                          | Returns as `Int`. Default:   |
|                       |                          | `True`                       |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `mamode`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | Which MA to use. Default:    |
|                       |                          | `"sma"`                      |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `detailed`            | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | Extra detailed. Default:     |
|                       |                          | `False`                      |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 6 columns (*default*) or 12       |
|                                         | columns if `detailed=True`        |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Warning

May be depreciated in the future and combined with `squeeze`.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [stc]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.stc.stc "Permanent link"){.headerlink} {#src.pandas_ta.momentum.stc.stc .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    stc(
        close: Series,
        tc_length: Int = None,
        fast: Int = None,
        slow: Int = None,
        factor: IntFloat = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Schaff Trend Cycle

This indicator is an evolved MACD with additional smoothing.

Sources

- [rengel8](https://github.com/rengel8)
- [prorealcode](https://www.prorealcode.com/prorealtime-indicators/schaff-trend-cycle2/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `tc_length`     | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | TC period. (Adjust to  |                 |
|                 |                                   | the half of cycle)     |                 |
|                 |                                   | Default: `10`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Fast MA period.        |                 |
|                 |                                   | Default: `12`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Slow MA period.        |                 |
|                 |                                   | Default: `26`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `factor`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Smoothing factor for   |                 |
|                 |                                   | last stoch.            |                 |
|                 |                                   | calculation. Default:  |                 |
|                 |                                   | `0.5`                  |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | How many bars to shift |                 |
|                 |                                   | the results. Default:  |                 |
|                 |                                   | `` `0 ``               |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+-----------------------------------+------------------------------+
| Name                  | Type                              | Description                  |
+=======================+===================================+==============================+
| `ma1`                 | [`Series`]{title="pandas.Series"} | ::: doc-md-description       |
|                       |                                   | User chosen MA. Default:     |
|                       |                                   | `False`                      |
|                       |                                   | :::                          |
+-----------------------+-----------------------------------+------------------------------+
| `ma2`                 | [`Series`]{title="pandas.Series"} | ::: doc-md-description       |
|                       |                                   | User chosen MA. Default:     |
|                       |                                   | `False`                      |
|                       |                                   | :::                          |
+-----------------------+-----------------------------------+------------------------------+
| `osc`                 | [`Series`]{title="pandas.Series"} | ::: doc-md-description       |
|                       |                                   | User chosen oscillator.      |
|                       |                                   | Default: `False`             |
|                       |                                   | :::                          |
+-----------------------+-----------------------------------+------------------------------+
| `fillna`              | [`value`]{title="value"}          | ::: doc-md-description       |
|                       |                                   | `pd.DataFrame.fillna(value)` |
|                       |                                   | :::                          |
+-----------------------+-----------------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 3 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Note

Can also seed STC with two MAs, `ma1` and `ma2`, or an oscillator `osc`.

- `ma1` and `ma2` are **both** required if this option is used.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [stoch]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.stoch.stoch "Permanent link"){.headerlink} {#src.pandas_ta.momentum.stoch.stoch .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    stoch(
        high: Series,
        low: Series,
        close: Series,
        k: Int = None,
        d: Int = None,
        smooth_k: Int = None,
        mamode: str = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Stochastic

This indicator, by George Lane in the 1950\'s, attempts to identify and
quantify momentum; it assumes that momentum precedes value change.

Sources

- [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=332&Name=KD_-_Slow)
- [tradingview](https://www.tradingview.com/wiki/Stochastic_(STOCH))

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `k`             | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Fast %K period.    |                 |
|                 |                                   | Default: `14`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `d`             | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Slow %D period.    |                 |
|                 |                                   | Default: `3`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `smooth_k`      | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Slow %K period.    |                 |
|                 |                                   | Default: `3`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"sma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 3 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [stochf]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.stochf.stochf "Permanent link"){.headerlink} {#src.pandas_ta.momentum.stochf.stochf .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    stochf(
        high: Series,
        low: Series,
        close: Series,
        k: Int = None,
        d: Int = None,
        mamode: str = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Fast Stochastic

This indicator, by George Lane in the 1950\'s, attempts to identify and
quantify momentum like STOCH, but is more volatile.

Sources

- [corporatefinanceinstitute](https://corporatefinanceinstitute.com/resources/knowledge/trading-investing/fast-stochastic-indicator/)
- [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=333&Name=KD_-_Fast)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `k`             | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Fast %K period.    |                 |
|                 |                                   | Default: `14`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `d`             | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Slow %D period.    |                 |
|                 |                                   | Default: `3`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"sma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [stochrsi]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.stochrsi.stochrsi "Permanent link"){.headerlink} {#src.pandas_ta.momentum.stochrsi.stochrsi .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    stochrsi(
        close: Series,
        length: Int = None,
        rsi_length: Int = None,
        k: Int = None,
        d: Int = None,
        mamode: str = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
Stochastic RSI

This indicator attempts to quantify RSI relative to its High-Low range.

Sources

- \"Stochastic RSI and Dynamic Momentum Index\", Tushar Chande and
  Stanley Kroll, Stock & Commodities V.11:5 (189-199)
- [tradingview](https://www.tradingview.com/wiki/Stochastic_(STOCH))

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `rsi_length`    | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | RSI period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `k`             | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Fast %K period.    |                 |
|                 |                                   | Default: `3`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `d`             | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Slow %K period.    |                 |
|                 |                                   | Default: `3`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"sma"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+

Note

May be more sensitive to RSI and thus identify potential \"overbought\"
or \"oversold\" signals.
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [tmo]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.tmo.tmo "Permanent link"){.headerlink} {#src.pandas_ta.momentum.tmo.tmo .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    tmo(
        open_: Series,
        close: Series,
        tmo_length: Int = None,
        calc_length: Int = None,
        smooth_length: Int = None,
        momentum: bool = None,
        normalize: bool = None,
        exclusive: bool = None,
        mamode: str = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
True Momentum Oscillator

This indicator attempts to quantify momentum.

Sources

- [tradingview
  A](https://www.tradingview.com/script/VRwDppqd-True-Momentum-Oscillator/)
- [tradingview
  B](https://www.tradingview.com/script/65vpO7T5-True-Momentum-Oscillator-Universal-Edition/)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `open_`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `open` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `tmo_length`    | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | TMO period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `calc_length`   | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Initial MA period.     |                 |
|                 |                                   | Default: `5`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `smooth_length` | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Main and smooth signal |                 |
|                 |                                   | MA period. Default:    |                 |
|                 |                                   | `3`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | See `help(ta.ma)`.     |                 |
|                 |                                   | Default: `"ema"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `momentum`      | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Compute main and       |                 |
|                 |                                   | smooth momentum.       |                 |
|                 |                                   | Default: `False`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `normalize`     | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Normalize. Default:    |                 |
|                 |                                   | `False`                |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `exclusive`     | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | Exclusive period over  |                 |
|                 |                                   | `n` bars, or           |                 |
|                 |                                   | inclusively over `n-1` |                 |
|                 |                                   | bars. Default: `True`  |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+-------------------------+
| Name                  | Type                     | Description             |
+=======================+==========================+=========================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description  |
|                       |                          | DataFrame.fillna(value) |
|                       |                          | :::                     |
+-----------------------+--------------------------+-------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 4 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [trix]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.trix.trix "Permanent link"){.headerlink} {#src.pandas_ta.momentum.trix.trix .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    trix(
        close: Series,
        length: Int = None,
        signal: Int = None,
        scalar: IntFloat = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Trix

This indicator attempts to identify divergences as an oscillator.

Sources

- [tradingview](https://www.tradingview.com/wiki/TRIX)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `18`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `signal`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Signal period.         |                 |
|                 |                                   | Default: `9`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [tsi]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.tsi.tsi "Permanent link"){.headerlink} {#src.pandas_ta.momentum.tsi.tsi .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    tsi(
        close: Series,
        fast: Int = None,
        slow: Int = None,
        signal: Int = None,
        scalar: IntFloat = None,
        mamode: str = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> DataFrame
:::

::: {.doc .doc-contents}
True Strength Index

This indicator attempts to identify short-term swings in trend direction
as well as identifying possible \"overbought\" and \"oversold\" signals.

Sources

- [investopedia](https://www.investopedia.com/terms/t/tsi.asp)

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Fast MA period.        |                 |
|                 |                                   | Default: `13`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Slow MA period.        |                 |
|                 |                                   | Default: `25`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `signal`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Signal period.         |                 |
|                 |                                   | Default: `13`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `scalar`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | Scalar. Default: `100` |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `mamode`        | [`str`]{title="str"}              | ::: doc-md-description | `None`          |
|                 |                                   | Signal MA. See         |                 |
|                 |                                   | `help(ta.ma)`.         |                 |
|                 |                                   | Default: `"ema"`       |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------------+-----------------------------------+
| Type                                    | Description                       |
+=========================================+===================================+
| [`DataFrame`]{title="pandas.DataFrame"} | ::: doc-md-description            |
|                                         | 2 columns                         |
|                                         | :::                               |
+-----------------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [uo]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.uo.uo "Permanent link"){.headerlink} {#src.pandas_ta.momentum.uo.uo .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    uo(
        high: Series,
        low: Series,
        close: Series,
        fast: Int = None,
        medium: Int = None,
        slow: Int = None,
        fast_w: IntFloat = None,
        medium_w: IntFloat = None,
        slow_w: IntFloat = None,
        talib: bool = None,
        drift: Int = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
Ultimate Oscillator

This indicator, by Larry Williams, attempts to identify momentum.

Sources

- [tradingview](https://www.tradingview.com/wiki/Ultimate_Oscillator_(UO))

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Fast %K period.    |                 |
|                 |                                   | Default: `7`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `medium`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Slow %K period.    |                 |
|                 |                                   | Default: `14`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow`          | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The Slow %D period.    |                 |
|                 |                                   | Default: `28`          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `fast_w`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | The Fast %K period.    |                 |
|                 |                                   | Default: `4.0`         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `medium_w`      | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | The Slow %K period.    |                 |
|                 |                                   | Default: `2.0`         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `slow_w`        | [`float`]{title="float"}          | ::: doc-md-description | `None`          |
|                 |                                   | The Slow %D period.    |                 |
|                 |                                   | Default: `1.0`         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `drift`         | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Difference amount.     |                 |
|                 |                                   | Default: `1`           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

------------------------------------------------------------------------

\

:::::::: {.doc .doc-object .doc-module}
::::::: {.doc .doc-contents .first}
:::::: {.doc .doc-children}
::::: {.doc .doc-object .doc-function}
## [willr]{.doc .doc-object-name .doc-function-name} [\#](index.html#src.pandas_ta.momentum.willr.willr "Permanent link"){.headerlink} {#src.pandas_ta.momentum.willr.willr .doc .doc-heading}

::: {.language-python .doc-signature .highlight}
    willr(
        high: Series,
        low: Series,
        close: Series,
        length: Int = None,
        talib: bool = None,
        offset: Int = None,
        **kwargs: DictLike,
    ) -> Series
:::

::: {.doc .doc-contents}
William\'s Percent R

This indicator attempts to identify \"overbought\" and \"oversold\"
conditions similar to the RSI.

Sources

- [tradingview](https://www.tradingview.com/wiki/Williams_%25R_(%25R))

[Parameters:]{.doc-section-title}

+-----------------+-----------------------------------+------------------------+-----------------+
| Name            | Type                              | Description            | Default         |
+=================+===================================+========================+=================+
| `high`          | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `high` Series          |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `low`           | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `low` Series           |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `close`         | [`Series`]{title="pandas.Series"} | ::: doc-md-description | *required*      |
|                 |                                   | `close` Series         |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `length`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | The period. Default:   |                 |
|                 |                                   | `14`                   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `talib`         | [`bool`]{title="bool"}            | ::: doc-md-description | `None`          |
|                 |                                   | If installed, use TA   |                 |
|                 |                                   | Lib. Default: `True`   |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+
| `offset`        | [`int`]{title="int"}              | ::: doc-md-description | `None`          |
|                 |                                   | Post shift. Default:   |                 |
|                 |                                   | `0`                    |                 |
|                 |                                   | :::                    |                 |
+-----------------+-----------------------------------+------------------------+-----------------+

[Other Parameters:]{.doc-section-title}

+-----------------------+--------------------------+------------------------------+
| Name                  | Type                     | Description                  |
+=======================+==========================+==============================+
| `fillna`              | [`value`]{title="value"} | ::: doc-md-description       |
|                       |                          | `pd.DataFrame.fillna(value)` |
|                       |                          | :::                          |
+-----------------------+--------------------------+------------------------------+

[Returns:]{.doc-section-title}

+-----------------------------------+-----------------------------------+
| Type                              | Description                       |
+===================================+===================================+
| [`Series`]{title="pandas.Series"} | ::: doc-md-description            |
|                                   | 1 column                          |
|                                   | :::                               |
+-----------------------------------+-----------------------------------+
:::
:::::
::::::
:::::::
::::::::

\

::: {.admonition .danger}
IMPORTANT

**Thanks** to all those that have sponsored and dontated to the library
in the past! Your support has been greatly appreciated!
![🙏](../../assets/external/cdn.jsdelivr.net/gh/jdecked/twemoji@15.1.0/assets/svg/1f64f.svg ":pray:"){.twemoji}

Only **Installation Bugs/Issues** will addressed for releases between
versions *0.4.25b* and *0.4.66b*. Releases beyond version *0.4.66b* will
**only** be released after **significant** donations, sponsorships or
yearly subscriptions have been received via [Buy Me a
Coffee](https://www.buymeacoffee.com/twopirllc){style="color: #ff9200;"}.

Support Tiers coming soon!
:::

\

\

[![\"Buy Me A
Coffee\"](../../assets/external/www.buymeacoffee.com/assets/img/custom_images/orange_img.png){align="left"}](https://www.buymeacoffee.com/twopirllc)\
\

[![ko-fi](../../assets/external/ko-fi.com/img/githubbutton_sm.svg){align="left"}](https://ko-fi.com/K3K4ZRH9D)
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::

![](data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdib3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEzIDIwaC0yVjhsLTUuNSA1LjUtMS40Mi0xLjQyTDEyIDQuMTZsNy45MiA3LjkyLTEuNDIgMS40MkwxMyA4eiIgLz48L3N2Zz4=)
Back to top
:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
