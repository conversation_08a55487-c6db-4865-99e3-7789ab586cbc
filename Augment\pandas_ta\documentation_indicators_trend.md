Title: Trend - Pandas TA

URL Source: https://www.pandas-ta.dev/api/trend/

Published Time: Tue, 10 Jun 2025 22:38:27 GMT

Markdown Content:
* * *

adx[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.adx.adx "Permanent link")
-----------------------------------------------------------------------------------------

```
adx(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    signal_length: Int = None,
    adxr_length: Int = None,
    scalar: IntFloat = None,
    talib: bool = None,
    tvmode: bool = None,
    mamode: str = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Average Directional Movement

This indicator attempts to quantify trend strength by measuring the amount of movement in a single direction.

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/average-directional-movement-adx/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `signal_length` | `int` | Signal period. Default: `length` | `None` |
| `adxr_length` | `int` | ADXR period. Default: `2` | `None` |
| `scalar` | `float` | Scalar. Default: `100` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `tvmode` | `bool` | Trading View. Default: `False` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"rma"` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 4 columns |

Note
`signal_length` is like TradingView's default ADX.

* * *

alphatrend[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.alphatrend.alphatrend "Permanent link")
--------------------------------------------------------------------------------------------------------------

```
alphatrend(
    open_: Series,
    high: Series,
    low: Series,
    close: Series,
    volume: Series = None,
    src: str = None,
    length: int = None,
    multiplier: IntFloat = None,
    threshold: IntFloat = None,
    lag: Int = None,
    mamode: str = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
)
```

Alpha Trend

This indicator attempts to filter sideways movement for accurate signals.

Sources
*   [OnlyFibonacci](https://github.com/OnlyFibonacci/AlgoSeyri/blob/main/alphaTrendIndicator.py)
*   [tradingview](https://www.tradingview.com/script/o50NYLAZ-AlphaTrend/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `open_` | `Series` | `open` Series | _required_ |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `volume` | `Series` | `volume` Series. Default: `None` | `None` |
| `src` | `str` | One of: "open", "high", "low" or "close". Default: `"close"` | `None` |
| `length` | `int` | ATR, MFI, or RSI period. Default: `14` | `None` |
| `multiplier` | `float` | Trailing ATR multiple. Default: `1` | `None` |
| `threshold` | `float` | Momentum threshold. Default: `50` | `None` |
| `lag` | `int` | Lag period of main trend. Default: `2` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"sma"` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

* * *

amat[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.amat.amat "Permanent link")
--------------------------------------------------------------------------------------------

```
amat(
    close: Series,
    fast: Int = None,
    slow: Int = None,
    lookback: Int = None,
    mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Archer Moving Averages Trends

This indicator, by Kevin Johnson, attempts to identify both long run and short run trends.

Sources
*   Kevin Johnson
*   [tradingview](https://www.tradingview.com/script/Z2mq63fE-Trade-Archer-Moving-Averages-v1-4F/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `fast` | `int` | Fast MA period. Default: `8` | `None` |
| `slow` | `int` | Slow MA period. Default: `21` | `None` |
| `lookback` | `int` | Lookback period for `long_run` and `short_run`. Default: `2` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"ema"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `run_length` | `int` | OBV trend period. Default: `2` |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

Note
Both the long run and short run values are integers, where `1` is a trend and `0` is not a trend.

* * *

aroon[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.aroon.aroon "Permanent link")
-----------------------------------------------------------------------------------------------

```
aroon(
    high: Series,
    low: Series,
    length: Int = None,
    scalar: IntFloat = None,
    talib: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Aroon & Aroon Oscillator

This indicator attempts to identify trends and their magnitude.

Sources
*   [tradingtechnologies](https://www.tradingtechnologies.com/help/x-study/technical-indicator-definitions/aroon-ar/)
*   [tradingview](https://www.tradingview.com/wiki/Aroon)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `scalar` | `float` | Scalar. Default: `100` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 3 columns |

chop[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.chop.chop "Permanent link")
--------------------------------------------------------------------------------------------

```
chop(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    atr_length: Int = None,
    ln: bool = None,
    scalar: IntFloat = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Choppiness Index

This indicator, by E.W. Dreiss, attempts to determine choppiness.

Sources
*   E.W. Dreiss an Australian Commodity Trader
*   [motivewave](https://www.motivewave.com/studies/choppiness_index.htm)
*   [tradingview](https://www.tradingview.com/scripts/choppinessindex/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `atr_length` | `int` | ATR period. Default: `1` | `None` |
| `ln` | `bool` | Use `ln` instead of `log10`. Default: `False` | `None` |
| `scalar` | `float` | Scalar. Default: `100` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
*   Choppy: `~ 100`
*   Trending: `~ 0`

* * *

cksp[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.cksp.cksp "Permanent link")
--------------------------------------------------------------------------------------------

```
cksp(
    high: Series,
    low: Series,
    close: Series,
    p: Int = None,
    x: IntFloat = None,
    q: Int = None,
    tvmode: bool = None,
    mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Chande Kroll Stop

This indicator, by Tushar Chande and Stanley Kroll, attempts to identify trends with long and short stops.

Sources
*   "The New Technical Trader", Wiley 1 st ed. ISBN 9780471597803, page 95
*   [multicharts](https://www.multicharts.com/discussion/viewtopic.php?t=48914)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `p` | `int` | ATR and first stop period; see Note. Default: `10` for both modes | `None` |
| `x` | `float` | ATR scalar; see Note. Default: `1` or `3` | `None` |
| `q` | `int` | Second stop period; see Note. Default: `9` or `20` | `None` |
| `tvmode` | `bool` | Trading View mode. Default: `True` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `None` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

Book vs TradingView Defaults
*   Book: `p=10, x=3, q=20, ma="sma"`
*   Trading View: `p=10, x=1, q=9, ma="rma"`

* * *

decay[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.decay.decay "Permanent link")
-----------------------------------------------------------------------------------------------

```
decay(
    close: Series,
    length: Int = None,
    mode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Decay

This function creates a decay moving forward from prior signals.

Sources
*   [tulipindicators](https://tulipindicators.org/decay)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `1` | `None` |
| `mode` | `str` | Either `"linear"` or `"exp"` (exponetional) Default: `"linear"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

decreasing[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.decreasing.decreasing "Permanent link")
--------------------------------------------------------------------------------------------------------------

```
decreasing(
    close: Series,
    length: Int = None,
    strict: bool = None,
    asint: bool = None,
    percent: IntFloat = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Decreasing

This indicator, by Kevin Johnson, attempts to identify decreasing periods.

Sources
*   Kevin Johnson

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `1` | `None` |
| `strict` | `bool` | Check if continuously increasing. Default: `False` | `None` |
| `percent` | `float` | Percent, i.e. `5.0`. Default: `None` | `None` |
| `asint` | `bool` | Returns as `Int`. Default: `True` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

dpo[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.dpo.dpo "Permanent link")
-----------------------------------------------------------------------------------------

```
dpo(
    close: Series,
    length: Int = None,
    centered: bool = True,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Detrend Price Oscillator

This indicator attempts to detrend (remove the trend) and identify cycles.

Sources
*   [fidelity](https://www.fidelity.com/learning-center/trading-investing/technical-analysis/technical-indicator-guide/dpo)
*   [stockcharts](http://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:detrended_price_osci)
*   [tradingview](https://www.tradingview.com/scripts/detrendedpriceoscillator/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `centered` | `bool` | Shift the dpo back by `int(0.5 * length) + 1`. Set to `False` to remove data leakage. Default: `True` | `True` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Possible Data Leak
Set `centered=False` to remove data leakage. See [Issue #60](https://github.com/twopirllc/pandas-ta/issues/60#).

* * *

ht_trendline[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.ht_trendline.ht_trendline "Permanent link")
--------------------------------------------------------------------------------------------------------------------

```
ht_trendline(
    close: Series,
    talib: bool = None,
    prenan: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Hilbert Transform TrendLine

This indicator uses the Hilbert Transform to smooth values.

Sources
*   John F Ehlers's "Rocket Science for Traders" Book
*   [mql5](https://c.mql5.com/forextsd/forum/59/023inst.pdf)
*   TA-Lib [ta_HT_TRENDLINE](https://github.com/TA-Lib/ta-lib/blob/main/src/ta_func/ta_HT_TRENDLINE.c)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series. | _required_ |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `prenan` | `int` | Prenans to apply. Ehlers's `6` or `12`, TALib `63` Default: `63` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Warning
TA-Lib Correlation: `np.float64(0.9979308363057683)`

Tip
Corrective contributions welcome!

increasing[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.increasing.increasing "Permanent link")
--------------------------------------------------------------------------------------------------------------

```
increasing(
    close: Series,
    length: Int = None,
    strict: bool = None,
    asint: bool = None,
    percent: IntFloat = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Increasing

This indicator, by Kevin Johnson, attempts to identify increasing periods.

Sources
*   Kevin Johnson

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `1` | `None` |
| `strict` | `bool` | Check if continuously increasing. Default: `False` | `None` |
| `percent` | `float` | Percent, i.e. `5.0`. Default: `None` | `None` |
| `asint` | `bool` | Returns as `Int`. Default: `True` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

long_run[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.long_run.long_run "Permanent link")
--------------------------------------------------------------------------------------------------------

```
long_run(
    fast: Series,
    slow: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Long Run

This indicator, by Kevin Johnson, attempts to identify long runs.

Sources
*   Kevin Johnson
*   [tradingview](https://www.tradingview.com/script/Z2mq63fE-Trade-Archer-Moving-Averages-v1-4F/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `fast` | `Series` | `fast` Series. | _required_ |
| `slow` | `Series` | `slow` Series. | _required_ |
| `length` | `int` | The `decreasing` and `increasing` period. Default: `2` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

psar[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.psar.psar "Permanent link")
--------------------------------------------------------------------------------------------

```
psar(
    high: Series,
    low: Series,
    close: Series = None,
    af0: IntFloat = None,
    af: IntFloat = None,
    max_af: IntFloat = None,
    tv=False,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Parabolic Stop and Reverse

This indicator, by J. Wells Wilder, attempts to identify trend direction and potential reversals.

Sources
*   [sierrachart](https://www.sierrachart.com/index.php?page=doc/StudiesReference.php&ID=66&Name=Parabolic)
*   [tradingview](https://www.tradingview.com/pine-script-reference/#fun_sar)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | Optional `close` Series | `None` |
| `af0` | `float` | Initial Acceleration Factor. Default: `0.02` | `None` |
| `af` | `float` | Acceleration Factor. Default: `0.02` | `None` |
| `max_af` | `float` | Maximum Acceleration Factor. Default: `0.2` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 4 columns |

Warning
TA-Lib Correlation: `np.float64(0.9837617513753181)`

Tip
Corrective contributions welcome!

* * *

qstick[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.qstick.qstick "Permanent link")
--------------------------------------------------------------------------------------------------

```
qstick(
    open_: Series,
    close: Series,
    length: Int = None,
    mamode: str = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Q Stick

This indicator, by Tushar Chande, attempts to quantify and identify trends.

Sources
*   [tradingtechnologies](https://library.tradingtechnologies.com/trade/chrt-ti-qstick.html)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `open_` | `Series` | `open` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `10` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"sma"` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

rwi[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.rwi.rwi "Permanent link")
-----------------------------------------------------------------------------------------

```
rwi(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    mamode: str = None,
    talib: bool = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Random Walk Index

This indicator attempts to identify the difference between a trend and a random walk.

Sources
*   [technicalindicators](https://www.technicalindicators.net/indicators-technical-analysis/168-rwi-random-walk-index)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `mamode` | `str` | See `help(ta.ma)`. Default: `"rma"` | `None` |
| `talib` | `bool` | If installed, use TA Lib. Default: `True` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

* * *

short_run[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.short_run.short_run "Permanent link")
-----------------------------------------------------------------------------------------------------------

```
short_run(
    fast: Series,
    slow: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Short Run

This indicator, by Kevin Johnson, attempts to identify short runs.

Sources
*   Kevin Johnson
*   [tradingview](https://www.tradingview.com/script/Z2mq63fE-Trade-Archer-Moving-Averages-v1-4F/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `fast` | `Series` | `fast` Series. | _required_ |
| `slow` | `Series` | `slow` Series. | _required_ |
| `length` | `int` | The `decreasing` and `increasing` period. Default: `2` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

trendflex[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.trendflex.trendflex "Permanent link")
-----------------------------------------------------------------------------------------------------------

```
trendflex(
    close: Series,
    length: Int = None,
    smooth: Int = None,
    alpha: IntFloat = None,
    pi: IntFloat = None,
    sqrt2: IntFloat = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Trendflex

This trend indicator, by John F. Ehlers, complements the "reflex" indicator.

Sources
*   [rengel8](https://github.com/rengel8) (2021-08-11) based on the implementation from "ProRealCode" (2021-08-11)
*   [prorealcode](https://www.prorealcode.com/prorealtime-indicators/reflex-and-trendflex-indicators-john-f-ehlers/)
*   [traders](http://traders.com/Documentation/FEEDbk_docs/2020/02/TradersTips.html)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `20` | `None` |
| `smooth` | `int` | Super Smoother period. Default: ```20```` | `None` |
| `alpha` | `float` | Alpha weight. Default: `0.04` | `None` |
| `pi` | `float` | Ehlers's truncated value: `3.14159`. Default: `3.14159` | `None` |
| `sqrt2` | `float` | Ehlers's truncated value: `1.414`. Default: `1.414` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

Note
John F. Ehlers introduced two indicators within the article "Reflex: A New Zero-Lag Indicator” in February 2020, TASC magazine. One of which is Reflex, a lag reduced cycle indicator. Both indicators (Reflex/Trendflex) are oscillators that complement each other with the focus for cycle and trend.

* * *

ttm_trend[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.ttm_trend.ttm_trend "Permanent link")
-----------------------------------------------------------------------------------------------------------

```
ttm_trend(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

TTM Trend

This indicator, by John Carter, labels bars green, `1`, or red `-1`, when above or below the average value.

Sources
*   John Carter, book “Mastering the Trade”
*   [prorealcode](https://www.prorealcode.com/prorealtime-indicators/ttm-trend-price/)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `6` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 1 column |

Tip
*   Two bars of the opposite color is the signal to get in or out.
*   Recommended to stay in trade if colors do not change.

vhf[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.vhf.vhf "Permanent link")
-----------------------------------------------------------------------------------------

```
vhf(
    close: Series,
    length: Int = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> Series
```

Vertical Horizontal Filter

This indicator, by Adam White, attempts to identify trending and ranging markets.

Sources
*   [incrediblecharts](https://www.incrediblecharts.com/indicators/vertical_horizontal_filter.php)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `28` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `Series` | 1 column |

* * *

vortex[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.vortex.vortex "Permanent link")
--------------------------------------------------------------------------------------------------

```
vortex(
    high: Series,
    low: Series,
    close: Series,
    length: Int = None,
    drift: Int = None,
    offset: Int = None,
    **kwargs: DictLike,
) -> DataFrame
```

Vortex

This indicator attempts to capture positive and negative trend movement using two oscillators.

Sources
*   [stockcharts](https://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:vortex_indicator)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series | _required_ |
| `length` | `int` | The period. Default: `14` | `None` |
| `drift` | `int` | Difference amount. Default: `1` | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

* * *

zigzag[#](https://www.pandas-ta.dev/api/trend/#src.pandas_ta.trend.zigzag.zigzag "Permanent link")
--------------------------------------------------------------------------------------------------

```
zigzag(
    high: Series,
    low: Series,
    close: Series = None,
    legs: int = None,
    deviation: IntFloat = None,
    backtest: bool = None,
    offset: Int = None,
    **kwargs: DictLike,
)
```

Zigzag

This indicator attempts to filter out smaller movements while identifying trend direction. It does not predict future trends, but it does identify swing highs and lows.

Sources
*   [stockcharts](https://school.stockcharts.com/doku.php?id=technical_indicators:zigzag)
*   [tradingview](https://www.tradingview.com/support/solutions/43000591664-zig-zag/#:~:text=Definition,trader%20visual%20the%20price%20action.)

Parameters:

| Name | Type | Description | Default |
| --- | --- | --- | --- |
| `high` | `Series` | `high` Series | _required_ |
| `low` | `Series` | `low` Series | _required_ |
| `close` | `Series` | `close` Series. Default: `None` | `None` |
| `legs` | `int` | Number of legs (> 2). Default: `10` | `None` |
| `deviation` | `float` | Reversal deviation percentage. Default: `5` | `None` |
| `backtest` | `bool` |  | `None` |
| `offset` | `int` | Post shift. Default: `0` | `None` |

Other Parameters:

| Name | Type | Description |
| --- | --- | --- |
| `fillna` | `value` | `pd.DataFrame.fillna(value)` |

Returns:

| Type | Description |
| --- | --- |
| `DataFrame` | 2 columns |

Deviation
When `deviation=10`, it shows movements greater than `10%`.

Backtest Mode
Ensures the DataFrame is safe for backtesting. By default, swing points are returned on the pivot index. Intermediate swings are not returned at all. This mode swing detection is placed on the bar that would have been detected. Furthermore, changes in swing levels are also included instead of only the final value.

*   Use the following formula to get the true index of a pivot: `p_i = i - int(floor(legs / 2))`

Warning
A Series reversal will create a new line.

