"""
TEST ULTRA-OPTIMIZED INTELLIGENT VECTORIZED BACKTESTER

This script tests the revolutionary batch-processing backtester that implements:
✅ Batch calculation of ALL sideways signals at once using vectorized HH, HL, LH, LL detection
✅ Ver4 exact logic preservation with massive speed improvements
✅ <PERSON><PERSON> Na<PERSON>ya Watson calculations until both 0.7h and 1.2h windows confirm sideways
✅ Single data fetch + intelligent chunk processing
✅ Expected 1000x+ performance improvement
"""

import os
import sys
import json
from datetime import datetime

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from intelligent_vectorized_backtester import IntelligentVectorizedBacktester

def main():
    """Test the ultra-optimized intelligent vectorized backtester"""
    print("🚀 ULTRA-OPTIMIZED INTELLIGENT VECTORIZED BACKTESTER")
    print("=" * 60)
    print("🧠 Revolutionary Batch Processing with Ver4 Exact Logic")
    print("⚡ Vectorized HH, HL, LH, LL Detection")
    print("🎯 Batch Sideways Calculation for ALL Minutes")
    print("=" * 60)
    
    # Test parameters
    ticker = "BATAINDIA"
    exchange = "NSE"
    start = "11:00"
    end = "12:31"
    date = "24-06-2025"  # Use a valid past trading day
    tokenid = "371"
    
    print(f"📊 Testing: {ticker} on {date}")
    print(f"⏰ Time: {start} to {end}")
    print(f"🔢 Token: {tokenid}")
    print()
    
    try:
        # Initialize backtester
        print("🔧 Initializing Ultra-Optimized Backtester...")
        backtester = IntelligentVectorizedBacktester(
            ticker=ticker,
            exchange=exchange,
            start=start,
            end=end,
            date=date,
            tokenid=tokenid
        )
        
        # Run the revolutionary batch analysis
        print("🚀 Starting Revolutionary Batch Signal Analysis...")
        print("📊 Calculating ALL sideways signals in batch...")
        print("🎯 Using Ver4 exact HH, HL, LH, LL logic...")
        results = backtester.single_vectorized_signal_generation()
        
        if results['success']:
            print("\n🎉 ULTRA-OPTIMIZED ANALYSIS COMPLETED!")
            print("=" * 50)
            print(f"⚡ Execution Time: {results['execution_time_seconds']:.2f} seconds")
            print(f"📡 API Calls Used: {results['api_calls_used']}")
            print(f"🚀 Performance Improvement: {results['performance_improvement_factor']:.1f}x faster")
            print(f"📊 API Reduction: {results['api_reduction_factor']:.1f}x fewer calls")
            print(f"🎯 Signals Generated: {results['signals_generated']}")
            print(f"📥 Positions Opened: {results['positions_opened']}")
            print(f"✅ Ver4 Logic Preserved: {results['ver4_logic_preserved']}")
            print(f"🧠 Batch Optimization: {results['intelligent_optimization']}")
            
            # Calculate expected vs actual performance
            total_minutes = int((datetime.strptime(f"{date} {end}", "%d-%m-%Y %H:%M") - 
                               datetime.strptime(f"{date} {start}", "%d-%m-%Y %H:%M")).total_seconds() / 60)
            expected_naive_time = total_minutes * 18  # 18 seconds per minute
            actual_time = results['execution_time_seconds']
            
            print(f"\n📈 PERFORMANCE ANALYSIS:")
            print(f"⏱️  Expected Naive Time: {expected_naive_time:.0f} seconds ({expected_naive_time/60:.1f} minutes)")
            print(f"⚡ Actual Batch Time: {actual_time:.2f} seconds")
            print(f"🚀 Speed Improvement: {expected_naive_time/actual_time:.0f}x faster!")
            
            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ultra_optimized_results_{ticker}_{date.replace('-', '')}_{timestamp}.json"
            
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            print(f"\n💾 Results saved to: {filename}")
            
            # Display signals
            if results['signals']:
                print("\n📊 SIGNALS GENERATED:")
                print("-" * 30)
                for signal in results['signals']:
                    print(f"⏰ {signal['time']} - {signal['signal_type']}: {signal['reason']}")
            else:
                print("\n📊 No signals generated during this period")
            
            # Display positions
            if results['positions']:
                print("\n📈 POSITION EVENTS:")
                print("-" * 30)
                for position in results['positions']:
                    if position['type'] == 'ENTRY':
                        print(f"📥 {position['time']} - ENTRY: {position['position_type']}")
                    else:
                        print(f"📤 {position['time']} - EXIT: {position.get('exit_reason', 'Unknown')}")
            else:
                print("\n📈 No positions opened during this period")
        
        else:
            print(f"❌ Analysis failed: {results.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error running ultra-optimized backtester: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
